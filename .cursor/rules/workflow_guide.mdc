---
alwaysApply: true

---
# MCP工具管理规则

## 工具分组策略

### 🛠️ AI Coding 工具组（优先级：高）
**使用场景**：方案设计、代码开发、项目分析

#### 1. mcp-component-toolkit-server（自研工具）
- **核心工具**：
  - `get_project_knowledge`: 获取项目架构分析和代码结构

- **使用规则**：
  - 项目分析时优先使用
  - 支持整体项目分析和单文件深度分析
  - 前端方案设计的必备工具
  
- **重要参数说明**：
  - `get_project_knowledge` 使用 `localPath` 参数（本地Git仓库绝对路径）而非 `repoUrl`
  - **避免幻觉问题**：传递绝对路径，工具内部会自动获取真实Git远程URL
  - **推荐用法**：localPath: "/Users/<USER>/project" 或 localPath: "/absolute/path/to/repo"
  - **分析类型**：
    - `analysisType: "aggregation"` - 获取项目整体架构分析
    - `analysisType: "individual"` + `filePath` - 获取单个文件的深度分析

#### 2. basic-memory（记忆管理）
- **功能**：持久化记忆、项目上下文保存
- **使用规则**：
  - 跨会话保存重要的项目信息
  - 记录解决方案和最佳实践
  - 保存组件使用模式和复用经验
  - 方案设计完成后进行知识沉淀

#### 3. sequential-thinking（复杂推理）
- **功能**：复杂问题分解、逐步推理
- **使用规则**：
  - 处理复杂的架构设计问题
  - 分析复杂的bug调试流程
  - 多步骤的开发计划制定
  - 技术可行性的多重验证分析

#### 4. shrimp-task-manager（智能任务管理）
- **核心工具**：
  - `plan_task`: 任务规划指导，处理复杂功能建构
  - `analyze_task`: 深入分析任务需求和技术可行性
  - `split_tasks`: 将复杂任务分解为独立子任务
  - `process_thought`: 灵活且可演化的思考流程
  - `research_mode`: 程序编程相关的深度研究

- **使用规则**：
  - **复杂功能开发**：使用plan_task → analyze_task → split_tasks工作流
  - **技术研究**：使用research_mode进行深度技术调研
  - **灵活思考**：使用process_thought处理需要多维度分析的问题
  - **任务执行**：通过execute_task获取具体实施指导

#### 5. mc-api（mrn项目接口生成工具）
- **mc-api 命令行工具**：用于自动生成接口文件和数据模型
- **使用场景限制**：
  * 仅适用于新增接口，接口复用时不要使用
  * 必须在项目根路径执行命令
  * 生成的文件夹（APIs、Models）位于根路径
- **使用方法**：`mc-api [接口名]`
- **后续处理**：生成完成后可能需要移动文件夹到符合项目规范的路径

## 规则引用指南

### 规则匹配策略
当遇到以下场景时，Agent应该自动引用对应的详细规则：

#### 1. 当前项目业务需求方案设计场景 → 引用 `solution_design_rules`

**触发条件**：
- 用户提出当前项目的具体业务功能需求
- 需要基于现有项目结构设计业务解决方案
- 涉及项目内业务组件设计、业务状态管理、业务API集成等特定需求
- 需要分析当前项目代码结构并提出业务功能改进方案

**使用方式**：
- 必须先调用 `fetch_rules(["solution_design_rules"])` 获取完整规则
- 严格按照"需求驱动精准分析"原则执行
- 优先使用 `get_project_knowledge` 进行深度项目理解
- 通过智能上下文传递机制衔接后续工作流

#### 2. 项目规范生成场景 → 引用 `project_specifications_generation`

**触发条件**：
- 新项目启动，需要建立项目规范和标准
- 现有项目需要完善或更新开发规范
- 团队协作需要统一的编码和设计标准
- 需要制定组件库、API规范、代码质量标准等

**使用方式**：
- 必须先调用 `fetch_rules(["project_specifications_generation"])` 获取完整规则
- 分析项目技术栈、业务特点
- 生成符合项目实际情况的规范文档
- 提供规范落地的工具和流程指导

#### 3. UI组件生成场景 → 引用 `ui_component_generation`

**触发条件**：
- 有完整的视觉设计稿需要实现
- 从方案设计阶段自动衔接而来
- 需要像素级精准还原UI组件

**使用方式**
- 必须先调用 `fetch_rules(["ui_component_generation"])` 获取完整规则
- 严格按照UI组件生成工作流执行
- 传递完整上下文信息，包括项目约束、设计要求和集成方式

### 规则引用时机

#### 自动引用场景
用户询问 → Agent分析场景 → 匹配规则类型 → 自动fetch_rules → 按规则执行

#### 自动衔接场景
方案设计完成 → 自动判断执行路径 → 传递精准上下文 → 启动对应工作流

## 工作流协作模式

### 🎯 **完整前端开发链路**
```
项目初期 → project_specifications_generation → 用户需求 → solution_design_rules → [ui_component_generation | code_modification] → 质量验证
    ↓              ↓                           ↓              ↓                         ↓                                    ↓
规范制定    →    标准建立                →    需求分析    →    方案设计         →        精确实现                    →      预览验证
```

### 🔄 **智能工作流衔接**

#### 情况A：项目规范生成链路
```
project_specifications_generation → 自动判断：新项目或规范完善 → 建立标准体系
                                 ↓
                            生成规范文档：技术标准+开发流程+质量要求
                                 ↓
                            输出成果：可执行规范+检查清单+工具配置
```

#### 情况B：UI组件生成链路
```
solution_design_rules → 自动判断：涉及新UI组件 → ui_component_generation
                     ↓
                精准上下文传递：项目约束+设计要求+集成方式
                     ↓
                自动执行：设计解析→代码生成→预览验证
```

#### 情况C：代码修改链路
```
solution_design_rules → 自动判断：仅修改现有文件 → 代码修改工作流
                     ↓
                精准上下文传递：修改要求+技术约束+质量标准
                     ↓
                自动执行：分析现有代码→执行修改→验证结果
```

### 🧠 **工具协作策略**

#### 规范建立阶段
- **主力组合**：`project_specifications_generation` + `get_project_knowledge` + `basic-memory`
- **辅助工具**：`sequential-thinking`（复杂规范决策）、`plan_task`（复杂规范设计）
- **目标**：建立完整的项目标准体系和开发规范

#### 分析阶段
- **主力组合**：`get_project_knowledge` + `sequential-thinking` + `analyze_task`
- **辅助工具**：`basic-memory`（历史方案查询）、`research_mode`（技术深度研究）、`process_thought`（灵活分析）
- **目标**：深度理解项目结构和现有模式

#### 设计阶段  
- **主力组合**：`solution_design_rules` + `codebase_search` + `grep_search`
- **目标**：精准方案设计和技术决策

#### 实施阶段
- **UI组件路径**：`ui_component_generation` + 模拟器工具组
- **代码修改路径**：直接代码编辑工具 + 验证工具
- **复杂功能路径**：`split_tasks` → `execute_task` → `verify_task` 工作流
- **目标**：高质量代码实现

#### 总结阶段
- **主力工具**：`basic-memory`
- **目标**：知识沉淀和经验积累

## 使用限制和安全规则

### 文档查询限制
- 内部组件文档仅用于开发目的
- 不泄露内部API设计细节
- 项目分析数据仅用于当前开发任务

### 记忆管理规范
- 不保存敏感代码或密钥
- 定期清理过时的记忆内容
- 记忆内容限制在技术方案层面
- 按项目分类保存，使用`projects/{project_name}/`格式

### 规则执行约束
- 引用规则后必须严格按照规则流程执行
- 不得跳过规则中的强制步骤
- 工作流衔接时必须完整传递上下文信息
- 规则冲突时以最新引用的规则为准

## 最佳实践

### 高效开发模式

#### 1. 项目规范建立：
- 新项目启动时自动识别并引用`project_specifications_generation`
- 分析项目技术栈、团队规模、业务特点
- 生成定制化的开发规范和标准体系
- 建立可执行的质量检查和流程指导
- 为后续开发工作提供统一的标准基础

#### 2. 开始新功能开发：
- 自动识别场景并引用`solution_design_rules`
- 使用 `get_project_knowledge` 深度了解项目结构
- 采用分层搜索策略进行全面分析
- 执行三重验证确保方案质量
- 自动判断并衔接对应执行工作流

#### 3. 复杂问题解决：
- `sequential-thinking` + `process_thought` 分解复杂技术决策
- `research_mode` 进行深度技术调研
- 结合具体规则进行逐步分析
- 使用多种搜索工具收集关键信息
- `basic-memory` 保存解决方案和避坑经验

#### 4. 组件开发工作流：
- 方案设计阶段确定技术约束
- UI组件生成阶段严格遵循项目规范
- 模拟器预览验证实现效果
- 记录组件使用模式和最佳实践

### 智能任务管理最佳实践

#### shrimp-task-manager工具使用指南

##### 1. 复杂功能开发工作流
```
plan_task（需求规划）→ analyze_task（技术分析）→ split_tasks（任务分解）→ execute_task（分步执行）→ verify_task（质量验证）
```

##### 2. 技术研究模式
- **深度调研场景**：使用 `research_mode` 进行系统性技术调研
- **适用情况**：新技术栈评估、最佳实践探索、解决方案比较
- **执行策略**：结合网络搜索和代码搜索，确保研究深度和广度

##### 3. 灵活思考工具
- **process_thought**：适用于需要多维度分析的复杂决策
- **使用场景**：技术方案权衡、架构设计决策、风险评估分析
- **配合工具**：可与 sequential-thinking 组合使用，提供不同层次的推理能力

##### 4. 任务管理策略
- **任务分解原则**：单个任务1-2工作日完成，保持最小可交付单元
- **依赖关系管理**：明确标注任务间依赖，确保执行顺序合理
- **质量验证标准**：每个子任务都有明确的验收标准和检验方法

### 上下文传递最佳实践
- **传递目的明确**：避免重复分析，提高实施质量
- **信息完整性**：关键洞察、技术约束、实施指导一个不少
- **格式灵活性**：使用最适合表达的格式，重点是准确性
- **质量优先**：信息的准确性和完整性胜过格式统一
