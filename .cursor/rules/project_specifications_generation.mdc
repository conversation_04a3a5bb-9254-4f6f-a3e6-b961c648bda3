---
ruleType: Agent Requested

description: Rules for generating project specifications.
globs:
---
你是一位经验丰富的代码架构师和规范制定专家。你的核心任务是深入分析一个指定的前端项目代码库，并基于实际观察到的、在项目中占主导地位的编码模式和约定，自动生成一份结构清晰、易于理解的**项目研发规范文档**，并将其封装为一个特定于当前IDE的项目规则文件。

**最终目标：**
1.  生成一份 Markdown 格式的**项目研发规范文档内容**，该内容能够准确反映项目当前的研发规范，特别是命名规则、代码风格、目录结构、Hooks使用、组件拆分和样式等方面。文档中应包含从项目中提取或合理推断出的具体代码示例或原则。
2.  将上述生成的规范文档内容，根据当前IDE类型，封装并保存为一个项目规则文件。

**核心分析原则（用于生成规范文档内容）：**
1.  **代码驱动，事实为先：** 所有规范和结论【必须】源自对项目现有代码的直接分析。优先记录项目中【实际存在并普遍遵循】的模式，而非引入外部的"理想"规范或个人偏好。
2.  **识别主导模式：** 当代码中存在多种风格或约定处理同一类事物时，应尝试识别并记录【最常用、最一致或最新近】的模式作为规范。如果多种模式并存且难以区分主导者，可以列出观察到的模式并注明其使用情况。
3.  **具体示例与原则并重：** 为每一条规范（尤其是命名规则）提供【一个】清晰、相关的代码示例。对于更抽象的规范（如组件拆分），则阐述观察到的原则和推荐做法。示例应优先从项目中【直接截取】，或在无法直接截取时，根据观察到的模式【合理构造】。
4.  **结构化输出：** 规范文档内容本身应使用 Markdown 进行组织，确保层级清晰，易于阅读。

**规范文档内容结构（请根据项目实际情况调整和扩展）：**

## 1. 命名规则 (Naming Conventions)

### 1.1. 组件命名 (Component Naming)
   - **规则描述：** [例如：组件文件和组件名均采用 PascalCase。对于特定模块或业务域下的组件，可添加模块/域作为前缀。]
   - **示例：**
     ```javascript
     // 例：src/components/User/UserProfileCard.vue
     export default {
       name: 'UserProfileCard',
       // ...
     }
     ```

### 1.2. API (函数/方法) 命名 (API/Function/Method Naming)
   - **规则描述：** [例如：API 请求函数、服务层方法、工具函数等采用 camelCase。通常遵循 动词+名词 的结构。]
   - **示例：**
     ```typescript
     // 例：src/api/user.ts
     async function getUserById(userId: string) { /* ... */ }
     ```

### 1.3. 变量与常量命名 (Variable & Constant Naming)
   - **变量规则描述：** [例如：普通变量采用 camelCase。]
   - **变量示例：** `let isLoading = false;`
   - **常量规则描述：** [例如：全局常量、枚举成员等采用 UPPER_SNAKE_CASE。]
   - **常量示例：** `const MAX_API_RETRIES = 3;`

### 1.4. 文件及目录命名 (File & Directory Naming)
   - **规则描述：**
     -   **视图/页面 (Views/Pages):** [例如：目录使用复数形式 `views/users/`，页面组件文件使用 PascalCase `UserListPage.vue`。]
     -   **通用组件 (Components):** [例如：目录可按功能或模块组织 `components/common/buttons/`，组件文件使用 PascalCase。]
     -   **服务/API (Services/APIs):** [例如：`src/api/`，文件名使用 camelCase描述其功能，如 `userService.ts`。]
     -   **状态管理 (Store):** [例如：`src/store/modules/`，模块文件使用 camelCase 加 `Store` 后缀，如 `userStore.ts`。]
     -   **工具库 (Utils):** [例如：`src/utils/`，文件名描述其功能，如 `dateFormatter.ts`。]
     -   **类型定义 (Types):** [例如：`src/types/`，文件名使用 PascalCase 描述实体，如 `User.ts`。]
     -   **Hooks (自定义组合式函数):** [例如：存放于 `src/hooks/`，文件名使用 camelCase 并以 `use` 开头，如 `useFormValidation.ts`。]
   - **综合示例 (目录结构)：**
     ```
     src/
     ├── api/
     │   └── userService.ts
     ├── components/
     │   └── common/
     │       └── AppButton.vue
     ├── hooks/
     │   └── useWindowResize.ts
     ├── store/
     │   └── modules/
     │       └── cartStore.ts
     ├── utils/
     │   └── stringHelpers.ts
     ├── views/
     │   └── HomePage.vue
     └── types/
         └── Product.d.ts
     ```

### 1.5. 状态管理Store命名 (Store Naming - e.g., Vuex, Pinia, Redux)
   - **模块 (Module) 规则：** [例如：模块名（文件名或注册名）使用 camelCase，并可能加上 `Store` 或 `Module` 后缀。]
   - **State 规则：** [例如：State 属性使用 camelCase。]
   - **Getters 规则：** [例如：Getters 使用 camelCase，通常是名词或描述性短语。]
   - **Actions 规则：** [例如：Actions 使用 camelCase，通常是动词+名词结构，表示意图。]
   - **Mutations (Vuex) / Reducer Cases (Redux) 规则：** [例如：Mutations (Vuex) 使用过去式动词或事件描述，采用 UPPER_SNAKE_CASE 或 camelCase。]
   - **示例 (Pinia风格)：**
     ```typescript
     // store/userStore.ts
     export const useUserStore = defineStore('user', {
       state: () => ({ currentUser: null, isAuthenticated: false }),
       getters: { isAdmin: (state) => state.currentUser?.role === 'admin' },
       actions: { async loginUser(credentials) { /* ... */ } },
     });
     ```

## 2. Hooks 使用规范 (若项目中使用 Hooks, e.g., React Hooks, Vue Composition API)

### 2.1. 自定义 Hook 命名与定义
   - **命名规则：** [例如：自定义 Hook (组合式函数) 必须以 `use` 开头，采用 camelCase (如 `useFeatureName`)。文件名也应遵循此模式。]
   - **单一职责：** [例如：每个自定义 Hook 应聚焦于单一的、可复用的逻辑点（如表单处理、窗口事件监听）。]
   - **返回结构：** [例如：倾向于返回一个对象，包含状态和操作方法。]
   - **目录存放：** [例如：通用的自定义 Hooks 存放于 `src/hooks/`。]
   - **示例：**
     ```typescript
     // src/hooks/useFormInput.ts
     export function useFormInput(initialValue: string) {
       const value = ref(initialValue);
       const onChange = (event: Event) => { /* ... */ };
       return { value, onChange };
     }
     ```

### 2.2. Hooks 使用规则
   - **顶层调用：** [例如：Hooks 只能在函数组件的顶层或自定义 Hook 的顶层调用。禁止在循环、条件语句或嵌套函数中调用 Hooks。]
   - **依赖管理 (useEffect, useMemo, useCallback)：** [例如：明确并正确地声明依赖项数组，避免不必要的重渲染或陈旧闭包。]

## 3. 组件拆分规范 (Component Splitting Guidelines)

### 3.1. 拆分原则
   - **单一职责原则 (SRP)：** [例如：一个组件应只关注UI的某个特定部分或单一功能。]
   - **可复用性：** [例如：如果一部分UI或逻辑在多处被使用，应将其提取为独立的可复用组件。]
   - **代码行数/复杂度：** [例如：一个组件文件过大（如超过300行）或逻辑难以理解时，通常是拆分的信号。]

### 3.2. 常见拆分模式
   - **展示组件与容器组件：** [例如：容器组件负责数据和逻辑，展示组件负责UI渲染。]
   - **按功能领域拆分：** [例如：复杂表单可拆分为多个子组件（如用户信息区、地址区）。]
   - **列表与列表项：** [例如：列表项（`ListItem`）拆分为独立组件。]

### 3.3. 组件通信
   - **Props Down, Events Up：** [例如：父子组件通信主要通过属性向下传递，通过自定义事件向上传递。]
   - **状态管理：** [例如：复杂状态共享使用项目统一的状态管理方案（如 Pinia）。]
   - **依赖注入 (Provide/Inject)：** [例如：深层嵌套组件间数据共享可使用 Provide/Inject。]

## 4. 样式规范 (Styling Conventions)

### 4.1. CSS/SCSS/Less 编写风格
   - **CSS类名命名：** [例如：推荐使用 BEM (Block__Element--Modifier) 规范（如 `.card__button--primary`），或使用 kebab-case 结合项目/模块前缀（如 `mh-form-group`）。]
   - **选择器嵌套深度：** [例如：建议 SCSS/Less 嵌套层级不超过 3 层。]
   - **样式的组织与作用域：**
     -   [例如：优先使用组件作用域样式（如 Vue SFC 的 `<style scoped>`）。]
     -   [例如：全局样式严格控制，仅用于基础布局、主题变量等。]
   - **变量使用：** [例如：颜色、字体等通过 CSS 自定义属性（`--primary-color: #007bff;`）或 SCSS/Less 变量（`$primary-color: #007bff;`）管理。选择一种方式并统一。]
   - **工具类 (Utility Classes)：** [例如：项目中是否使用原子化CSS框架或定义了自定义的工具类（如 `.mt-1`）。]

### 4.2. 样式文件组织
   - **组件内样式：** [例如：简单的组件样式直接写在组件文件内部。]
   - **组件外样式文件：** [例如：复杂组件样式可抽离到与组件同目录的 `.scss` 文件中。]
   - **全局样式目录：** [例如：`src/assets/styles/` 用于存放全局样式、主题、变量等。]

## 5. 代码风格与格式 (Code Style & Formatting)
   - **缩进：** [例如：使用 2 个空格进行缩进。]
   - **引号：** [例如：JavaScript/TypeScript 中优先使用单引号。]
   - **分号：** [例如：语句末尾必须添加分号。]
   - **对象/数组格式化：** [例如：多行对象/数组的最后一个元素后建议添加逗号。]
   - **最大行长度：** [例如：建议不超过 100 字符（通过Prettier等工具强制）。]

## 6. 注释规范 (Commenting Conventions)
   - **模块/类/函数注释：** [例如：公共模块、类、方法推荐使用 JSDoc/TSDoc 风格的块注释说明其用途、参数和返回值。]
     ```typescript
     /**
      * Fetches patient data from the API.
      * @param patientId The ID of the patient to fetch.
      * @returns A promise that resolves with the patient data.
      */
     async function getPatientData(patientId: string): Promise<Patient> { /* ... */ }
     ```
   - **行内注释：** [例如：对于非显而易见的逻辑判断或特定处理步骤，使用 `//` 行内注释进行解释。]
   - **TODO/FIXME 等标签使用：** [例如：使用 `// TODO: [描述待办事项]` 和 `// FIXME: [描述待修复的问题]`。]

## 7. 其他重要约定 (Other Important Conventions)
   *(根据项目分析，可能包含目录结构深层逻辑、特定技术栈的最佳实践、测试文件命名与组织、错误处理策略、国际化方案等)*

---
**任务执行流程与输出：**

1.  **获取当前IDE类型：** AI 自行判断当前是在 Cursor 环境还是 CatPaw 环境下执行。
2.  **分析代码库：** AI 使用可用的工具（如 MCP 工具集）对指定的项目代码库进行全面分析，以提取上述各节规范内容。
3.  **生成规范内容：** 基于分析结果，填充上述 Markdown 结构，形成完整的项目研发规范文档内容。
4.  **创建/更新特定IDE的项目规则文件：**
    *   **确定文件路径和元数据：**
        *   **如果IDE是 "Cursor"：**
            *   文件路径为项目根目录下的 `.cursor/rules/project_specifications.mdc`。
            *   文件开头的元数据块为：
                ```
                ---
                ruleType: Optional types are Always, Auto Attached, and Agent Requested
                description: Project-specific development specifications and coding conventions, automatically generated based on codebase analysis.
                globs: Only needed in Auto Attached mode, specify the file extensions to match, such as *.vue,*.ts
                ---
                ```
        *   **如果IDE是 "CatPaw"：**
            *   文件路径为项目根目录下的 `.catpaw/rules/project_specifications.md`。
            *   文件开头的元数据块为：
                ```
                ---
                ruleType: Optional types are Always, Auto Attached, and Model Request
                description: Project-specific development specifications and coding conventions, automatically generated based on codebase analysis.
                globs: Only needed in Auto Attached mode, specify the file extensions to match, such as *.vue,*.ts
                ---
                ```
    *   **写入文件：** 创建或覆盖指定路径的文件。文件内容由上述确定的元数据块和紧随其后的、在步骤3中生成的**完整项目研发规范文档内容** (从 `## 1. 命名规则 (Naming Conventions)` 开始) 组成。
5.  **回复用户：** 完成文件生成/更新后，根据当前IDE类型，回复以下确切信息给用户：
    *   **如果IDE是 "Cursor"：**
        `已经生成完成，请检查 .cursor/rules/project_specifications.mdc，有什么需要修改吗？`
    *   **如果IDE是 "CatPaw"：**
        `已经生成完成，请检查 .catpaw/rules/project_specifications.md，有什么需要修改吗？`
6.  **处理修改请求：** 如果用户在后续交互中提出对规范内容的修改请求，AI 应直接修改对应IDE的规则文件（`.cursor/rules/project_specifications.mdc` 或 `.catpaw/rules/project_specifications.md`）中的规范内容部分（元数据块通常不需要修改，除非明确指出）。
**特别注意：** 元数据块（`--- ... ---`）在创建后可能会被IDE（尤其是Cursor）以特定方式渲染或轻微调整其文本表示。AI 在后续修改文件时，**不应** 尝试"修正"这些由IDE引入的元数据块的表示差异，只需聚焦于其后的 Markdown 内容的修改。元数据块的语义内容（如 `ruleType`, `description`）仅在用户明确指示时才进行修改。

**请开始对目标项目进行分析，并依据上述结构、原则和流程执行任务。**