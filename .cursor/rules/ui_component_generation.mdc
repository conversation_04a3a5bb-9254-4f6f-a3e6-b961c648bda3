---
ruleType: Agent Requested

description: Rules for component generation with design data
globs:
---
# UI组件生成工作流
<ui_component_development_workflow>
本工作流程将UI设计稿转换为功能完整的React Native组件，分为五个递进阶段：

## 工作流程五阶段
1. **设计数据解析** - 分析图层数据，生成树形结构，参考<design_data_parsing>
2. **组件和API匹配** - 匹配公共组件和API，参考<component_matching>，**在此阶段必须暂停工作流程，等待用户明确选择使用哪个组件库（美团栗子设计组件库/频道页组件/原生React Native组件）**。在得到用户明确回应前，禁止继续后续步骤。
3. **UI组件结构** - 基于选定的组件库，转换为组件层级结构，同时处理交互逻辑和数据绑定，参考<ui_description_generation>
4. **代码生成** - 生成最终代码，参考<code_implementation>
5. **代码质量检查** - 对生成的代码进行质量检查，确保符合开发标准，参考<code_quality_review>
+> 如需项目运行与调优，请参考独立模板<project_run_debug_workflow>

## 输入文件
**使用 `read_file` 工具读取以下文件**：
1. 设计数据：tmp/extracted-data.json
2. 设计稿图片：tmp/component.png
3. 组件交互描述：tmp/description.md
4. 接口Mock数据：tmp/api-mock.json

## 执行方式
- 每阶段在前一阶段完成后执行，除代码生成外各阶段使用JSON格式输出结果，不要生成文件
- **第二阶段必须明确询问用户选择组件库并等待回复，在用户未明确选择组件库之前，不得进入第三阶段**
- **第四阶段代码生成前必须使用 `query_component_docs` 获取所有需要的API文档**
</ui_component_development_workflow>

<design_data_parsing>
# 任务
根据提供的UI设计稿图片和相应的平铺数据，提取有效图层并合并相似图层，确保图片中所有视觉元素都被正确识别和结构化，以树形结构表示元素的层级关系，同时保留所有原有CSS属性，并补充现代布局方式（margin、padding、flex等）以替代绝对坐标。

# 前置条件检查
1. **设计稿图片验证**：
   - 必须先检查设计稿图片是否存在
   - 如未找到图片，立即中止并提示用户："请提供设计稿图片，此步骤无法继续"
   - 验证图片格式是否支持(PNG/JPG/WEBP等)
   - 确认图片分辨率足够用于分析

2. **印迹数据处理**（如有设计材料）：
   - 使用 `process_ingee_file` 处理印迹设计数据
   - 根据需求内容自行判断 processType：
     * `component` - 单个组件图片
     * `page` - 整页面图片
     * `both` - 单个组件的图片及其在页面中的展示图片（默认）
   - 获取完整的设计、交互和数据要求

# 输入
1. UI设计稿图片（component.png）
2. 包含图层信息的平铺数据（extracted-data.json）

# 步骤
1. **分析图片**：
   - 识别所有独立的视觉元素，包括文本、图标、背景等
   - 标记元素的精确位置、尺寸和样式信息
   - 确定元素的层级关系和嵌套情况

2. **对比平铺数据**：
   - 将识别出的视觉元素与图层数据进行匹配
   - 标记图层数据中缺失的元素和与元素无关的图层
   - 标记需要拆分的复合图层（如包含多个独立元素的图层）

3. **处理图层数据**：
   - 为图层数据中缺失的视觉元素创建新的图层
   - 过滤掉图层数据中无关的图层，对相似图层保留一个代表图层
   - 将复合图层拆分为独立的子图层

4. **保留有效信息**：
   - 保留有效的图层信息，包含名称、CSS、图片url等关键信息
   - 确保没有遗漏图层中较小的元素，例如icon、分割线等

5. **推断绝对定位元素**：
   - 仅在以下情况使用绝对定位：
     * 元素需要脱离文档流（如悬浮标签、角标）
     * 元素需要精确定位在特定位置且无法通过Flex实现
     * 元素需要叠加在其他元素之上
   - 对于使用绝对定位的元素，确保其父容器设置position: relative
   - 计算元素相对于父容器的偏移量，设置top、right、bottom、left属性

6. **布局属性转换**：
   - **保留所有原有CSS属性**
   - 补充flex布局实现元素的排列和对齐
   - 添加margin设置元素间距
   - 添加padding设置内部元素与容器边界的距离
   - 保留必要的position属性（如弹窗、悬浮元素等）
   - **移动端自适应处理**：设计稿375px基准适配320px-428px移动设备，使用相对单位或flex布局

7. **构建层级树结构**：
   - 根据元素的包含关系，确定父子节点
   - 对于重叠元素，根据视觉层级确定前后关系
   - 确保树形结构准确反映UI的视觉和逻辑层级

8. **输出结果**：
   - 使用嵌套的JSON格式表示有效图层的树形结构
   - 每个节点包含名称、CSS、图片url等关键信息（不含坐标）
   - 使用children数组表示子元素

9. **比对验证**：
   - 将得到的JSON结构与原始设计图进行比对
   - 确认所有视觉元素都已被正确识别和结构化
   - 检查元素的尺寸、位置和样式是否与设计图完全一致
   - 验证元素的层级关系和嵌套结构是否准确反映设计意图
   - 确保不遗漏任何细节（如小图标、分割线、文字等）
   - 如发现不一致，返回调整相应步骤直至完全匹配

# 注意事项：
- 保留所有原有CSS属性
- 优先使用flex布局，但在必要时保留绝对定位
- 对于需要相对于某个元素定位的组件，确保正确设置父容器的position: relative属性
- 当判断某个元素不是常见的字体或者是某种图标时，直接认为这个元素是一张icon图片，无需再做额外处理
- **仅返回JSON**，不要输出多余内容
- **[MOST IMPORTANT]确保没有遗漏图片中任何元素‼️确保没有遗漏图片中任何元素‼️确保没有遗漏图片中任何元素‼️**

# 输出示例：
```json
{
  "name": "卡片容器",
  "rect": {
    "width": 355,
    "height": 145.5
  },
  "css": [
    "border-radius: 8px;", 
    "background: #FFFFFF;", 
    "display: flex;", 
    "padding: 13px 10px;",
    "position: relative;"
  ],
  "children": [
    {
      "name": "剧照容器",
      "rect": {
        "width": 80,
        "height": 80
      },
      "css": [
        "border-radius: 6px;", 
        "position: relative;",
        "margin-right: 8px;"
      ],
      "children": [
        {
          "name": "剧照图片",
          "rect": {
            "width": 80,
            "height": 80
          },
          "css": ["border-radius: 6px;"],
          "image_url": "https://dummyimage.com/80x80/666/fff.png"
        },
        {
          "name": "热点标签",
          "rect": {
            "width": 80,
            "height": 18
          },
          "css": [
            "background: linear-gradient(284deg, #FF4B10 0%, #FF7700 100%);", 
            "position: absolute;", 
            "bottom: 0;", 
            "left: 0;",
            "display: flex;",
            "justify-content: center;",
            "align-items: center;"
          ],
          "children": [
            {
              "name": "热点文本",
              "rect": {
                "width": 48,
                "height": 12
              },
              "css": [
                "color: #FFFFFF;", 
                "font-family: PingFang SC;", 
                "font-weight: 400;", 
                "font-size: 12px;", 
                "line-height: 14px;", 
                "text-align: center;"
              ]
            }
          ]
        }
      ]
    }
  ]
}
```
</design_data_parsing>

<ui_description_generation>
# 任务
作为专业的React Native UI工程师，基于已选定的组件库，将设计稿的布局样式数据转换为实际开发可用的组件结构描述，同时处理交互逻辑和数据绑定。确保组件结构完整且符合开发最佳实践。

# [MOST IMPORTANT] 转换原则
- **基于选定组件库**：使用前一阶段确定的具体组件库和组件进行结构设计
- **以设计稿图片为参考确保元素完整性**：确保没有遗漏设计稿中的任何视觉元素
- **以布局样式数据为准确定实现方式**：不得更改元素的实现类型（如图层中的图片不能转换为文字）
- **同时处理交互和数据**：在构建组件结构的同时，整合交互逻辑和数据绑定需求

# 输入
1. UI设计稿图片
2. 布局样式数据（第一阶段生成的）
3. 组件和API匹配结果（第二阶段生成的）
4. 组件交互描述（tmp/description.md）
5. 接口Mock数据（tmp/api-mock.json）

# 转换步骤
1. **分析设计稿图片**：
   - 识别图片中所有UI元素
   - 标记元素间的层级关系
   - 确定各元素的视觉特征

2. **对比布局数据**：
   - 将图片中识别的元素与布局数据进行匹配
   - 添加布局数据中缺失的元素
   - 确保没有遗漏图片中较小的元素

3. **分析交互需求**：
   - 识别交互需求中明确提及的交互行为
   - 将已识别的交互行为与UI元素精确匹配
   - 仅提取明确要求的交互的触发条件、所需数据和处理逻辑

4. **处理数据绑定**：
   - 从API数据中选择必要的字段
   - 分别选择视觉和交互必要的数据字段进行映射
   - 定义条件渲染规则和数据绑定映射

5. **匹配组件类型**：
   - 根据元素类型匹配组件类型
   - 保持布局数据中指定的实现方式，不改变元素本质
   - 考虑交互需求选择合适的组件（如TouchableOpacity用于可点击元素）

6. **构建组件结构**：
   - 建立组件间的层级关系
   - 移除不必要的嵌套层级
   - 整合交互事件处理器和数据绑定

7. **转换样式属性**：
   - 将CSS样式转换为React Native样式对象
   - 省略单位，使用数值
   - 将CSS命名转换为驼峰式
   - 统一颜色值格式（十六进制）
   - 处理平台差异性样式
   - **移动端适配转换**：设计稿固定尺寸转换为flex布局，确保320px-428px设备正常显示

8. **处理图片资源**：
   - 为Image组件配置source={{ uri: 'imageUrl' }}
   - 若存在image_url属性，拼接'https://p0.meituan.net'前缀

9. **对比验证**：
   - 将生成的组件结构与设计稿进行对比
   - 确认所有UI元素都已正确转换为组件
   - 验证元素的实现方式是否与布局数据一致
   - 验证交互逻辑和数据绑定的正确性
   - 确保最终输出能准确还原设计稿的视觉效果

# 输出示例
```json
{
  "moduleName": "热点卡片组件",
  "componentName": "TouchableOpacity",
  "props": {
    "source": {
      "title": "data?.title || ''",
      "imageUrl": "data?.images?.[0]?.url || ''",
      "searchItems": "data?.searchKeywords?.map(item => ({ text: item.keyword, url: item.searchUrl })) || []",
      "jumpUrl": "data?.linkUrl || ''",
      "trackInfo": {
        "itemId": "data?.id || ''",
        "title": "data?.title || ''",
        "category": "data?.category || ''"
      }
    },
    "style": {
      "paddingHorizontal": 10,
      "paddingVertical": 13,
      "flexDirection": "row",
      "backgroundColor": "#FFFFFF",
      "borderRadius": 12
    }
  },
  "events": {
    "onPress": {
      "action": "openUrl",
      "params": "props.source.jumpUrl",
      "condition": "Boolean(props.source.jumpUrl)",
      "track": {
        "channel": "gc",
        "bid": "b_gc_hotspot_mc",
        "params": {
          "extra_info": "props.source.trackInfo"
        }
      }
    },
    "onExposure": {
      "action": "exposure",
      "track": {
        "channel": "gc",
        "bid": "b_gc_hotspot_mv",
        "params": {
          "extra_info": "props.source.trackInfo"
        }
      }
    }
  },
  "children": [
    {
      "moduleName": "左侧图片区域",
      "componentName": "View",
      "props": {
        "style": {
          "position": "relative",
          "width": 80,
          "height": 80,
          "borderRadius": 6,
          "overflow": "hidden"
        }
      },
      "children": [
        {
          "moduleName": "剧照图片",
          "componentName": "Image",
          "condition": "Boolean(props.source.imageUrl)",
          "props": {
            "source": {
              "uri": "props.source.imageUrl"
            },
            "style": {
              "width": "100%",
              "height": "100%"
            }
          }
        }
      ]
    },
    {
      "moduleName": "右侧内容区域",
      "componentName": "View",
      "props": {
        "style": {
          "flex": 1,
          "marginLeft": 9
        }
      },
      "children": [
        {
          "moduleName": "标题",
          "componentName": "Text",
          "props": {
            "source": {
              "text": "props.source.title"
            },
            "style": {
              "fontSize": 15.5,
              "color": "#222222",
              "fontWeight": 500,
              "fontFamily": "PingFang SC"
            },
            "ellipsizeMode": "IS_ANDROID ? 'tail' : 'wordWrapping'",
            "numberOfLines": "IS_ANDROID ? 2 : 1"
          }
        },
        {
          "moduleName": "搜索标签区域",
          "componentName": "GCScrollView",
          "condition": "props.source.searchItems?.length > 0",
          "props": {
            "horizontal": true,
            "showsHorizontalScrollIndicator": false,
            "source": {
              "items": "props.source.searchItems"
            },
            "style": {
              "marginTop": 9
            }
          },
          "events": {
            "onItemPress": {
              "action": "openUrl",
              "params": "item.url",
              "track": {
                "channel": "gc",
                "bid": "b_gc_search_mc",
                "params": {
                  "extra_info": {
                    "keyword": "item.text",
                    "parent_info": "props.source.trackInfo"
                  }
                }
              }
            }
          }
        }
      ]
    }
  ]
}
```
# 输出字段说明：
- `moduleName`：组件模块名称，用于描述组件用途
- `componentName`：组件类型名称（如View、Text、Image、TouchableOpacity）
- `props`：组件属性对象
  - `source`：数据映射对象，包含从API数据到组件属性的映射关系
  - `style`：样式对象，使用React Native样式规范
  - 其他组件特定属性
- `events`：事件处理配置对象（可选）
  - `onPress`、`onExposure`、`onItemPress`等事件
  - 每个事件包含action、params、condition、track等配置
- `condition`：条件渲染表达式（可选）
  - 使用Boolean()函数确保返回布尔值
  - 引用props.source中的数据字段
- `children`：子组件数组，每个子组件具有相同的字段结构

# [MOST IMPORTANT] 输出规范
- **仅返回JSON结构**，采用统一的组件描述格式
- **确保元素完整**：不遗漏设计稿中的任何元素
- **正确使用组件**：根据组件信息正确选择组件类型
- **数据映射统一**：使用props.source统一管理数据映射
- **事件配置完整**：包含action、params、condition、track等完整配置
- **条件渲染清晰**：使用condition字段，确保Boolean()包装
- 包含完整的组件层级关系
- 添加有意义的moduleName
- 确保所有数据路径从root开始，使用完整路径表达
</ui_description_generation>

<component_matching>
# 任务
根据用户偏好，匹配合适的组件和工具函数，确保理解其API使用方式。必须让用户明确选择组件库，且在代码生成前必须查询所有相关API文档。

# 执行流程

## 第零阶段：用户偏好确认（必须等待用户选择）
- **!!!必须停下流程，等待用户明确选择!!!**
- 明确向用户提问："请从以下选项中选择一个组件库："
  1. 栗子组件
  2. 频道页组件
  3. MRN原生组件
- **!!!在用户明确回答之前，禁止继续后续步骤!!!**

## 第一阶段：需求分析
- 列出视觉元素、交互需求和特殊要求
- 列出所需交互功能和工具函数

## 第二阶段：API匹配
1. 组件匹配：选择合适的UI组件
   - 基于需求分析进行组件决策，栗子设计组件库参考 `<max_leez_components>`，MRN原生组件参考 `<mrn_components>`
   - 从组件列表描述中确定几个可能匹配的候选组件
   - 筛选标准:
     * 组件功能与视觉元素的匹配度
     * 组件支持的交互方式与需求的匹配度
     * 组件的通用性和扩展性
2. 工具函数匹配：基于 `<util_api_doc>` 选择必要的工具函数

## 第三阶段：文档查询（强制必须执行）
- **必须使用`query_component_docs`或`mcp__query_component_docs`获取所有需要的API文档**
- 分析文档：了解参数、返回值和使用限制
- 使用示例：`query_component_docs(['Avatar', 'openUrl'])`
- **禁止在没有查询文档的情况下进行代码生成**

## 第四阶段：最终决策
- **只有当完全满足**组件的所有使用条件时，才选择该组件；若有任何不符，应选择更基础的组件。
- 检查是否选择了功能重复的组件，当多个组件可满足同一需求时，遵循优先级：业务组件 > 基础组件
- 确认最终选择的组件和工具函数
- 记录关键参数和使用注意事项

# 输出格式
```json
{
  "userPreference": {
    "componentLibrary": "选择的组件库"
  },
  "apiSelection": {
    "components": [
      {
        "name": "组件名",
        "reason": "选取理由",
        "example": "导入路径和使用示例"
      }
      // ... 其他组件
    ],
    "utilityFunctions": []
  }
}
```

# 关键规则
1. **!!!强制停下等待用户选择!!!**：必须停下流程，明确提问，等待用户明确选择组件库后才能继续
2. **文档查询强制执行**：所有选用的API必须查阅文档，禁止跳过此步骤
3. **工具函数匹配必需**：无论组件路径如何，都需匹配工具函数
4. **禁止代码生成绕过**：在完成API文档查询前，禁止进入代码生成阶段

# 检查清单
- [ ] 是否已明确让用户选择组件库并等待回答？
- [ ] 是否已查询并分析所有工具函数API？
- [ ] (非原生路径)是否已查询并分析所有组件API？
</component_matching>

<code_implementation>
# 任务
作为React Native开发专家，根据提供的组件描述JSON结构，使用React Native和TypeScript构建组件。

## 输入
1. 组件描述（之前生成的基础组件代码）
2. 组件文档查询结果
3. 工具函数查询结果

## 前置条件检查
- **检查是否存在所有需要的API文档**
- 如果没有，**使用`query_component_docs`或`mcp__query_component_docs`获取所有需要的API文档**
- 使用示例：`query_component_docs(['Avatar', 'openUrl'])`

## 接口文件生成工具（可选）
- **mc-api 命令行工具**：用于自动生成接口文件和数据模型
- **使用场景限制**：
  * 仅适用于新增接口，接口复用时不要使用
  * 必须在项目根路径执行命令
  * 生成的文件夹（APIs、Models）位于根路径
- **使用方法**：`mc-api [接口名]`
- **后续处理**：生成完成后可能需要移动文件夹到符合项目规范的路径

## 实现步骤
1. **数据处理设置**：
```typescript
// 统一使用 useMemo 处理数据字段，dataFields使用领域模型相关的名称
const dataFields = useMemo(() => ({
  title: data?.title || '',
  imageUrl: data?.images?.[0]?.url || '',
  description: data?.description || '',
  // ... 其他数据字段
}), [data]);
```

2. **组件实现**：
   - 使用确认后的组件库中的组件替换原有基础组件
   - 保留原有样式和布局属性
   - 实现交互逻辑

3. **代码优化**：
   - 确保TypeScript类型安全
   - 遵循React Native最佳实践
   - 保持代码清晰易读

4. **代码检查**：
   - 检查所有样式是否符合RN规范
   - 检查所有条件渲染逻辑
   - 检查所有回调函数依赖项
   - 检查所有数据处理逻辑
   **只有在通过以上所有检查项后，才进行代码生成**

# 开发规范
1. **布局规范**：
   - 严格使用Flexbox布局，仅在必要时使用`position: 'absolute'`
   - 组件宽度采用自适应方式，避免硬编码固定值
   - 合理使用flexDirection、justifyContent和alignItems
   - **移动端自适应**：设计稿375px基准适配主流移动设备尺寸，可使用Dimensions API或@mrn/mrn-utils工具函数等进行屏幕适配

2. **数据处理优化**：
   - 使用useMemo处理数据转换
   - 避免组件内重复计算
   ```typescript
   // 示例：复杂数据处理
   const processedData = useMemo(() => {
     return dataFields.items?.map(item => ({
       ...item,
       isValid: Boolean(item.value)
     })) || [];
   }, [dataFields.items]);
   ```

3. **回调函数优化**：
   ```typescript
   const handlePress = useCallback(() => {
     if (dataFields.isClickable) {
       // 处理点击
     }
   }, [dataFields.isClickable]);
   ```

4. **MRN项目屏幕适配**：
   ```typescript
   import { Dimensions, PixelRatio } from '@mrn/react-native';
   
   const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
   const designWidth = 375; // 设计稿宽度
   
   // 通用适配函数（最常用）
   const scale = (size: number) => (screenWidth / designWidth) * size;
   
   // 获取最细边框宽度（用于显示1物理像素的细线）
   const hairlineWidth = 1 / PixelRatio.get();
   
   // 示例使用
   const styles = StyleSheet.create({
     container: {
       width: scale(100),           // 设计稿100px → 适配当前屏幕
       height: scale(50),           // 设计稿50px → 适配当前屏幕
       fontSize: scale(16),         // 设计稿16px → 适配当前屏幕
       borderWidth: hairlineWidth,  // 显示1物理像素的细线
       borderColor: '#E5E5E5',
     },
     separator: {
       height: hairlineWidth,       // 分割线使用最细线条
       backgroundColor: '#F0F0F0',
     }
   });
     ```
   
   **PixelRatio实际应用场景**：
   - **最常用**：`1 / PixelRatio.get()` 用于获取最细边框线
     * 在2倍屏设备上 = 0.5 逻辑像素
     * 在3倍屏设备上 = 0.33 逻辑像素  
     * 确保在任何设备上都显示1个物理像素的细线
   
   - **典型使用场景**：
     * 分割线：`height: 1 / PixelRatio.get()`
     * 边框：`borderWidth: 1 / PixelRatio.get()`
     * 细线装饰：确保视觉上的一致性

5. **文本处理**：
   - 静态文本直接使用设计中的文字
   - 设置正确的字体、大小、颜色和对齐方式
   - 长文本设置numberOfLines并使用ellipsizeMode处理溢出

6. **图片资源**：
   - **必须使用正确的Image标签语法：`source={{ uri: 'imageUrl' }}`**
   - **设计数据中的image_url属性需添加'https://p0.meituan.net'前缀**
   - **完整写法：`source={{ uri: 'https://p0.meituan.net' + image_url }}`**
   - 缺少资源时使用DummyImage占位："https://dummyimage.com/600x400/666/fff.png"

7. **样式标准**：
   - 使用StyleSheet.create创建样式表
   - 遵循RN样式规范，严禁使用不支持的CSS属性（如gap、grid等）

8. **库使用规范**：
   - **MRN项目**：使用"@mrn/react-native"替代标准"react-native"库

9. **条件渲染**：
   - 所有条件渲染变量必须显式转为布尔值：`Boolean(variable)`
   - 避免直接使用变量作为条件判断

# 输出
- 仅输出组件代码和用于导出组件的索引文件
- **不要输出或修改其他内容，比如入口文件、README等**
</code_implementation>

<code_quality_review>
# 代码质量检查流程
本流程用于在代码生成后，对源码进行快速审查，确保满足以下五大维度：

1. **移动端自适应**
   - 使用 flex、百分比等相对单位布局，避免硬编码固定宽高
   - 必要时采用屏幕适配方案（如 Dimensions.get('window') 或 scale 函数）
   - 确保在 320px-428px 屏幕范围内正常显示
   - 字体大小使用适配后的数值，保证可读性

2. **接口绑定正确性**
   - 数据源来自明确的 `props` 或 `hooks`，使用 useMemo 处理数据转换
   - 校验 API 路径、参数、字段名与 UI 映射是否一致
   - 避免未使用或多余字段，确保数据解构安全
   - 数据字段命名使用领域模型相关的名称，保持语义清晰

3. **编码规范一致性**
   - 组件、方法命名遵循项目既定规范（通常为 PascalCase / camelCase）
   - 保持与项目整体代码风格一致，包括缩进、空格、换行等
   - 避免魔法数字，使用常量或配置文件
   - 保持单一职责原则，组件功能职责清晰
   - 清理调试代码（如 `console.log`），确保代码整洁

4. **边界情况与错误处理**
   - 对可能为 `null/undefined` 的值进行条件渲染，避免显示空白内容
   - 数据缺失时采用不渲染策略，而非显示错误提示（除非需求明确要求）
   - 确保组件在异常情况下优雅降级，用户感知不到错误
   - **条件渲染必须使用布尔值**：
     * ✅ 正确：`{Boolean(data?.field) && <Component />}`
     * ✅ 正确：`{!!data?.field && <Component />}`  
     * ✅ 正确：`{data?.field ? <Component /> : null}`
     * ❌ 错误：`{data?.field && <Component />}` (可能渲染对象或数字导致崩溃)
   - 避免白屏或崩溃，保证用户体验流畅

5. **React Native 样式属性兼容性**
   - 仅使用 RN 支持的样式属性，避免 web-only 属性（如 `gap`、`grid` 等）
   - 针对 iOS/Android 平台差异，添加条件样式处理
   - 图片资源使用正确的 `source={{ uri: 'imageUrl' }}` 语法
   - 样式对象使用 StyleSheet.create 创建，遵循 RN 样式规范

## 执行步骤
1. **静态分析**：检查代码语法和基本规范问题
2. **维度审查**：按上述 5 维度逐项检查，记录问题与改进建议
3. **条件渲染专项检查**：扫描所有 `&&` 条件渲染，确保左侧为布尔值
4. **输出报告**：使用 JSON 数组输出，每项结构如下：
```json
{
  "dimension": "边界情况与错误处理",
  "passed": false,
  "problems": ["{dealCard?.secDealTags?.[0] && <Text>} 可能渲染对象而非组件"],
  "suggestions": ["改为 {Boolean(dealCard?.secDealTags?.[0]) && <Text>} 或使用三元表达式"]
}
```
5. **修复迭代**：针对未通过的维度逐项修复并重复检查，直至全部通过

## 质量标准
- **必须通过**：移动端自适应、RN样式兼容性、条件渲染布尔值检查
- **重要**：接口绑定正确性、编码规范一致性  
- **建议**：边界情况处理（在不影响用户体验前提下）

## 条件渲染最佳实践
```typescript
// ✅ 推荐写法
{Boolean(data?.items?.length) && (
  <View>{/* 渲染列表 */}</View>
)}

// ✅ 推荐写法
{!!data?.showBadge && (
  <View>{/* 渲染徽章 */}</View>
)}

// ✅ 推荐写法
{data?.content ? (
  <Text>{data.content}</Text>
) : null}

// ❌ 危险写法（会直接崩溃报错）
{data?.items?.length && <View />}  // length为0时渲染数字0，RN要求文本必须用Text包裹
{data?.content && <Text />}        // content为空字符串时渲染空字符串，同样报错
{data?.user && <UserCard />}       // user为对象时渲染[object Object]，直接崩溃
```

**重要提醒**：React Native 比 React Web 更严格，任何非布尔值的条件渲染都可能导致应用崩溃，而不仅仅是显示异常。
</code_quality_review>

<mrn_components>
1. 基础组件(无需使用MCP查询文档)
   - 与react native组件相同，使用"@mrn/react-native"库
2. 特殊组件(需使用MCP查询文档)
   - LinearGradient: 渐变色组件，支持线性渐变和角度渐变。适用于创建背景渐变、按钮渐变和遮罩层渐变等效果。
</mrn_components>

<max_leez_components>
栗子设计（Leez Design）是美团到店设计和前端团队一起设计并开发的一套到店C端设计标准及组件。栗子设计-Max组件（Leez-Max）是栗子设计中的重要组成部分。它涵盖了（或即将涵盖）所有的栗子设计中的标准和组件。

- ActionSheet: 从屏幕底部弹出的菜单，提供一组相关操作选项
- Avatar: 用户头像展示组件，支持圆形、方形和多种尺寸
- Badge: 在图标或文字右上角显示的小红点或数字标记，用于消息提醒
- Button: 统一风格的按钮组件，支持多种类型、尺寸和状态
- Card: 包含内容和操作的卡片容器，用于信息展示和交互
- CascadeMenu: 多级联动的下拉菜单，适用于分类数据的选择
- Checkbox: 多选框组件，支持单独使用或组合使用
- Context: 提供全局状态和上下文管理的工具组件
- Coupon: 优惠券展示组件，支持多种样式和状态
- Dialog: 模态对话框组件，用于重要信息确认和交互
- DialogJSX: 支持使用JSX语法定义内容的对话框组件
- DialogManager: 统一管理多个对话框的显示和隐藏
- GoodsCard: 用于展示商品信息的卡片组件，包含图片、价格等信息
- Icon: 图标组件，提供丰富的内置图标和自定义能力
- IconButton: 包含图标的按钮组件，常用于工具栏和操作区
- Image: 增强的图片组件，支持懒加载、占位图和加载失败处理
- ImageList: 用于展示多张图片的网格列表组件
- Indicator: 用于指示当前状态或位置的组件，如分页指示器
- InlineView: 类似于行内元素的视图容器，适用于文本中嵌入视图
- InvalidState: 展示无效或错误状态的组件，如空数据、网络错误等
- Line: 用于分隔内容的水平或垂直线条
- LiveAtmosphere: 用于增强直播互动体验的氛围组件，如点赞动画
- LiveGuidance: 直播场景中的用户引导组件，如新功能引导
- LiveTag: 用于直播场景的标签组件，如直播状态、热度等
- LiveWindow: 直播视频窗口组件，支持各种控制和交互
- Loading: 加载状态指示器，支持多种样式和自定义
- LoadingManager: 统一管理应用中多个加载状态的管理器
- LoadingView: 包含加载指示器的内容视图，用于异步加载场景
- Modal: 模态对话框容器，可自定义内容和交互
- ModalBaseContainer: 模态框的基础容器组件，用于构建自定义模态组件
- NavigationBar: 应用顶部导航栏，支持标题、返回按钮和自定义操作
- PhoneModal: 专用于手机号码输入和验证的模态框
- Price: 价格展示组件，支持多种货币符号和格式
- Rate: 星级评分组件，支持只读和交互模式
- Shadow: 为元素添加阴影效果的工具组件，简化阴影样式设置
- SimpleHtmlParser: 简单的HTML解析工具，用于将HTML转换为组件
- SimpleRichHtml: 用于展示简单富文本内容的组件
- SlideModal: 从屏幕边缘滑入的模态框，支持多种进入方向
- Stepper: 数字输入框，用于数量的增减操作
- Switch: 开关选择器组件，用于开/关状态切换
- Tab: 标签页组件，用于内容分类展示和切换
- TabCapsule: 胶囊形状的标签切换组件，常用于筛选条件
- Tag: 用于分类、标记和选择的标签组件
- Text: 增强的文本组件，支持多种文本样式和处理
- TextButton: 文字按钮组件，无背景色的轻量级按钮
- Tip: 轻量级提示组件，用于展示简短的提示信息
- Toast: 轻量级的反馈提示组件，自动消失
- ToastManager: 统一管理应用中多个Toast提示的管理器
- ToggleGroup: 选项组切换组件，如单选按钮组、多选按钮组
- TopView: 应用最顶层的视图容器，用于全局弹窗和提示
- TopViewProvider: 为应用提供顶层视图容器的上下文提供者
- VideoEntry: 视频入口展示组件，用于视频列表或推荐
- LinearGradient: 渐变色组件，支持线性渐变和角度渐变。适用于创建背景渐变、按钮渐变和遮罩层渐变等效果。
</max_leez_components>

<util_api_doc>
- lxTrackMGEClickEvent: 点击埋点，用于上报 MGE 点击事件埋点
- lxTrackMGEViewEvent: 曝光埋点，用于上报 MGE 曝光事件埋点
- openUrl: 链接打开，用于打开指定 URL，支持传递参数和选项
</util_api_doc>
