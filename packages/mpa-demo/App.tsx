import React from 'react'
import { View, Text } from '@mrn/react-native'
import { MCModule, MCPage } from '@nibfe/doraemon-practice'

import { NavModule } from 'components/nav/NavModule'
import { FooterModule } from 'components/footer/FooterMoudle'

export default class App extends React.Component {
  render() {
    return (
      <MCPage
        contentBackgroundColor="#ffffff"
        // eslint-disable-next-line react-native/no-inline-styles
        separatorLineStyle={{
          display: 'hidden-all'
        }}
        modules={[
          {
            moduleKey: 'NavModule',
            module: <NavModule />
          },
          {
            moduleKey: 'ContentModule1',
            module: (
              <MCModule>
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: 200,
                    backgroundColor: '#a0eee1'
                  }}
                >
                  <Text>页面内容1</Text>
                </View>
              </MCModule>
            )
          },
          {
            moduleKey: 'ContentModule2',
            module: (
              <MCModule>
                <View
                  style={{
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: 200,
                    backgroundColor: '#beedc7'
                  }}
                >
                  <Text>页面内容2</Text>
                </View>
              </MCModule>
            )
          },
          {
            moduleKey: 'FooterModule',
            module: <FooterModule />
          }
        ]}
      />
    )
  }
}
