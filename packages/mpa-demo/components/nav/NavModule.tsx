import React from 'react'
import { pageRouterClose } from '@mrn/mrn-utils'
import { MCModule } from '@nibfe/doraemon-practice'
import NavigationBar from '@max/leez-navigation-bar'
import LText from '@max/leez-text'

interface Props {}

export const NavModule: React.FC<Props> = () => {
  const back = () => {
    pageRouterClose()
  }
  return (
    <MCModule paddingLeft={0} paddingRight={0} hoverType="alwaysHover" hoverOffset={0}>
      {/* <View style={styles.container}>
        <TouchableWithoutFeedback onPress={back}>
          <View style={styles.left}>
            <Image
              style={styles.backImage}
              source={{
                uri: 'https://p0.meituan.net/travelcube/57c78bbb83d26311c7f0206776a047b32864.png'
              }}
            />
          </View>
        </TouchableWithoutFeedback>
        <Text style={styles.title}>{'页面标题'} </Text>
        <View style={styles.right} />
      </View> */}
      <NavigationBar
        safeArea="normal"
        style={{ backgroundColor: '#ffffff' }}
        iconPosition="absolute"
        onBackPress={back}
        backIcon={{ name: 'fanhui' }}
      >
        <LText type="title3" lineClamp={1} style={{ textAlign: 'center' }}>
          标题
        </LText>
      </NavigationBar>
    </MCModule>
  )
}
