// mrn.config.js 配置文档
// http://mrn.sankuai.com/docs/guide/conf.html#mrn-config-js

module.exports = {
  name: 'mcone-template-mpa-demo',
  main: './index.tsx', // 页面入口地址
  biz: 'gcbu',
  bundleType: 1,
  bundleDependencies: ['@mrn/mrn-base'],
  debugger: {
    //
    moduleName: 'mcone-template-mpa-demo',
    initialProperties: {
      hideNavigationBar: true
    }
  },
  // 转 H5 配置
  one: {
    appConfig: {
      pages: [
        {
          name: 'mcone-template-mpa',
          path: 'index.tsx',
          enableShareAppMessage: true
        }
      ]
    }
  }
}
