// mrn.config.js 配置文档
// http://mrn.sankuai.com/docs/guide/conf.html#mrn-config-js
let iconfont
try {
  // 由于 talos 流水线 MRN-CAT 插件在 Yarn 插件前，
  // 该插件会 require('mrn.config.js')，但此时尚未安装 npm 依赖。
  // 此时直接忽略错误即可。
  iconfont = require('@max/leez-icon/font.js').fonts
} catch (e) {}
module.exports = {
  /**
   * 这里注册的 name 可以是集体内不冲突的任意名字
   * 命名规范建议
   * ${repoName}-${packageName}
   */
  name: 'mrn-beauty-craftsman-live-center',
  main: './index.tsx', // 页面入口地址
  biz: 'gcbu',
  bundleType: 1,
  bundleDependencies: ['@mrn/mrn-base'],
  debugger: {
    //
    moduleName: 'index',
    initialProperties: {
      hideNavigationBar: true
    }
  },
  // 转 H5 配置
  // one: {
  //   appConfig: {
  //     pages: [
  //       {
  //         name: 'mrn-beauty-craftsman-live-center',
  //         path: 'index.tsx',
  //         enableShareAppMessage: true
  //       }
  //     ]
  //   }
  // },
  // leez-icon 使用配置
  fonts: {
    ...iconfont
  }
}
