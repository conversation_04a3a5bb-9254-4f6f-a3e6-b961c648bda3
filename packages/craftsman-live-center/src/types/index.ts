/**
 * 直播中心头部详情数据
 */
export interface InfoResponse {
  headInfo: AnchorHeadInfoDTO
  simpleStatisticModule: SimpleStatisticModuleDTO
}

export interface AnchorHeadInfoDTO {
  name: string
  techId: number
  shopId: number
  anchorImage: string
  backgroundImage: string
  medalList: TechMedalTaskDto[]
}

export interface TechMedalTaskDto {
  title: string
  image: string
  type: string
  description: string
  wined: boolean
}

export interface SimpleStatisticModuleDTO {
  statisticList: StatisticInfoDTO[]
}

export interface StatisticInfoDTO {
  title: string
  value: string
  growthValue: string
}

/**
 * 直播效果模块
 */
export interface EffectReq {
  periodTabKey: string
}
export interface EffectRes {
  // effectsModule: EffectsModuleDTO
  title: string
  moreDetailDesc: string
  periodTabInfoList: TabInfoDTO[]
  statisticList: StatisticInfoDTO[]
}

export interface EffectsModuleDTO {
  title: string
  moreDetailDesc: string
  periodTabInfoList: TabInfoDTO[]
  statisticList: StatisticInfoDTO[]
}

export interface TabInfoDTO {
  key: string
  title: string
  currentMatch: boolean
}
export interface StatisticInfoDTO {
  title: string
  value: string
}

/**
 * T端榜单数据
 */
export interface RankReq {
  periodTabKey?: string // 指定命中筛选的时段tab
  rankTypeTabKey?: string // 指定筛选的榜单类型
}
export interface RankRes {
  title: string
  periodTabInfoList: TabInfoDTO[]
  rankTypeTabInfoList: TabInfoDTO[]
  moreUrl: string
  startLiveUrl: string
  curRankText: string
  rankList: LiveRankDTO[]
}

export interface LiveRankDTO {
  attrValues: {
    categoryId: number
    technicianId: number
    anchorName: string // 主播名称
    anchorImage: string // 主播头像
    cityName: string // 城市名称
    followers: string // 粉丝数
    playStatus: PlayStatusEnum // 直播状态
    liveTitle: string // 直播标题
    rankIcon: string // 排名图标
  }
  detailUrl: string // 详情页url
  rank: {
    rankType: string
    rank: number
  }
  statisticValues: {
    rankText: string // 排序佐证数值
  }
  shopInfo: {
    shopName: string
    shopId: number
    distance: string
    regionName: string
    shopDetailUrl: string
  }
}

/**
 * 直播状态
 * NOTSTART(1, "未开始"),
 * DOING(2, "直播中"),
 * FINISH(3, "直播结束");
 */
export enum PlayStatusEnum {
  NOTSTART = 1,
  DOING = 2,
  FINISH = 3
}

export * from './data'
