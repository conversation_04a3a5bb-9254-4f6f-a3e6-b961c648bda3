export interface postShopLiveType {
  pageNo: number
  pageSize: number
}

export interface postShopLiveShareType {
  liveId: string
}

export interface ShopLiveListType {
  pageNo: number
  pageSize: number
  liveId: string
}

export interface TabularDataType {
  pageNo: number
  pageSize: number
  records?: DataSourceType[]
  totalCount: number
}

export interface DataSourceType {
  liveInfoByTechDTO: {
    anchorId: number
    beginTime: number
    cover11: string
    endTime: number
    liveH5Url?: string
    liveIdL?: string
    liveTitle?: string
    playStatus?: number
    shopId?: number
    technicianId?: number
    liveId?: string
  }
  shareEffectDTO: {
    orderActutalPayAmount?: number
    orderAmount?: number
    orderCancelQuantity?: number
    orderQuantity?: number
    orderVerifyAmount?: number
    orderVerifyCouponQuantity?: number
    referralCount?: number
    referralTechCount?: number
    referralOrderCount?: number
    orderActualPayAmount?: number
    cashbackOrderCount?: number // 返现订单数
    cashbackAmount?: number // 返现金额
  }
}

export interface pageType {
  pageNo: number
  pageSize: number
  total?: number
}
