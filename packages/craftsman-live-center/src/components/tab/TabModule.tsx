import {
  View,
  StyleSheet,
  TouchableOpacity,
  // TouchableWithoutFeedback,
  Image,
  // ImageBackground,
  // Dimensions
  Text
} from '@mrn/react-native'
import React, { useState } from 'react'
import { MCModule } from '@nibfe/doraemon-practice'
import { getRecommendHeight } from '@nibfe/dm-navigation'
import Tab from '@max/leez-tab'

import { commonStyles } from '../../lib/commonStyle'
import { RankRes } from 'src/types'
import { openUrl } from '@mrn/mrn-utils'
interface Props {
  info: RankRes
  timeTabIndex: number
  rankTypeTabIndex: number
  changeRankTab: (timeTabIndex: number, rankTabIndex: number) => void
}

export const TabModule: React.FC<Props> = props => {
  const { info, timeTabIndex, rankTypeTabIndex, changeRankTab } = props

  const [hoverStatus, setHoverStatus] = useState(false)

  const { periodTabInfoList, rankTypeTabInfoList } = info || {}

  // const tabs = ['人气榜', '带货榜']
  // const times = ['小时', '本周', '本月']
  const tagClickHandle = index => {
    changeRankTab(timeTabIndex, index)
  }
  const timeClickHandle = index => {
    changeRankTab(index, rankTypeTabIndex)
  }

  return (
    <MCModule
      onHoverStatusChanged={event => {
        if (event && event.hoverStatus) {
          setHoverStatus(event.hoverStatus === 'hovering')
        }
      }}
      hoverType="autoHover"
      hoverOffset={getRecommendHeight()}
      paddingHorizontal={hoverStatus ? 0 : 12}
      backgroundColor={'transparent'}
    >
      <View style={[styles.liveWrap, hoverStatus ? styles.hoverStyle : styles.noHoverStyle]}>
        {!hoverStatus && (
          <View style={[commonStyles.flexRowCenter, styles.titleLine]}>
            <Text style={styles.title}>{info.title}</Text>
            <TouchableOpacity
              style={commonStyles.flexRowCenter}
              onPress={() => {
                openUrl(info.moreUrl)
              }}
            >
              <Text style={[styles.titleInfo]}>查看全部榜单</Text>
              <Image
                style={styles.titleArrow}
                source={{
                  uri: 'https://p0.meituan.net/travelcube/e4748670bf4640f1bf6c8c196d25f23f409.png'
                }}
              />
            </TouchableOpacity>
          </View>
        )}
        <View style={[commonStyles.flexRowCenter, styles.tabline]}>
          <View style={[commonStyles.flexRowCenter, { flex: 1 }]}>
            {/* {rankTypeTabInfoList.map((tab, index) => (
              <TouchableOpacity
                onPress={() => {
                  tagClickHandle(index)
                }}
                style={[styles.tagItem, rankTypeTabIndex === index ? styles.isSelected : {}]}
              >
                <Text style={[styles.tagText, rankTypeTabIndex === index ? styles.isSelected : {}]}>
                  {tab.title}
                </Text>
              </TouchableOpacity>
            ))} */}
            <Tab
              activeIndex={rankTypeTabIndex}
              onItemPress={tagClickHandle}
              data={rankTypeTabInfoList}
              enableAnimation={false}
              onScroll={() => {}}
            />
          </View>
          <View style={[commonStyles.flexRowCenter]}>
            {periodTabInfoList.map((time, index) => (
              <TouchableOpacity
                onPress={() => {
                  timeClickHandle(index)
                }}
                style={styles.timeItem}
              >
                <Text style={timeTabIndex === index ? styles.timeSelected : {}}>{time.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  liveWrap: {
    backgroundColor: '#fff',
    // marginTop: 12,
    // marginHorizontal: 12,
    padding: 12
    // paddingBottom: 15

    // position: 'absolute'
    // top: 0
    // borderRadius: 20
  },
  hoverStyle: {
    // backgroundColor: 'red',
    height: 44,
    paddingVertical: 0,
    justifyContent: 'center'
  },
  noHoverStyle: {
    borderTopRightRadius: 12,
    borderTopLeftRadius: 12,
    paddingBottom: 15
  },
  titleLine: {
    height: 19,
    marginBottom: 12,
    justifyContent: 'space-between'
  },
  title: {
    fontWeight: '600',
    fontSize: 16,
    color: 'black'
  },
  titleInfo: {
    marginLeft: 14,
    color: '#000000',
    fontSize: 12
  },
  titleArrow: {
    width: 12,
    height: 12
  },
  tabline: {
    height: 25,
    justifyContent: 'space-between'
    // marginBottom: 12
  },
  tagItem: {
    marginRight: 5,
    backgroundColor: '#F3F3F3',
    borderRadius: 12.5,
    height: 25,
    paddingHorizontal: 15,
    justifyContent: 'center'
  },
  tagText: {
    color: '#111111',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12
  },
  isSelected: {
    backgroundColor: '#FFEDE5',
    color: '#FF6633'
  },
  timeItem: {
    marginLeft: 14
  },
  timeSelected: {
    color: '#FF6633'
  },
  content: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between'
  },
  item: {
    marginBottom: 5,
    width: 108,
    height: 61,
    backgroundColor: '#F8FAFB',
    justifyContent: 'center',
    alignItems: 'center'
  },
  tips: {
    marginTop: 7,
    color: '#979797',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 11,
    lineHeight: 13
  }
})
