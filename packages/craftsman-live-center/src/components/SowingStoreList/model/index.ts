/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useEffect } from 'react'
import MRNKNB from '@mrn/mrn-knb'
import {
  postShopLive,
  postShopLiveShare,
  postShopLiveDetailHead,
  postShopLiveDetailOrderList
} from 'src/api'
import { DataSourceType, pageType } from 'src/types'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'
import { _channel } from 'src/controller'

const useModel = () => {
  const [showModal, setShowModal] = useState<boolean>(false)
  const [dataSource, setDataSource] = useState<DataSourceType[]>([])
  const [isLoading, setIsLoading] = useState({
    loadingStatus: 'loading',
    loadingMoreStatus: 'done'
  })
  const [modalHeaderData, setModalHeaderData] = useState<DataSourceType>()
  const [modalDataList, setModalDataList] = useState([])
  const [listPage, setListPage] = useState<pageType>({
    pageNo: 1,
    pageSize: 10
  })
  const [modalPage, setModalPage] = useState<pageType>({
    pageNo: 1,
    pageSize: 10
  })
  const [liveId, setLiveId] = useState<string>('')
  const [isFooterView, setIsFooterView] = useState<boolean>(false)
  const [onEarth, setOnEarth] = useState<boolean>(false)

  const resetModal = () => {
    // 关闭弹窗重置数据
    setOnEarth(false)
    setModalDataList([])
    setModalPage({
      pageNo: 1,
      pageSize: 10
    })
  }

  useEffect(() => {
    init()
  }, [listPage?.pageNo])

  const init = async () => {
    try {
      const res = await postShopLive({ pageNo: listPage.pageNo, pageSize: listPage.pageSize })
      if (res) {
        setIsLoading(() => ({
          loadingStatus: 'done',
          loadingMoreStatus:
            // @ts-ignore
            [...dataSource, ...res?.records]?.length < res?.totalCount ? 'loading' : 'done'
        }))
        // @ts-ignore
        setIsFooterView([...dataSource, ...res?.records]?.length === res?.totalCount)
        // @ts-ignore
        setDataSource([...dataSource, ...res?.records])
        setListPage({
          pageNo: res?.pageNo,
          pageSize: res?.pageSize
        })
      }
    } catch (error) {
    } finally {
      setIsLoading(preState => ({
        ...preState,
        loadingStatus: 'done'
      }))
    }
  }

  // 分页搭桥
  const _isHasNext = () => {
    if (!isFooterView) {
      setListPage({
        pageNo: listPage?.pageNo + 1,
        pageSize: 10
      })
    } else return
  }

  const onShare = (id: string, tech_id: string) => {
    postShopLiveShare({ liveId: id }).then((res: any) => {
      if (res) {
        MRNKNB.share({
          title: res?.liveTitle || '',
          image: res?.cover11 || '',
          url: res?.liveH5Url || '',
          desc: res?.desc || '',
          channel: [MRNKNB.share.WECHAT_FRIENDS, MRNKNB.share.WECHAT_TIMELINE, MRNKNB.share.COPY],
          success: function (result) {
            lxTrackMGEClickEvent('gc', 'b_gc_yomzmfty_mc', 'c_gc_6xdi4ie9', {
              tech_id: tech_id,
              // @ts-ignore
              title: _channel(result?.sharedTo)
            })
          }
        })
        const arr = [
          { id: tech_id, title: '微信好友' },
          { id: tech_id, title: '微信朋友圈' },
          { id: tech_id, title: '复制链接' }
        ]
        arr.map(item => {
          lxTrackMGEViewEvent('gc', 'b_gc_yomzmfty_mv', 'c_gc_6xdi4ie9', {
            tech_id: item?.id,
            title: item?.title
          })
        })
      }
    })
    lxTrackMGEClickEvent('gc', 'b_gc_lo4pj35s_mc', 'c_gc_6xdi4ie9', {
      tech_id: tech_id
    })
  }

  const getDetailHead = async (id: string) => {
    try {
      const res = await postShopLiveDetailHead({ liveId: id })
      // @ts-ignore
      if (res) setModalHeaderData(res)
    } catch (error) {}
  }

  const getDetailOrderList = async (id?: string) => {
    if (onEarth) return
    try {
      const res = await postShopLiveDetailOrderList({
        liveId: id || liveId,
        pageNo: modalPage.pageNo,
        pageSize: modalPage.pageSize
      })
      if (res) {
        // @ts-ignore
        if ([...modalDataList, ...res?.records]?.length <= res?.totalCount) {
          // @ts-ignore
          setModalDataList([...modalDataList, ...res?.records])
        }
        // @ts-ignore
        setOnEarth([...modalDataList, ...res?.records]?.length === res?.totalCount)
        // @ts-ignore
        if ([...modalDataList, ...res?.records]?.length < res?.totalCount) {
          setModalPage({
            pageNo: res?.pageNo + 1,
            pageSize: res?.pageSize
          })
        }
      }
    } catch (error) {}
  }

  const onPopup = (id?: string, tech_id?: string) => {
    setShowModal(!showModal)
    if (id) {
      setLiveId(id)
      getDetailOrderList(id)
      getDetailHead(id)
    }
    if (tech_id) {
      lxTrackMGEViewEvent('gc', 'b_gc_7e5t69sn_mv', 'c_gc_6xdi4ie9', {
        tech_id: tech_id
      })
    }
  }

  return {
    setShowModal,
    showModal,
    onShare,
    dataSource,
    isLoading,
    onPopup,
    modalHeaderData,
    modalDataList,
    isFooterView,
    init,
    _isHasNext,
    getDetailOrderList,
    onEarth,
    resetModal
  }
}
export default useModel
