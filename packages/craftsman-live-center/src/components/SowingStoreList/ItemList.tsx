import React from 'react'
import { View, TouchableOpacity, Text, Image, Dimensions } from '@mrn/react-native'
import { Icon } from '@nibfe/gc-ui'
import { FlatList } from 'react-native'
import { styles } from './style'
import dayjs from 'dayjs'
import {
  _status,
  orderAmountList,
  showGridData,
  transactionOrRefund,
  _forbiddenButton,
  modalShowData
} from 'src/controller'
import { LinearGradient } from '@mrn/react-native-linear-gradient'

const { width } = Dimensions.get('window')

/**
 * 直播列表
 * @param param
 * @returns
 */
export const LiveBroadcastList = ({ onShare, item, onPopup }) => {
  const { liveInfoByTechDTO, shareEffectDTO } = item

  return (
    <View style={styles.liveWrap}>
      <TouchableOpacity
        onPress={() => onPopup(liveInfoByTechDTO?.liveId, liveInfoByTechDTO?.technicianId)}
      >
        <View>
          <View style={styles.container}>
            <View style={styles.listInfo}>
              <Image
                style={styles.image}
                source={{
                  uri: liveInfoByTechDTO?.cover11
                }}
              />
              <View style={styles.liveInfo}>
                <Text numberOfLines={1} style={[styles.mainTitle, { width: width - 165 }]}>
                  {liveInfoByTechDTO?.liveTitle}
                </Text>
                <Text style={styles.timeTitle} textBreakStrategy="simple">
                  直播时间: {dayjs(liveInfoByTechDTO?.beginTime).format('YYYY.MM.DD HH:mm')} -{' '}
                  {dayjs(liveInfoByTechDTO?.endTime).format('MM.DD HH:mm')}
                </Text>
                {liveInfoByTechDTO?.playStatus === 2 && (
                  <Image
                    style={styles.stateImg}
                    source={{
                      uri: 'https://p0.meituan.net/travelcube/d20673e7d92f933012ba7fd0a547aef434908.gif'
                    }}
                  />
                )}
                {liveInfoByTechDTO?.playStatus === 1 && (
                  <Text style={styles.statusText}>即将开播</Text>
                )}
                {liveInfoByTechDTO?.playStatus === 3 && (
                  <Text style={styles.statusEnd}>已结束</Text>
                )}
                {liveInfoByTechDTO?.playStatus === 4 && (
                  <Text style={styles.statusEnd}>直播暂停</Text>
                )}
              </View>
            </View>
            {!_forbiddenButton(liveInfoByTechDTO?.playStatus) && (
              <TouchableOpacity
                onPress={() => onShare(liveInfoByTechDTO?.liveId, liveInfoByTechDTO?.technicianId)}
              >
                <LinearGradient
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  colors={['#FF7700', '#FF4B10']}
                  style={[styles.share]}
                >
                  <Text style={styles.shareSize}>分享</Text>
                </LinearGradient>
              </TouchableOpacity>
            )}
          </View>
        </View>
        <View style={styles.bottomDisplay}>
          {showGridData(shareEffectDTO).map(dataList => (
            <View>
              {dataList.map((item, i) => {
                return (
                  <View key={i} style={styles.boxItem}>
                    <Text style={styles.buttonTitle}>{item?.label}</Text>
                    <View style={styles.buttonBox}>
                      <Text style={styles.buttonSize}>{item?.value || '--'}</Text>
                      <Text numberOfLines={1} style={styles.buttonUnits}>
                        {item?.value && item.unit}
                      </Text>
                    </View>
                  </View>
                )
              })}
            </View>
          ))}
        </View>
      </TouchableOpacity>
    </View>
  )
}

/**
 *订单明细
 * @param param
 * @returns
 */
export const DetailList = ({ item }) => {
  const {
    pictureUrl,
    customerPhoneNo,
    actualPayAmount,
    orderPayTime,
    productName,
    status,
    orderId,
    cashbackAmount
  } = item
  return (
    <View>
      <View style={styles.modelWrap}>
        <View>
          <View style={styles.modelContainerFlex}>
            <Image
              style={styles.image}
              source={{
                uri: pictureUrl
              }}
            />
            <View style={{ width: width - 93 }}>
              <Text
                numberOfLines={2}
                style={[styles.modelContainerFlexText, { maxWidth: width - 93 }]}
              >
                {productName}
              </Text>
              <Text style={styles.toBeWrittenOff}>{_status(status)}</Text>
            </View>
          </View>
          <View style={styles.modelShowTitle}>
            <View style={{ width: '50%' }}>
              <Text style={styles.modelShowTitleSize}>实付金额：￥{actualPayAmount}</Text>
              <Text numberOfLines={1} style={[styles.modelShowTitleSize, { width: '100%' }]}>
                订单编号：{orderId}
              </Text>
              <Text style={styles.modelShowTitleSize}>
                返现金额：{!cashbackAmount && typeof cashbackAmount !== 'number' ? '-' : '￥'}
                {cashbackAmount}
              </Text>
            </View>
            <View>
              <Text style={styles.modelShowTitleSize}>顾客手机号：{customerPhoneNo}</Text>
              <Text style={styles.modelShowTitleSize}>
                成交时间：{dayjs(orderPayTime).format('YYYY-MM-DD HH:mm')}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  )
}

export const ModalContent = ({
  setShowModal,
  modalHeaderData,
  onShare,
  modalDataList,
  onChangePage,
  onEarth
}) => {
  return (
    <View style={styles.modalView}>
      <View style={styles.modelTitle}>
        <Text style={styles.modelText}>订单明细</Text>
        <TouchableOpacity style={styles.modalIcon} onPress={() => setShowModal(false)}>
          <Icon name={'close'} size={24} />
        </TouchableOpacity>
      </View>
      <View style={{ marginTop: 12 }}>
        <View style={styles.modelWrap}>
          <View>
            <View style={styles.container}>
              <View style={styles.listInfo}>
                <Image
                  style={styles.image}
                  source={{
                    uri: modalHeaderData?.liveInfoByTechDTO?.cover11
                  }}
                />
                <View style={styles.liveInfo}>
                  <Text style={[styles.mainTitle, { maxWidth: width - 93 }]}>
                    {modalHeaderData?.liveInfoByTechDTO?.liveTitle}
                  </Text>
                  <Text style={styles.timeTitle}>
                    直播时间:{' '}
                    {dayjs(modalHeaderData?.liveInfoByTechDTO?.beginTime).format(
                      'YYYY.MM.DD HH:mm'
                    )}{' '}
                    - {dayjs(modalHeaderData?.liveInfoByTechDTO?.endTime).format('MM.DD HH:mm')}
                  </Text>
                  {modalHeaderData?.liveInfoByTechDTO?.playStatus === 2 && (
                    <Image
                      style={styles.stateImg}
                      source={{
                        uri: 'https://p0.meituan.net/travelcube/d20673e7d92f933012ba7fd0a547aef434908.gif'
                      }}
                    />
                  )}
                  {modalHeaderData?.liveInfoByTechDTO?.playStatus === 1 && (
                    <Text style={styles.statusText}>即将开播</Text>
                  )}
                  {modalHeaderData?.liveInfoByTechDTO?.playStatus === 3 && (
                    <Text style={styles.statusEnd}>已结束</Text>
                  )}
                  {modalHeaderData?.liveInfoByTechDTO?.playStatus === 4 && (
                    <Text style={styles.statusEnd}>直播暂停</Text>
                  )}
                </View>
              </View>
              {!_forbiddenButton(modalHeaderData?.liveInfoByTechDTO?.playStatus) && (
                <TouchableOpacity
                  onPress={() =>
                    onShare(
                      modalHeaderData?.liveInfoByTechDTO?.liveId,
                      modalHeaderData?.liveInfoByTechDTO?.technicianId
                    )
                  }
                >
                  <LinearGradient
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    colors={['#FF7700', '#FF4B10']}
                    style={[styles.share]}
                  >
                    <Text style={styles.shareSize}>分享</Text>
                  </LinearGradient>
                </TouchableOpacity>
              )}
            </View>
          </View>
          <View style={styles.bottomDisplay}>
            <View>
              {modalShowData(modalHeaderData?.shareEffectDTO).map((item, i) => {
                return (
                  <View key={i} style={{ marginBottom: 10 }}>
                    <Text style={styles.buttonTitle}>{item?.label}</Text>
                    <View style={styles.buttonBox}>
                      <Text style={styles.buttonSize}>{item?.value || '--'}</Text>
                      <Text numberOfLines={1} style={styles.buttonUnits}>
                        {item?.value && item?.unit}
                      </Text>
                    </View>
                  </View>
                )
              })}
            </View>
            <View>
              {orderAmountList(modalHeaderData?.shareEffectDTO).map((item, i) => {
                return (
                  <View key={i} style={{ marginBottom: 10 }}>
                    <Text style={styles.buttonTitle}>{item?.label}</Text>
                    <View style={styles.buttonBox}>
                      <Text style={styles.buttonSize}>{item?.value || '--'}</Text>
                      <Text numberOfLines={1} style={styles.buttonUnits}>
                        {item?.value && item?.unit}
                      </Text>
                    </View>
                  </View>
                )
              })}
            </View>
            <View>
              {transactionOrRefund(modalHeaderData?.shareEffectDTO).map((item, i) => {
                return (
                  <View key={i} style={{ marginBottom: 10 }}>
                    <Text style={styles.buttonTitle}>{item?.label}</Text>
                    <View style={styles.buttonBox}>
                      <Text style={styles.buttonSize}>{item?.value || '--'}</Text>
                      <Text numberOfLines={1} style={styles.buttonUnits}>
                        {item?.value && item?.unit}
                      </Text>
                    </View>
                  </View>
                )
              })}
            </View>
          </View>
        </View>
      </View>
      <Text style={styles.modelOrder}>订单明细</Text>
      <FlatList
        data={modalDataList}
        gapTop={0}
        paddingHorizontal={0}
        backgroundColor="#F6F6F6"
        // @ts-ignore
        keyExtractor={(item, index) => {
          return `item_${index}`
        }}
        reuseIdentifierExtractor={(item, index) => {
          return `item_${index}`
        }}
        refreshing={true}
        onEndReachedThreshold={0.1}
        onEndReached={() => {
          onChangePage()
        }}
        // @ts-ignore
        renderItem={(itemData, index) => {
          return <DetailList key={`item_${index}`} item={itemData?.item} />
        }}
        ListFooterComponent={() => {
          return (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                height: 150,
                justifyContent: 'center'
              }}
            >
              {onEarth && (
                <Text
                  style={{
                    fontWeight: '400',
                    fontFamily: 'PingFangSC-Regular',
                    fontSize: 12,
                    color: '#777777',
                    marginBottom: 80
                  }}
                >
                  - 没有更多了 -
                </Text>
              )}
            </View>
          )
        }}
      />
    </View>
  )
}
