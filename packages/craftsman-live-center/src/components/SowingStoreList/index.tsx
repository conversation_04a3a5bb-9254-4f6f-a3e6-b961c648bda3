import React from 'react'
import { SlideModal } from '@nibfe/gc-ui'
import useModel from './model'
import { styles } from './style'
import { LiveBroadcastList, ModalContent } from './ItemList'
import { MCListModule } from '@nibfe/doraemon-practice'
import { EmptyView, FooterView } from './ListExtraView'
import { lxTrackMGEViewEvent } from '@mrn/mrn-utils'

export const SowingStoreList = () => {
  const {
    setShowModal,
    showModal,
    onShare,
    dataSource,
    isLoading,
    onPopup,
    modalHeaderData,
    modalDataList,
    _isHasNext,
    isFooterView,
    getDetailOrderList,
    onEarth,
    resetModal
  } = useModel()

  return (
    <>
      <MCListModule
        data={dataSource}
        isEmpty={dataSource?.length < 1 && isLoading.loadingStatus !== 'loading'}
        backgroundColor="#F4F4F4"
        paddingHorizontal={0}
        // @ts-ignore
        loadingStatus={isLoading.loadingStatus}
        // @ts-ignore
        loadingMoreStatus={isLoading.loadingMoreStatus}
        onNeedLoadMore={_isHasNext}
        onRetryForLoadingMoreFail={_isHasNext}
        // @ts-ignore
        keyExtractor={item => `key_${item?.liveInfoByTechDTO?.liveId}`}
        renderItem={(item, index) => {
          return <LiveBroadcastList item={item} key={index} onPopup={onPopup} onShare={onShare} />
        }}
        onAppear={item => {
          lxTrackMGEViewEvent('gc', 'b_gc_lo4pj35s_mv', 'c_gc_6xdi4ie9', {
            // @ts-ignore
            tech_id: item?.liveInfoByTechDTO?.technicianId || '-9999'
          })
        }}
        emptyView={<EmptyView />}
        headerView={<></>}
        footerView={isFooterView && <FooterView />}
      />
      {showModal && (
        <SlideModal
          visible={showModal}
          direction="up"
          wrapperStyles={styles.modalWrapper}
          modalProps={{
            maskClosable: true,
            animateable: false,
            onPressClose: () => {
              setShowModal(false)
              resetModal()
            }
          }}
        >
          <ModalContent
            setShowModal={setShowModal}
            modalHeaderData={modalHeaderData}
            onShare={onShare}
            modalDataList={modalDataList}
            onChangePage={getDetailOrderList}
            onEarth={onEarth}
          />
        </SlideModal>
      )}
    </>
  )
}
