import React from 'react'
import { View, Text, StyleProp, ViewStyle, StyleSheet, Image } from '@mrn/react-native'
import { getWidth } from '@mrn/mrn-gc-utils'

const rate = getWidth() / 375

export const FooterView: React.FC<{ style?: StyleProp<ViewStyle>; title?: string }> = ({
  style,
  title = '- 没有更多了 -'
}) => {
  return (
    <View style={[styles.footerView, style]}>
      <Text style={styles.footerText}>{title}</Text>
    </View>
  )
}

export const HeaderView: React.FC<{ style?: StyleProp<ViewStyle>; title?: string }> = ({
  style,
  title
}) => {
  return (
    <View style={[styles.headerView, style]}>
      <Text style={styles.headerText}>{title}</Text>
    </View>
  )
}

export const EmptyView: React.FC<{ style?: StyleProp<ViewStyle>; title?: string }> = ({
  style
}) => {
  return (
    <View style={[styles.emptyView, style]}>
      <Image
        source={{
          uri: 'https://p0.meituan.net/travelcube/968816b6abcf5106fccbfa078ccd247f68725.png'
        }}
        style={styles.emptyImage}
      />
      <Text
        style={{
          color: '#222222',
          fontFamily: 'PingFang SC',
          fontWeight: '600',
          fontSize: 16
        }}
      >
        暂无数据
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  headerView: {
    height: 35,
    paddingLeft: 10,
    flexDirection: 'row',
    alignItems: 'center'
  },
  headerText: {
    color: '#000',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '500',
    fontSize: 15
  },
  footerView: {
    alignItems: 'center',
    marginVertical: 10
  },
  footerText: {
    fontWeight: '400',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    color: '#777777',
    textAlign: 'center'
  },
  emptyView: {
    width: '100%',
    height: 560 * rate,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F4F4F4',
  },
  emptyImage: {
    width: 150,
    height: 133,
  }
})
