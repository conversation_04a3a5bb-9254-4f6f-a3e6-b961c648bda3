import { StyleSheet } from '@mrn/react-native'

export const styles = StyleSheet.create({
  liveWrap: {
    backgroundColor: '#fff',
    marginHorizontal: 12,
    marginTop: 12,
    padding: 12,
    paddingBottom: 15,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderStyle: 'solid'
  },

  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },

  listInfo: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center'
  },

  liveInfo: {
    flex: 1
  },

  image: {
    width: 60,
    height: 60,
    borderRadius: 6,
    marginRight: 6
  },

  stateImg: {
    height: 13,
    width: 41
  },

  statusText: {
    color: '#29CC29',
    fontSize: 12,
    fontWeight: '400'
  },

  statusEnd: {
    color: '#999999',
    fontSize: 12,
    fontWeight: '400'
  },

  mainTitle: {
    fontSize: 14,
    fontFamily: 'PingFang SC',
    fontWeight: '500',
    color: '#222222'
  },

  timeTitle: {
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'PingFang SC',
    color: '#666666',
    marginVertical: 5
  },

  share: {
    width: 52,
    height: 30,
    borderRadius: 30,
    backgroundColor: '#FF4B10',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },

  shareSize: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14
  },

  bottomDisplay: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // alignItems: 'center',
    paddingHorizontal: 10,
    marginTop: 12
  },

  boxItem: {
    marginBottom: 4
  },

  buttonTitle: {
    color: '#777777',
    fontSize: 10
  },

  buttonBox: {
    flexDirection: 'row',
    alignItems: 'center',
    fontFamily: 'MTfin2.0'
  },

  buttonSize: {
    color: '#111111',
    fontSize: 18,
    fontFamily: 'MTfin2.0',
    marginTop: 3
  },

  buttonUnits: {
    fontSize: 11,
    marginTop: 6,
    fontFamily: 'MTfin2.0',
    fontWeight: '400'
  },

  modalWrapper: {
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    overflow: 'hidden'
  },

  modalView: {
    height: 700,
    backgroundColor: '#F4F4F4'
  },

  modelTitle: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 18
  },

  modelText: {
    color: '#111111',
    fontSize: 18,
    fontWeight: '600'
  },

  modelContainerFlex: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },

  modelContainerFlexText: {
    flexWrap: 'wrap',
    overflow: 'hidden',
    paddingRight: 12
  },

  toBeWrittenOff: {
    color: '#666666',
    fontSize: 11,
    marginTop: 6
  },

  modalIcon: {
    position: 'absolute',
    top: -1,
    right: 17
  },

  modelWrap: {
    backgroundColor: '#fff',
    marginHorizontal: 12,
    marginTop: 12,
    padding: 12,
    paddingBottom: 15,
    borderRadius: 12
  },

  modelOrder: {
    color: '#9E9E9E',
    marginLeft: 21,
    fontSize: 14,
    fontWeight: '500'
  },

  modelShowTitle: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },

  modelShowTitleSize: {
    color: '#666666',
    fontSize: 11,
    marginTop: 12.5
  }
})
