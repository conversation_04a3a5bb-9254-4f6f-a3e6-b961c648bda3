import React from 'react'
import { MCModule } from '@nibfe/doraemon-practice'
// import NavigationBar from '@max/leez-navigation-bar'
// import LText from '@max/leez-text'
import { View } from '@mrn/react-native'

// import { Nav } from './nav'
import { CraftsmanInfo } from './craftsmanInfo'
import { InfoResponse } from 'src/types'

interface Props {
  info: InfoResponse
}

export const BackgroundModule: React.FC<Props> = (props: Props) => {
  const { info } = props
  return (
    <MCModule paddingLeft={0} paddingRight={0} backgroundColor="transparent">
      {/* <View>{Nav({})}</View> */}
      <View>{CraftsmanInfo({ info })}</View>
    </MCModule>
    // <MCModule paddingLeft={0} paddingRight={0} hoverType="alwaysHover" hoverOffset={0}>
    //   <ImageBackground
    //     source={{
    //       uri: 'https://img.meituan.net/technician/3d7d53a03d9c6a354f401b0643c51a97330417.jpg'
    //     }}
    //     style={styles.container}
    //   >
    //     <View>{Nav({})}</View>
    //     <View>{CraftsmanInfo({})}</View>
    //   </ImageBackground>
    // </MCModule>
  )
}
