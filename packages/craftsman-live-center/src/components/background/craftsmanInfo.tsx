import React from 'react'
// import NavigationBar from '@max/leez-navigation-bar'
// import LText from '@max/leez-text'
import { StyleSheet, View, Image, Text } from '@mrn/react-native'
import { commonStyles } from '../../lib/commonStyle'
import { InfoResponse, StatisticInfoDTO } from 'src/types'

interface Props {
  info: InfoResponse
}

const renderTagLine = props => {
  const { tagList } = props
  return (
    <View
      style={[
        commonStyles.flexRowCenter,
        {
          marginTop: 6,
          flexWrap: 'nowrap',
          overflow: 'hidden',
          height: 18
        }
      ]}
    >
      {tagList.map(tag => (
        <View
          style={{
            marginRight: 5,
            borderColor: '#9999995E',
            borderWidth: 1,
            borderRadius: 1,
            paddingHorizontal: 5,
            paddingVertical: 2.5
          }}
        >
          <Text style={{ color: '#ffffff', fontSize: 11 }}>{tag}</Text>
        </View>
      ))}
    </View>
  )
}

const renderDataItem = (item: StatisticInfoDTO, index: number) => {
  return (
    <>
      {index !== 0 && <View style={styles.line} />}
      <View style={styles.dataItem}>
        <Text style={styles.itemLabel}>{item.title}</Text>
        <Text style={styles.itemVal}>{item.value}</Text>
        <Text style={styles.itemDiff}>{item.growthValue}</Text>
      </View>
    </>
  )
}

export const CraftsmanInfo: React.FC<Props> = (props: Props) => {
  const { info } = props

  // const tagList = ['上海长宁区', '纹理烫', '初级交易达人']
  const tagList = []

  return (
    <View style={{ paddingBottom: 15, marginTop: 100 }}>
      {/* 个人信息 */}
      <View style={[commonStyles.flexRowCenter, styles.wrap]}>
        <View style={[styles.avatar, { marginRight: 15 }]}>
          <Image
            style={styles.avatar}
            source={{
              uri: info?.headInfo?.anchorImage
            }}
          />
        </View>
        <View>
          <View style={commonStyles.flexRowCenter}>
            <Text style={styles.title}>{info?.headInfo?.name}</Text>
            {/* <Image
              style={{ height: 20, width: 53 }}
              source={{
                uri: 'https://p1.meituan.net/dprainbow/52b9df0384a21ac73d2394a6bbbf14b917050.png'
              }}
            /> */}
            <View style={commonStyles.flexRowCenter}>
              {info.headInfo.medalList?.length > 0
                ? info.headInfo.medalList.map((item, index) => {
                    return (
                      <View style={[commonStyles.flexRowCenter, styles.tagItem]}>
                        <Image
                          key={index}
                          style={{ height: 17, width: 17 }}
                          source={{
                            uri: item.image
                          }}
                        />
                        <Text style={styles.tagText}>{item.title || ''}</Text>
                      </View>
                    )
                  })
                : null}
            </View>
          </View>
          {renderTagLine({ tagList })}
          <View />
        </View>
      </View>

      {/* 个人数据 */}
      <View
        style={[
          commonStyles.flexRowCenter,
          {
            marginHorizontal: 37,
            marginTop: 18,
            justifyContent: 'space-between'
          }
        ]}
      >
        {(info?.simpleStatisticModule?.statisticList || []).map((item, index) => {
          return renderDataItem(item, index)
        })}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  wrap: {
    paddingLeft: 15
  },
  avatar: {
    width: 57,
    height: 57,
    borderRadius: 45,
    // marginRight: 15,
    overflow: 'hidden'
  },
  title: {
    color: '#ffffff',
    fontWeight: '600',
    fontSize: 16
  },
  tagItem: {
    marginLeft: 5,
    // alignItems: 'center',
    // justifyContent: 'center',
    height: 16,
    backgroundColor: '#FEFEFE26',
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: '#FFFFFF19',
    borderBottomRightRadius: 8,
    borderTopRightRadius: 8,
    paddingRight: 5
  },
  tagText: {
    color: '#FFFFFF',
    fontFamily: 'PingFang SC',
    fontSize: 10,
    height: 16,
    lineHeight: 16
  },
  dataItem: {
    alignItems: 'center',
    justifyContent: 'center'
  },
  itemLabel: {
    fontSize: 13,
    color: '#FFFFFFBF'
  },
  itemVal: {
    marginTop: 6,
    fontSize: 13,
    color: '#ffffff',
    fontWeight: '500'
  },
  itemDiff: {
    marginTop: 4.5,
    fontSize: 13,
    color: '#FF6C5E',
    fontWeight: '500'
  },
  line: {
    height: 26.5,
    width: 1,
    backgroundColor: '#ffffff'
  }
})
