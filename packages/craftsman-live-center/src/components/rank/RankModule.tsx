import React, { useCallback, useEffect } from 'react'
import {
  View,
  StyleSheet,
  // TouchableWithoutFeedback,
  Image,
  ImageBackground,
  // Dimensions
  Text
} from '@mrn/react-native'
import { MCListModule } from '@nibfe/doraemon-practice'
import { RankRes, RankReq, LiveRankDTO } from 'src/types'
import { openUrl } from '@mrn/mrn-utils'
import { useImmer } from 'use-immer'

interface Props {
  info: RankRes
  load: (_params: RankReq) => void
}

const RANK_COLOR = ['#AF642E', '#615F88', '#8D4534']

const renderCardItem = (item: LiveRankDTO, index: number) => {
  const isLiving = item.attrValues?.playStatus === 2
  const isPlayback = item.attrValues?.playStatus === 3

  return (
    <View style={cardStyles.wrap}>
      <View style={[cardStyles.content, index === 0 ? cardStyles.firstItem : {}]}>
        <View style={cardStyles.left}>
          <View style={cardStyles.avatar}>
            <Image
              style={cardStyles.avatar}
              source={{
                uri: item.attrValues.anchorImage || ''
              }}
            />
          </View>
          <View style={cardStyles.avatarIconWrap}>
            <Image
              style={cardStyles.avatarIcon}
              source={{
                uri: item.attrValues.rankIcon || ''
              }}
            />
            <Text
              style={[
                cardStyles.avatarIndex,
                index < 3
                  ? {
                      color: RANK_COLOR[index]
                    }
                  : {}
              ]}
            >
              {item.rank.rank}
            </Text>
          </View>
          {isLiving && <View style={cardStyles.liveBorder} />}
        </View>
        <View style={cardStyles.right}>
          <View style={cardStyles.titleLine}>
            <Text style={cardStyles.title} numberOfLines={1}>
              {item.attrValues.anchorName}
            </Text>
            <View style={cardStyles.hot}>
              <Image
                source={{
                  uri: 'https://p0.meituan.net/travelcube/2e8b2f11a27e41ed925bba3c6c02587b805.png'
                }}
                style={cardStyles.hotIcon}
              />
              <Text style={cardStyles.hotText}>{item.statisticValues.rankText}</Text>
            </View>
          </View>
          <View style={cardStyles.fansLine}>
            <Text style={cardStyles.fansText}>
              {item.attrValues.cityName} | {item.attrValues.followers}
            </Text>
          </View>
          {isLiving || isPlayback ? (
            <View style={cardStyles.liveStatusLine}>
              {isPlayback ? (
                <Image
                  style={[cardStyles.liveTag, { width: 31 }]}
                  source={{
                    uri: 'https://p0.meituan.net/travelcube/6cd5c3359cb41f85542cd91aa6b2d462988.png'
                  }}
                />
              ) : (
                <ImageBackground
                  source={{
                    uri: 'https://p0.meituan.net/travelcube/35b070b42b6a58561cd3cd85a7c354ca6327.png'
                  }}
                  style={[cardStyles.liveTag]}
                >
                  <View
                    style={{
                      height: 16,
                      flexDirection: 'row',
                      // justifyContent: 'center',
                      alignItems: 'center'
                    }}
                  >
                    <Image
                      style={[cardStyles.liveingIcon]}
                      source={{
                        uri: 'https://p1.meituan.net/travelcube/fdcb956bfd4d781192e91b0c67460d6728497.gif'
                      }}
                    />
                    <Text style={cardStyles.livingText}>直播中</Text>
                  </View>
                </ImageBackground>
              )}
              <View style={[cardStyles.liveStatusTextWrap, isPlayback ? { marginLeft: 22 } : {}]}>
                <Text style={[cardStyles.liveStatusText]}>{item.attrValues.liveTitle}</Text>
              </View>
            </View>
          ) : null}
          <View style={[cardStyles.locLine, isLiving || isPlayback ? { marginTop: 7 } : {}]}>
            <View style={cardStyles.shopWrap}>
              <Image
                style={{
                  width: 12,
                  height: 12
                }}
                source={{
                  uri: 'https://p0.meituan.net/travelcube/68d9a1684c5bce4505243eb0368db3761185.png'
                }}
              />
              <Text numberOfLines={1} style={cardStyles.shopText}>
                {item.shopInfo.shopName}
              </Text>
            </View>
            <Text numberOfLines={1} style={cardStyles.locText}>
              {item.shopInfo.regionName} {item.shopInfo.distance || ''}
            </Text>
          </View>
        </View>
      </View>
    </View>
  )
}

export const RankModule: React.FC<Props> = props => {
  const { info } = props
  const { rankList } = info || {}

  const [isEmpty, setIsEmpty] = useImmer(false)

  useEffect(() => {
    setIsEmpty(!rankList?.length)
  }, [rankList, setIsEmpty])

  const renderItem = useCallback(renderCardItem, [])

  const renderEmptyView = useCallback(() => {
    return (
      <View
        style={{
          height: 300,
          marginHorizontal: 12,
          alignItems: 'center',
          backgroundColor: '#ffffff',
          borderBottomRightRadius: 12,
          borderBottomLeftRadius: 12
        }}
      >
        <Image
          style={{
            width: 150,
            height: 133,
            marginTop: 50,
            alignSelf: 'center'
          }}
          source={{
            uri: 'https://p0.meituan.net/travelcube/968816b6abcf5106fccbfa078ccd247f68725.png'
          }}
        />
        <Text
          style={{
            color: '#222222',
            fontFamily: 'PingFang SC',
            fontWeight: '600',
            fontSize: 16
          }}
        >
          该榜单暂无上榜手艺人
        </Text>
      </View>
    )
  }, [])

  const keyExtractor = useCallback((item, index: number) => {
    return `${index}`
  }, [])

  return (
    <MCListModule
      // headerView={renderTab()}
      backgroundColor="transparent"
      // paddingHorizontal={0}
      paddingHorizontal={12}
      gapBottom={10}
      data={rankList} // 必要参数：数据源
      renderItem={renderItem} // 必要参数：列表项渲染方法
      keyExtractor={keyExtractor} // 必要参数：列表项唯一标识
      reuseIdentifierExtractor={'NormalListItem'} // 性能优化：列表项复用标识
      estimatedHeightExtractor={165} // 性能优化：列表项预估高度
      emptyView={renderEmptyView()} // 空视图
      isEmpty={isEmpty}
      emptyMessage={'该榜单暂无上榜手艺人'}
      onItemClick={item => {
        openUrl(item.detailUrl)
      }}
    />
  )
}

const cardStyles = StyleSheet.create({
  wrap: {
    backgroundColor: '#F8FAFB',
    height: 115,
    paddingVertical: 4.5
  },
  content: {
    height: 105,
    // borderRadius: 12,
    padding: 12,
    backgroundColor: '#ffffff',
    flexDirection: 'row'
    // alignItems: 'center'
  },
  firstItem: {
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0
  },
  left: {
    marginRight: 11
  },
  liveBorder: {
    position: 'absolute',
    left: -2,
    top: -2,
    width: 80,
    height: 80,
    borderRadius: 80,
    borderWidth: 1,
    borderColor: '#FF4B10'
  },
  avatar: {
    width: 76,
    height: 76,
    borderRadius: 76,

    overflow: 'hidden'
  },
  avatarIconWrap: {
    // backgroundColor: 'red',
    width: 29,
    height: 33,
    position: 'absolute',
    alignItems: 'center',
    top: -12,
    left: 0,
    zIndex: 1
  },
  avatarIcon: {
    // zIndex: 9,
    width: 29,
    height: 33,
    position: 'absolute',
    bottom: 0,
    right: 0
  },
  avatarIndex: {
    marginTop: 10,
    marginLeft: 2,
    color: '#878699',
    fontFamily: 'MTfin2.0',
    fontSize: 14
  },
  topIcon: {},
  liveStatus: {},
  right: {
    flex: 1
  },
  titleLine: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 18
  },
  title: {
    flex: 1,
    width: '100%',
    color: '#222222',
    fontWeight: '600',
    fontSize: 15,
    lineHeight: 18
  },
  hot: {
    flexBasis: 100,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end'
  },
  hotIcon: {
    width: 12,
    height: 12
  },
  hotText: {
    textAlign: 'right',
    color: '#FF4B10',
    fontWeight: '500',
    fontSize: 12,
    height: 18,
    lineHeight: 18
  },
  fansLine: {
    marginTop: 6,
    flexDirection: 'row',
    alignItems: 'center'
  },
  fansText: {
    fontSize: 12,
    lineHeight: 13
  },
  liveStatusLine: {
    marginTop: 6,
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
    justifyContent: 'center',
    // backgroundColor: '#eeeeee',
    height: 16
  },
  liveTag: {
    position: 'absolute',
    left: 0,
    width: 52.5,
    height: 16
  },
  liveingIcon: {
    marginHorizontal: 1,
    width: 12,
    height: 12
  },
  livingText: {
    color: '#FFFFFF',
    fontFamily: 'PingFang SC',
    fontWeight: '500',
    fontSize: 10
  },
  liveStatusTextWrap: {
    height: 16,
    marginLeft: 45,
    paddingRight: 3,
    paddingLeft: 6,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: '#FF2727',
    borderRadius: 3,
    // alignItems: 'center',
    justifyContent: 'center'
  },
  liveStatusText: {
    fontSize: 10,
    // height: 12,
    // lineHeight: 15,

    color: '#FF2727',
    fontFamily: 'PingFang SC'
  },
  locLine: {
    marginTop: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  shopWrap: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center'
  },
  shopText: {
    width: '100%',
    color: '#999999',
    fontSize: 12,
    lineHeight: 14.5
  },
  locText: {
    marginLeft: 10,
    flexBasis: 100,
    color: '#999999',
    fontSize: 12,
    textAlign: 'right',
    lineHeight: 14.5
  },
  locIcon: {
    width: 12,
    height: 12,
    marginRight: 2
  }
})
