import React from 'react'
import { MCModule } from '@nibfe/doraemon-practice'
import { StyleSheet, View, TouchableOpacity, Dimensions, Text } from '@mrn/react-native'
import { chunk } from 'lodash'
import { commonStyles } from 'src/lib/commonStyle'
import { EffectRes, TabInfoDTO } from 'src/types'
import LX from '@analytics/mrn-sdk'

interface Props {
  info: EffectRes
  index: number
  change: (item: TabInfoDTO, idx: number) => void
}
interface ItemProps {
  item: {
    label: string
    text: string
  }
}

const ScreenWidth = Dimensions.get('window').width

export const LiveEffect: React.FC<Props> = props => {
  // const timeList = ['上一场', '本周', '本月']
  // const list = [
  //   [
  //     {
  //       label: '累计直播时长',
  //       text: '4小时37分钟'
  //     },
  //     {
  //       label: '最高观看人数',
  //       text: '670人'
  //     },
  //     {
  //       label: '参与互动人数',
  //       text: '279人'
  //     }
  //   ],
  //   [
  //     {
  //       label: '净增粉丝数',
  //       text: '1218个'
  //     },
  //     {
  //       label: '交易订单金额',
  //       text: '67.14w'
  //     },
  //     {
  //       label: '核销订单金额',
  //       text: '43.7w'
  //     }
  //   ]
  // ]
  const { info, index, change } = props

  const { periodTabInfoList = [], statisticList } = info ?? {}
  const list = chunk(statisticList, 3)

  const lxMv = () => {
    periodTabInfoList.map((i, index) => {
      LX.moduleView({
        bid: 'b_gc_8j4sv6en_mv',
        pageInfoKey: 'mrn-beauty-craftsman-live-center',
        valLab: {
          index,
          title: i.title
        }
      })
    })
  }

  return (
    <MCModule backgroundColor="transparent" paddingHorizontal={0} onExpose={lxMv}>
      <View style={{ backgroundColor: '#F4F4F4' }}>
        <View style={styles.liveWrap}>
          <View style={[commonStyles.flexRowCenter, styles.titleLine]}>
            <Text style={styles.title}>{info?.title || '直播效果'}</Text>
            <View style={commonStyles.flexRowCenter}>
              {periodTabInfoList.map((i, idx) => (
                <TouchableOpacity
                  onPress={() => {
                    change(i, idx)
                    LX.moduleClick({
                      bid: 'b_gc_8j4sv6en_mc',
                      pageInfoKey: 'mrn-beauty-craftsman-live-center',
                      valLab: {
                        index: idx,
                        title: i.title
                      }
                    })
                  }}
                >
                  <Text style={[styles.titleInfo, idx === index ? styles.isSelected : {}]}>
                    {i.title}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          <View style={styles.content}>
            {list.map(line =>
              line.map(item => (
                <View style={styles.item}>
                  <Text style={styles.itemLabel}>{item.title}</Text>
                  <Text style={styles.itemValue}>{item.value}</Text>
                </View>
              ))
            )}
          </View>
          <Text style={styles.tips}>
            {info?.moreDetailDesc || '更多详情数据可去“美团直播-数据分析”查看'}
          </Text>
        </View>
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  liveWrap: {
    backgroundColor: '#fff',
    margin: 12,
    padding: 12,
    paddingBottom: 15,
    borderRadius: 12
  },
  titleLine: {
    height: 19,
    marginBottom: 12,
    justifyContent: 'space-between'
  },
  title: {
    fontWeight: '600',
    fontSize: 16,
    color: 'black'
  },
  titleInfo: {
    marginLeft: 14,
    color: '#000000',
    fontSize: 12
  },
  isSelected: {
    color: '#FF6633'
  },
  content: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between'
  },
  item: {
    borderRadius: 10,
    marginBottom: 5,
    width: (ScreenWidth - 58) / 3,
    height: 61,
    backgroundColor: '#F8FAFB',
    justifyContent: 'center',
    alignItems: 'center'
  },
  itemLabel: {
    color: '#111111',
    fontFamily: 'PingFang SC',
    // fontWeight: '500',
    fontSize: 12.5,
    lineHeight: 15
  },
  itemValue: {
    marginTop: 2,
    color: '#131313',
    // fontFamily: 'DIN',
    fontWeight: '700',
    fontSize: 15,
    lineHeight: 18.5,
    letterSpacing: 0,
    textAlign: 'center'
  },
  tips: {
    marginTop: 7,
    color: '#979797',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 11,
    lineHeight: 13
  }
})
