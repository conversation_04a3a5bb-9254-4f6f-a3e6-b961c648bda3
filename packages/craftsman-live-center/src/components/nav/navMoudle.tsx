import {
  StyleSheet,
  View,
  TouchableWithoutFeedback,
  Image,
  Text,
  Animated
} from '@mrn/react-native'
import { Icon } from '@nibfe/gc-ui'
import React from 'react'

import { getRecommendHeight, getRecommendPaddingTop } from '@nibfe/dm-navigation'

import { pageRouterClose } from '@mrn/mrn-utils'
// import NavigationBar from '@max/leez-navigation-bar'
// import LText from '@max/leez-text'

interface Props {
  scrollY: Animated.Value
  title: string
}

const NavMoudle = ({ scrollY, title }: Props) => {
  const back = () => {
    pageRouterClose()
  }

  const BottomButtons = () => {
    return (
      <View style={styles.row}>
        <TouchableWithoutFeedback onPress={back}>
          <View>
            <Image
              style={styles.backImage}
              source={{
                uri: 'https://p1.meituan.net/travelcube/10cd6a60fa8c091bc8092d22b4db70122744.png'
              }}
            />
          </View>
        </TouchableWithoutFeedback>
        {/* <Text style={styles.title}>{'页面标题'} </Text>
      <View style={styles.right} /> */}
      </View>
    )
  }

  // 上层按钮
  const TopButtons = () => {
    return (
      <Animated.View
        style={[
          styles.topButtons,
          {
            opacity: scrollY.interpolate({
              inputRange: [0, 50],
              outputRange: [0, 1]
            })
          }
        ]}
      >
        <View style={styles.row}>
          <TouchableWithoutFeedback onPress={back}>
            <Icon name="back" tintColor="black" />
          </TouchableWithoutFeedback>
          <Text style={styles.title}>{title}</Text>
          <View style={{ width: 20 }} />
        </View>
      </Animated.View>
    )
  }

  return (
    <View style={styles.wrap}>
      {/* {Platform.OS === 'android' ? <StatusBar backgroundColor="transparent" translucent /> : null} */}
      {BottomButtons()}
      {TopButtons()}
    </View>
  )
}

const styles = StyleSheet.create({
  wrap: {
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 20
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 15,
    marginTop: getRecommendPaddingTop(),
    height: getRecommendHeight() - getRecommendPaddingTop()
  },
  topButtons: {
    height: getRecommendHeight(),
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff'
  },

  backImage: {
    width: 29,
    height: 29
  },
  title: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 16
  },
  right: {}
})

export default NavMoudle
