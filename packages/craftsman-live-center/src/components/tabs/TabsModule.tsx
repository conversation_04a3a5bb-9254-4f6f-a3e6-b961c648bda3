/* eslint-disable react-hooks/exhaustive-deps */
import React, { useMemo } from 'react'
import Tab from '@max/leez-tab'
import { MCModule } from '@nibfe/doraemon-practice'
import { View, Text } from '@mrn/react-native'
interface Props {
  activeIndex1: number
  selectTab: (item: number) => void
  tabsData: any
}
export const TabsModule: React.FC<Props> = props => {
  const { selectTab, activeIndex1, tabsData } = props
  const dataNormal = [
    {
      title: '手艺人直播',
      titleTextStyle: { color: '#000000' }
    },
    {
      title: '店播分享',
      titleTextStyle: { color: '#000000' }
    }
  ]

  const changeLiveTab = (index: number) => {
    selectTab(index)
  }

  const renderDom = useMemo(() => {
    if (tabsData?.length > 1 || tabsData?.length < 1) {
      return (
        <Tab
          activeIndex={activeIndex1}
          onItemPress={changeLiveTab}
          data={dataNormal}
          enableAnimation={false}
          mode="justify"
          onScroll={() => {}}
        />
      )
    } else {
      return (
        <View style={{ marginTop: 10 }}>
          <Text
            style={{
              color: '#111111',
              fontSize: 18,
              fontWeight: '600'
            }}
          >
            店播分享
          </Text>
        </View>
      )
    }
  }, [tabsData, selectTab, activeIndex1])
  return (
    <MCModule backgroundColor='transparent' paddingLeft={0} paddingRight={0}>
      <View style={{
        width: '100%',
        height: 44.5,
        backgroundColor: '#F4F4F4',
        paddingHorizontal: 20,
        paddingTop: 5
      }}>{renderDom}</View>
    </MCModule>
  )
}
