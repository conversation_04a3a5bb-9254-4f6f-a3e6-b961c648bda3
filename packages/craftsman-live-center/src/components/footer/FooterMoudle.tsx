import React from 'react'
import { View, StyleSheet, Text } from '@mrn/react-native'
import { getInset } from '@mrn/react-native-safe-area-view'
import { MCModule } from '@nibfe/doraemon-practice'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
// import Shadow from '@max/leez-shadow'
import { openUrl } from '@mrn/mrn-utils'
import LButton from '@max/leez-button'

interface Props {
  rankText: string
  startLiveUrl: string
}

export const FooterModule: React.FC<Props> = props => {
  const { rankText = '', startLiveUrl = '' } = props
  const onPress = () => {
    startLiveUrl && openUrl(startLiveUrl)
  }
  return (
    <MCModule
      backgroundColor="transparent"
      paddingLeft={0}
      paddingRight={0}
      hoverType="alwaysHoverBottom"
      hoverOffset={0}
    >
      {rankText && rankText.length ? (
        <LinearGradient style={styles.tipWrap} colors={['#00000000', '#000000CC']}>
          <Text style={styles.tips}>{rankText}</Text>
        </LinearGradient>
      ) : null}
      <View style={styles.container}>
        <LButton style={{ width: '100%' }} onPress={onPress} text={'立即直播'} />
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingTop: 12,
    paddingBottom: 8,
    paddingHorizontal: 14,
    height: 64 + getInset('bottom'),
    backgroundColor: '#F4F4F4'
  },
  left: {},
  backImage: {
    width: 24,
    height: 24
  },
  title: {
    fontWeight: '500',
    fontSize: 16
  },
  right: {},
  tipWrap: {
    height: 80,
    paddingBottom: 16,
    alignItems: 'center',
    justifyContent: 'flex-end'
  },
  tips: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 15
  },
  btn: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 44,
    width: '100%',
    borderRadius: 23
  },
  btnText: {
    color: '#222222',
    fontWeight: '500',
    fontSize: 18
  }
})
