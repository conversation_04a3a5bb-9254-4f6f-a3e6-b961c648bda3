/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useCallback } from 'react'
import { useImmer } from 'use-immer'
import { EffectReq, EffectRes, InfoResponse, RankRes, RankReq } from '../types'
import { getCraftsmanInfo, getEffect, getRanking, getLiveTab } from '../api'
import MRNKNB from '@mrn/mrn-knb'
import { pageRouterClose } from '@mrn/mrn-utils'

export const useFetchHooks = ({ setActiveIndex1 }) => {
  const [craftsmanInfo, setCraftsmanInfo] = useImmer<InfoResponse>({
    // @ts-ignore
    headInfo: {
      name: '',
      anchorImage: '',
      backgroundImage: '',
      medalList: []
    },
    simpleStatisticModule: {
      statisticList: []
    }
  })

  const [effectInfo, setEffectInfo] = useImmer<EffectRes>({
    title: '',
    moreDetailDesc: '',
    periodTabInfoList: [],
    statisticList: []
  })

  const [rankData, setRankData] = useImmer<RankRes>({
    title: '',
    curRankText: '',
    moreUrl: '',
    startLiveUrl: '',
    periodTabInfoList: [],
    rankTypeTabInfoList: [
      {
        key: '',
        title: '',
        currentMatch: true
      }
    ],
    rankList: []
  })

  const [tabsData, setTabsData] = useImmer([])

  const _getEffect = useCallback(
    (_params: EffectReq) => {
      getEffect(_params).then(res => {
        setEffectInfo(res)
      })
    },
    [setEffectInfo]
  )
  const getRankList = useCallback(
    (_params: RankReq) => {
      getRanking(_params).then(res => {
        setRankData(res)
      })
    },
    [setRankData]
  )

  const getTab = useCallback(() => {
    getLiveTab().then(res => {
      // @ts-ignore
      setTabsData(res?.tabInfoList)
      // @ts-ignore
      if (res?.tabInfoList?.length > 1) {
        setActiveIndex1(0)
      } else {
        setActiveIndex1(1)
      }
    })
  }, [])

  const init = useCallback(() => {
    getCraftsmanInfo().then(res => {
      setCraftsmanInfo(res)
    })

    _getEffect({
      periodTabKey: ''
    })

    getRankList({
      periodTabKey: '',
      rankTypeTabKey: ''
    })
  }, [setCraftsmanInfo, _getEffect, getRankList])

  useEffect(() => {
    MRNKNB.getUserInfo({
      success: (user: any) => {
        if (user.userId === '-1') {
          MRNKNB.login({
            success: () => {
              getTab()
              init()
            },
            fail: e => {
              console.error(e)
              setTimeout(() => {
                pageRouterClose()
              }, 500)
            }
          })
        }
      }
    })
    getTab()
    init()
  }, [init])

  return {
    craftsmanInfo,
    effectInfo,
    rankData,
    _getEffect,
    getRankList,
    tabsData
  }
}
