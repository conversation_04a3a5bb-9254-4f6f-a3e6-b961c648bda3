// 禁用风控 //disableRisk即 SAKBaseModel中disableRisk // iOS MRN 2.5.3 支持
import { request } from '@mrn/mrn-utils'
import Dialog from '@max/leez-dialog'
import {
  InfoResponse,
  EffectRes,
  EffectReq,
  RankReq,
  RankRes,
  postShopLiveType,
  postShopLiveShareType,
  ShopLiveListType
} from 'src/types'

const errorHandle = error => {
  const title = error.message || '网络异常，请稍后重试'
  const dialogInstance = Dialog.open({
    title: title,
    showCancelButton: false,
    showConfirmButton: false,
    showCloseIcon: false,
    maskClosable: true,
    onClosePress: () => {
      dialogInstance.close()
    }
  })
}

// 示例 request 用法
const requestConfig = {
  url: '',
  method: 'GET',
  baseURL: 'http://m.dianping.com',
  // baseURL: 'http://m.51ping.com', // 调试用
  params: {},
  // // 模拟登陆用
  headers: {
    // cookie: 'testUserId=**********;'
  },
  options: {
    disableRisk: false,
    registerCandyHost: false
  }
}

export const getCraftsmanInfo: () => Promise<InfoResponse> = () => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/technician/workspace/live/info'
  })
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.msg)
      }
    })
    .catch(error => {
      errorHandle(error)
      console.error(`getCraftsmanInfo:${error}`)
    })
}

export const getEffect: (params: EffectReq) => Promise<EffectRes> = (params: EffectReq) => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/technician/workspace/live/effects',
    params
  })
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.msg)
      }
    })
    .catch(error => {
      console.error(`getEffect:${error}`)
    })
}

export const getRanking: (params: RankReq) => Promise<RankRes> = (params: RankReq) => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/technician/workspace/live/ranking',
    params
  })
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.msg)
      }
    })
    .catch(error => {
      console.error(`getRanking:${error}`)
    })
}

/**
 * 店播分享列表
 * @param params
 * @returns
 */
export const postShopLive: (params: postShopLiveType) => Promise<postShopLiveType> = (
  params: postShopLiveType
) => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/technician/workspace/live/shopLive',
    params,
    method: 'POST',
    headers: {
      ...requestConfig.headers,
      'Content-Type': 'application/json'
    }
  })
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.msg)
      }
    })
    .catch(error => {
      console.error(`postShopLive:${error}`)
    })
}

/**
 * 分享
 */
export const postShopLiveShare: (
  params: postShopLiveShareType
) => Promise<postShopLiveShareType> = (params: postShopLiveShareType) => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/technician/workspace/live/shopLive/share',
    params,
    method: 'POST',
    headers: {
      ...requestConfig.headers,
      'Content-Type': 'application/json'
    }
  })
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.msg)
      }
    })
    .catch(error => {
      console.error(`postShopLiveShare:${error}`)
    })
}

/**
 * .直播分享明细-头部
 */
export const postShopLiveDetailHead: (
  params: postShopLiveShareType
) => Promise<postShopLiveShareType> = (params: postShopLiveShareType) => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/technician/workspace/live/shopLive/detail/head',
    params,
    method: 'POST',
    headers: {
      ...requestConfig.headers,
      'Content-Type': 'application/json'
    }
  })
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.msg)
      }
    })
    .catch(error => {
      console.error(`postShopLiveDetailHead:${error}`)
    })
}

/**
 * 直播分享明细-订单列表
 */
export const postShopLiveDetailOrderList: (
  params: ShopLiveListType
) => Promise<ShopLiveListType> = (params: ShopLiveListType) => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/technician/workspace/live/shopLive/detail/orderList',
    params,
    method: 'POST',
    headers: {
      ...requestConfig.headers,
      'Content-Type': 'application/json'
    }
  })
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.msg)
      }
    })
    .catch(error => {
      console.error(`postShopLiveDetailOrderList:${error}`)
    })
}

/**
 * 直播中心Tab
 */
export const getLiveTab: () => Promise<InfoResponse> = () => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/technician/workspace/live/tabs'
  })
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.data
      } else {
        throw new Error(response.data.msg)
      }
    })
    .catch(error => {
      console.error(`getLiveTab:${error}`)
    })
}
