import { DataSourceType } from 'src/types'

export const showGridData = (item: DataSourceType['shareEffectDTO']) => {
  return [
    [
      {
        label: '分享带来人数',
        value: _formattedPrice(item?.referralCount),
        unit: '人'
      },
      {
        label: '返现订单数',
        value: _formattedPrice(item?.cashbackOrderCount),
        unit: '单'
      }
    ],
    [
      {
        label: '成交订单',
        value: _formattedPrice(item?.orderQuantity),
        unit: '单'
      },
      {
        label: '返现金额',
        value: _formattedPrice(item?.cashbackAmount),
        unit: '元'
      }
    ],
    [
      {
        label: '成交金额',
        value: _formattedPrice(item?.orderAmount),
        unit: '元'
      }
    ]
  ]
}

export const modalShowData = (item: DataSourceType['shareEffectDTO']) => {
  return [
    {
      label: '分享带来人数',
      value: _formattedPrice(item?.referralCount),
      unit: '人'
    },
    {
      label: '实付金额',
      value: _formattedPrice(item?.orderActualPayAmount),
      unit: '元'
    },
    {
      label: '核销订单',
      value: _formattedPrice(item?.orderVerifyCouponQuantity),
      unit: '单'
    },
    {
      label: '返现金额',
      value: _formattedPrice(item?.cashbackAmount),
      unit: '元'
    }
  ]
}

export const orderAmountList = (item: DataSourceType['shareEffectDTO']) => {
  return [
    {
      label: '分享交易人数',
      value: _formattedPrice(item?.referralOrderCount),
      unit: '人'
    },
    {
      label: '核销金额',
      value: _formattedPrice(item?.orderVerifyAmount),
      unit: '元'
    },
    {
      label: '退款订单数',
      value: _formattedPrice(item?.orderCancelQuantity),
      unit: '单'
    }
  ]
}

export const transactionOrRefund = (item: DataSourceType['shareEffectDTO']) => {
  return [
    {
      label: '成交金额',
      value: _formattedPrice(item?.orderAmount),
      unit: ' 元'
    },
    {
      label: '成交订单',
      value: _formattedPrice(item?.orderQuantity),
      unit: ' 单'
    },
    {
      label: '返现订单数',
      value: _formattedPrice(item?.cashbackOrderCount),
      unit: ' 单'
    }
  ]
}

export const _status = (status: number) => {
  switch (status) {
    case 1:
      return '未核销'
    case 2:
      return '已核销'
    case 3:
      return '已退款'
    case 4:
      return '仅部分退款'
    case 5:
      return '仅部分核销'
    case 6:
      return '部分核销部分退款'
    default:
      return null
  }
}

export const _forbiddenButton = (status: number) => {
  if (status === 1 || status === 2) return false
  else return true
}

export const _channel = (type: string) => {
  switch (type) {
    case 'WECHAT_FRIENDS':
      return '微信好友'
    case 'WECHAT_TIMELINE':
      return '微信微信朋友圈'
    default:
      return '复制链接'
  }
}

export const _formattedPrice = (num: number) => {
  if (num > 0) {
    const parts = num.toString().split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    return parts.join('.')
  }
}
