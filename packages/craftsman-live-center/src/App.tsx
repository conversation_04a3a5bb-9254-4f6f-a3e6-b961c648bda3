import React, { useRef } from 'react'
import { AnimatedMCPage } from '@nibfe/doraemon-practice'
import { View, ImageBackground, Dimensions, Animated } from '@mrn/react-native'
import LX from '@analytics/mrn-sdk'
import DialogManager from '@max/leez-dialog-manager'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { BackgroundModule } from 'components/background/BackgroundModule'
import { FooterModule } from 'components/footer/FooterMoudle'
import { LiveEffect } from 'components/liveEffect/LiveEffect'
import { RankModule } from 'components/rank/RankModule'
import { TabModule } from 'components/tab/TabModule'
import { TabsModule } from 'components/tabs/TabsModule'
import NavMoudle from 'components/nav/navMoudle'
import { useFetchHooks } from './hooks/fetchHooks'
import { useImmer } from 'use-immer'
import { TabInfoDTO } from './types'
import {
  Provider as ThemeProvider,
  APPDP_THEME_GCUI,
  APPMT_THEME_GCUI
} from '@nibfe/theme-provider-lighter'
import { MTDProvider } from '@ss/mtd-react-native'
import { isMT } from '@mrn/mrn-gc-utils'
import { SowingStoreList } from 'components/SowingStoreList'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'

const App = () => {
  const [effectIdx, setEffectIdx] = useImmer(0)

  const [timeTabIndex, setTimeTabIndex] = useImmer(0)
  const [rankTypeTabIndex, setRankTypeTabIndex] = useImmer(0)
  const [activeIndex1, setActiveIndex1] = useImmer(1)

  const { craftsmanInfo, effectInfo, rankData, _getEffect, getRankList, tabsData } = useFetchHooks({
    setActiveIndex1
  })

  const changeEffect = (item: TabInfoDTO, idx: number) => {
    setEffectIdx(idx)
    _getEffect({
      periodTabKey: item.key
    })
  }

  const changeRankTab = (timeTabIndex: number, rankTabIndex: number) => {
    setTimeTabIndex(timeTabIndex)
    setRankTypeTabIndex(rankTabIndex)
    getRankList({
      periodTabKey: rankData.periodTabInfoList[timeTabIndex].key,
      rankTypeTabKey: rankData.rankTypeTabInfoList[rankTabIndex].key
    })
  }

  const selectTab = (index: number) => {
    setActiveIndex1(index)
    if (index === 1) {
      lxTrackMGEClickEvent('gc', 'b_gc_7mu71koa_mc', 'c_gc_6xdi4ie9', {
        tech_id: craftsmanInfo?.headInfo?.techId ?? '',
        tab_name: '店播分享'
      })
    }
  }
  const scrollY = useRef(new Animated.Value(0)).current
  return (
    <>
      <View style={{ flex: 1 }}>
        <AnimatedMCPage
          onAppear={() => {
            LX.pageView({
              category: 'gc',
              cid: 'c_gc_ronvxdn2',
              pageInfoKey: 'mrn-beauty-craftsman-live-center',
              valLab: {
                custom: {
                  tech_id: craftsmanInfo?.headInfo?.techId ?? ''
                },
                poi_id: craftsmanInfo?.headInfo?.shopId ?? ''
              }
            })
            lxTrackMGEViewEvent('gc', 'b_gc_7mu71koa_mv', 'c_gc_6xdi4ie9', {
              tech_id: craftsmanInfo?.headInfo?.techId ?? '-9999',
              tab_name: '店播分享'
            })
          }}
          contentBackgroundColor="#F4F4F4"
          onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
            useNativeDriver: true
          })}
          pageTopGap={0}
          pageGap={0}
          separatorLineStyle={{ display: 'hidden-all' }}
          contentBackgroundView={
            <View>
              <LinearGradient
                style={{ height: 252, position: 'absolute', top: 0, left: 0, right: 0, zIndex: 1 }}
                colors={['#00051600', '#050D25']}
              />
              <ImageBackground
                source={{
                  uri: craftsmanInfo?.headInfo?.backgroundImage
                }}
                style={{
                  height: 252,
                  width: Dimensions.get('window').width
                }}
              />
            </View>
          }
          modules={[
            {
              moduleKey: 'BackgroundModule',
              module: craftsmanInfo ? <BackgroundModule info={craftsmanInfo} /> : null
            },
            {
              moduleKey: 'TabsModule',
              module: (
                <TabsModule activeIndex1={activeIndex1} selectTab={selectTab} tabsData={tabsData} />
              )
            },
            {
              moduleKey: 'LiveEffect',
              module:
                activeIndex1 === 0 ? (
                  <LiveEffect index={effectIdx} info={effectInfo} change={changeEffect} />
                ) : null
            },
            {
              moduleKey: 'TabModule',
              module:
                rankData && activeIndex1 === 0 ? (
                  <TabModule
                    info={rankData}
                    timeTabIndex={timeTabIndex}
                    rankTypeTabIndex={rankTypeTabIndex}
                    changeRankTab={changeRankTab}
                  />
                ) : null
            },
            {
              moduleKey: 'SowingStoreList',
              module: activeIndex1 === 1 ? <SowingStoreList /> : null
            },
            {
              moduleKey: 'RankModule',
              module:
                rankData && activeIndex1 === 0 ? (
                  <RankModule load={getRankList} info={rankData} />
                ) : null
            },
            {
              moduleKey: 'FooterModule',
              module:
                rankData && activeIndex1 === 0 ? (
                  <FooterModule
                    rankText={rankData?.curRankText || ''}
                    startLiveUrl={rankData?.startLiveUrl || ''}
                  />
                ) : null
            }
          ]}
        />
        <NavMoudle scrollY={scrollY} title={craftsmanInfo?.headInfo?.name ?? ''} />
      </View>
      <DialogManager />
    </>
  )
}
export default () => {
  return (
    <ThemeProvider theme={isMT() ? APPMT_THEME_GCUI : APPDP_THEME_GCUI}>
      <MTDProvider>
        <App />
      </MTDProvider>
    </ThemeProvider>
  )
}
