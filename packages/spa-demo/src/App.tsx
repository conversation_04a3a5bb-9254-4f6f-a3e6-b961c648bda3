import React from 'react'
import { Provider as ReduxProvider } from 'react-redux'
import { createStoreByRootTag } from './store/index'
import RootStack from './routers'

export default class App extends React.Component {
  // 通过这种方式，在每次 APP 初始化的时候创建一个新的 store 避免 store 数据污染
  // 这儿还可以把 this.props.rootTag 传下去根据情景去细化处理 store 等
  private reduxStore = createStoreByRootTag()

  render() {
    return (
      <ReduxProvider store={this.reduxStore} key={Math.random()}>
        <RootStack />
      </ReduxProvider>
    )
  }
}
