export interface ActionValue {
  type: string
}

export interface StateValue {
  counter: {
    num: number
  }
}

export default (state: StateValue = { counter: { num: 1 } }, action: ActionValue) => {
  switch (action.type) {
    case 'INCREMENT':
      const ret = { ...state }
      ret.counter.num += 1
      return ret
    case 'DECREMENT':
      const s = { ...state }
      s.counter.num -= 1
      return s
    default:
      return state
  }
}
