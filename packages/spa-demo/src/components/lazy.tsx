// 包装一个简单的 Lazy Load Component 的方法

import React, { Suspense, lazy } from 'react'
import { View } from '@mrn/react-native'

export class DefLoading extends React.Component {
  render() {
    return <View />
  }
}

export default function ({ loader, loading }: { loader: Function; loading?: any }) {
  const Loading = loading || DefLoading
  const LazyComp = lazy(async () => await loader())
  class LazyReactComponentScreen extends React.Component {
    render() {
      return (
        <Suspense fallback={<Loading />}>
          <LazyComp {...this.props} />
        </Suspense>
      )
    }
  }

  return LazyReactComponentScreen
}
