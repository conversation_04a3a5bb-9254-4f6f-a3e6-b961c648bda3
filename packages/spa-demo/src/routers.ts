import { createStackNavigator } from '@mrn/react-navigation'

// 懒加载提升首屏性能
import lazy from './components/lazy'

const rootStack = createStackNavigator(
  {
    Home: {
      screen: lazy({
        loader: () => import('./pages/home/<USER>')
      }),
      navigationOptions: () => ({
        title: 'Home',
        backOptions: {
          showBack: true
        }
      })
    },
    Counter: {
      screen: lazy({
        loader: () => import('./pages/counter/CounterContainer')
      })
    }
  },
  {}
)

export default rootStack
