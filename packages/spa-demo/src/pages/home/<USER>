import React from 'react'
import { StyleSheet, Text, View, Button } from '@mrn/react-native'
import { NavigationScreenProp, NavigationState, NavigationParams } from '@mrn/react-navigation'

interface Props {
  navigation: NavigationScreenProp<NavigationState, NavigationParams>
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center'
  }
})

export default (props: Props) => (
  <View style={styles.container}>
    <Text>Open up App.js to start 6666 on your app!</Text>
    <Text>Changes you make will automatically reload.</Text>
    <Text>Shake your phone to open the developer menu.</Text>
    <Button title="Go to Counter" onPress={() => props.navigation.navigate('Counter')} />
  </View>
)
