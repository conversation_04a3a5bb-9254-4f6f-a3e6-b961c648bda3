import React, { Component } from 'react'
import { Button, View, Text, StyleSheet } from '@mrn/react-native'

export interface PropsValue {
  value: number
  onIncrement(): void
  onDecrement(): void
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center'
  }
})

class Counter extends Component<PropsValue> {
  incrementIfOdd = () => {
    if (this.props.value % 2 !== 0) {
      this.props.onIncrement()
    }
  }

  incrementAsync = () => {
    setTimeout(this.props.onIncrement, 1000)
  }

  render() {
    const { value, onIncrement, onDecrement } = this.props
    return (
      <View style={styles.container}>
        <Text>Clicked: {value} times</Text>
        <Button title="+" onPress={onIncrement} />
        <Button title="-" onPress={onDecrement} />
        <Button title="Increment if odd" onPress={this.incrementIfOdd} />
        <Button title="Increment async" onPress={this.incrementAsync} />
      </View>
    )
  }
}

export default Counter
