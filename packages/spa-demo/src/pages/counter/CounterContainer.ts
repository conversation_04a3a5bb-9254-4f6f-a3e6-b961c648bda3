import { connect } from 'react-redux'
import Counter from './Counter'
import { StateValue } from '../../store/reducers/index'
import actions from '../../store/actions/index'

const { INCREMENT, DECREMENT } = actions

const mapStateToProps = (state: StateValue) => {
  return {
    value: state.counter.num
  }
}

const mapDispatchToProps = (dispatch: any) => {
  return {
    onIncrement: () => {
      dispatch(INCREMENT)
    },
    onDecrement: () => {
      dispatch(DECREMENT)
    }
  }
}

export default connect(mapStateToProps, mapDispatchToProps)(Counter)
