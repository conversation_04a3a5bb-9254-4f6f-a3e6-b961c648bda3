import React, { FC } from 'react'
import { MCPage } from '@nibfe/doraemon-practice'
import { View } from '@mrn/react-native'
import {
  Provider as ThemeProvider,
  APPDP_THEME_GCUI,
  APPMT_THEME_GCUI
} from '@nibfe/theme-provider-lighter'
import { IS_WEB } from '@mrn/mrn-gc-utils'
import { DetailScreenProps } from './types'
import { NarBarView } from './components/nav/NavBar'
import { ListModule } from './components/list'
import { getStatusBarHeight } from '@nibfe/dm-navigation'
import TextCompatUtils from './utils/TextCompatUtils'
import { isMT } from 'utils/platform'
// 模块入口处执行一次全局配置兼容方法
TextCompatUtils.fixTextCompat() // 修复部分Android机型文本截断问题、iOS默认字体

interface NailDetailProps {
  screenProps: DetailScreenProps
}

const NailDetail: FC<NailDetailProps> = ({ screenProps }) => {
  return (
    <View style={{ flex: 1, paddingTop: getStatusBarHeight(), backgroundColor: 'black' }}>
      {!IS_WEB && (
        <NarBarView
          style={{
            position: 'absolute',
            zIndex: 10,
            backgroundColor: 'transparent',
            left: 0,
            right: 0,
            top: getStatusBarHeight()
          }}
        />
      )}
      <MCPage
        enablePullRefresh={false}
        paddingHorizontal={0}
        pageTopGap={0}
        separatorLineStyle={{ display: 'hidden-all' }}
        contentBackgroundColor={'black'}
        mptInfo={{
          cid: 'c_gc_83vkocxv',
          category: 'gc',
          labs: {
            cat_id: '39'
          }
        }}
        modules={[
          {
            moduleKey: 'list_module',
            module: <ListModule screenProps={screenProps} />
          }
        ]}
      />
    </View>
  )
}

const App: React.FC<DetailScreenProps> = props => (
  <ThemeProvider theme={isMT ? APPMT_THEME_GCUI : APPDP_THEME_GCUI}>
    <NailDetail
      screenProps={{
        ...props,
        contentId: props.contentId || '',
        contentType: props.contentType,
        shopId: props.shopId || '',
        platform: Number(props.platform) || (isMT ? 2 : 1),
        unionId: props?.unionId || '',
        cityId: props?.cityId || 0,
        lng: props?.lng,
        lat: props?.lat,
        cursor: props.cursor || '',
        nodeTagIds: props.nodeTagIds || '0',
        limit: Number(props?.limit) || 10,
        offset: Number(props?.offset) || 0
      }}
    />
  </ThemeProvider>
)

export default App
