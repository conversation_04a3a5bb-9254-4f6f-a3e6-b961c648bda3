import { Text, StyleSheet, Platform } from '@mrn/react-native'
import React from 'react'
import get from 'lodash/get'

const styles = StyleSheet.create({
  defaultFontFamily: {
    fontFamily: 'PingFangSC-Regular' // iOS 默认苹方字体
  }
})

const TextStyle = {
  '#333-500': { color: '#000', fontWeight: 'normal' },
  '#666-500': { color: '#333', fontWeight: 'normal' },
  '#333-300': { color: '#666', fontWeight: 'normal' },
  '#666-300': { color: '#999', fontWeight: 'normal' },
  '#999-300': { color: '#AAA', fontWeight: 'normal' },
  '#333333-500': { color: '#000', fontWeight: 'normal' },
  '#666666-500': { color: '#333', fontWeight: 'normal' },
  '#333333-300': { color: '#666', fontWeight: 'normal' },
  '#666666-300': { color: '#999', fontWeight: 'normal' },
  '#999999-300': { color: '#AAA', fontWeight: 'normal' }
}

// 模块入口index.tsx调用一次
export default class TextCompatUtils {
  static fixTextCompat() {
    // 设置默认字体不随系统字体大小变化 防止没有最好加一个try catch
    try {
      // @ts-ignore
      Text.defaultProps = Text.defaultProps || {}
      // @ts-ignore
      Text.defaultProps.allowFontScaling = false
      // @ts-ignore
      Text.defaultProps.includeFontPadding = false
      TextCompatUtils.fixAndroidTextCutOff()
    } catch (error) {
      // error处理
      console.log(error)
    }
  }

  // 兼容iOS未设置字体lineHeight失效 & Android部分机型（小米、Oppo等）文字被截断问题
  // 兼容android端样式与iOS不一致问题
  static fixAndroidTextCutOff() {
    const oldRender = Text.prototype.render
    if (oldRender) {
      Text.prototype.render = function (...args: any[]) {
        const origin = oldRender.call(this, ...args)
        let newStyle
        if (Platform.OS === 'android') {
          const orginColor = get(origin.props, 'style.color', '')
          const orginFontWeight = get(origin.props, 'style.fontWeight', '')
          newStyle = TextStyle[orginColor + '-' + orginFontWeight]
        }
        return React.cloneElement(origin, {
          style: [styles.defaultFontFamily, origin.props.style, newStyle]
        })
      }
    }
  }
}
