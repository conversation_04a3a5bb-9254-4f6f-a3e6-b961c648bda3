import { IS_WEB } from '@mrn/mrn-gc-utils'
import { request } from '@mrn/mrn-utils'
import { RequestParams } from '../types'
import { isMT } from 'utils/platform'

const requestConfig = {
  url: '',
  method: 'GET',
  // baseURL: 'https://m.51ping.com',
  baseURL: isMT ? 'https://g.meituan.com' : 'https://m.dianping.com',
  params: {},
  options: {
    disableRisk: false,
    registerCandyHost: false
  }
}

// 获取美甲款式详情
export function getNailShowDetail(params: RequestParams) {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: IS_WEB
      ? '/gw/medical/content/query_content_style_detail'
      : '/gw/medical/content/query_nail_style_detail',
    params
  })
  return request(_requestConfig)
    .then(response => {
      return response.data
    })
    .catch(error => {
      console.error(`getNailShowDetail:${error}`)
    })
}
