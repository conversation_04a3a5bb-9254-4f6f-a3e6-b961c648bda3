// mrn.config.js 配置文档
// http://mrn.sankuai.com/docs/guide/conf.html#mrn-config-js

module.exports = {
  name: 'mrn-beauty-nail-content-detail',
  main: './index.tsx', // 页面入口地址
  biz: 'gcbu',
  bundleType: 1,
  bundleDependencies: ['@mrn/mrn-base'],
  debugger: {
    //
    moduleName: 'mrn-beauty-nail-content-detail',
    initialProperties: {
      hideNavigationBar: true,
      contentId: '7034493351',
      shopId: '1119470656',
      contentType: 400,
      offset: 0,
      limit: 10
    }
  },
  // 转 H5 配置
  one: {
    appConfig: {
      alias: {
        '@mrn/mrn-utils': '@nibfe/gc-one-api'
      },
      pages: [
        {
          name: 'mrn-beauty-nail-content-detail',
          path: 'index.tsx',
          enableShareAppMessage: true
        }
      ]
    }
  }
}
