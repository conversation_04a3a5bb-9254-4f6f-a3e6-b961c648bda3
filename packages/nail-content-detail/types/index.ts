export interface DetailScreenProps {
  contentId: string // 当前内容ID，从url上取contentId参数值
  contentType: number // 当前内容类型，300-笔记，400评价
  shopId: string // 商户ID，点评侧是点评uuid或点评商户id，美团侧是美团商户id，从url上取shopId参数值
  platform: number // 平台，1-点评，2-美团
  // mrn公共参数
  mrn_component: string

  // 可选参数
  cursor?: string
  nodeTagIds?: string // 快筛项ID
  limit?: number // 分页大小
  offset?: number // 分页内容起始偏移量，从url上取offset参数值
  unionId?: string
  cityId?: number
  lng?: number
  lat?: number
}

export interface RequestParams {
  unionId: string
  contentId: string //作品id
  cityId: number
  platform: number //平台，1是点评，2是美团
  lng: number
  lat: number
  contentType: number ////300-笔记,400评价
  shopId: string //商户id
  inApp: number
  offset: number
  limit: number
  nodeTagIds: string //笔记的筛选项,是筛选项查出来的ids。 查询全部的话，就传0
  cursor: string
}
