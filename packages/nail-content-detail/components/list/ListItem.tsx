/* eslint-disable react-native/no-inline-styles */
import React, { FC, useEffect, useRef, useState, useMemo } from 'react'
import { View, Text, StyleSheet, TouchableOpacity, useWindowDimensions } from '@mrn/react-native'
import { DMImage, GCScrollPageView, GCScrollPageViewRef } from '@nibfe/gc-ui'
import { Tag } from '@nibfe/mrn-materials-lego'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { DetailScreenProps } from '../../types'
import { lxTrackMGEClickEvent, openUrl } from '@mrn/mrn-utils'
import { getWidth } from '@mrn/mrn-gc-utils'
import { isMT } from 'utils/platform'

const BackIconUrl = 'https://p0.meituan.net/ingee/2c5a9eddf6bd2b817e41414b8819a1e2869.png'
const CouponIconUrl = isMT
  ? 'https://p0.meituan.net/travelcube/87ffd9bfa8e354e86f596ae5d78a198e767.png.webp'
  : 'https://p0.meituan.net/travelcube/44390cd04e854c764d6bd6c9ce3e2214734.png.webp'
const foldIconUrl = 'https://p0.meituan.net/travelcube/4e834b7e6fb074a75462f14a2c6bcb7f785.png'
const unFoldIconUrl = 'https://p1.meituan.net/travelcube/9e2ce502a74fdac3d76cbe47665ec6a0787.png'

interface Props {
  screenProps: DetailScreenProps
  item: any
  index: number
  onClickCoupon?: (item: any, index: number) => void
}

export const ListItem: FC<Props> = props => {
  const { width: screenWidth } = useWindowDimensions()
  const [picIndex, setPicIndex] = useState(0)
  const [textFold, setTextFold] = useState(true)
  const scrollViewRef = useRef<GCScrollPageViewRef | null>(null)
  const { item, onClickCoupon, index } = props

  const { tagList, picInfos = [], content = '', message, dealGroup, shop, user } = item

  const cutOff = useMemo(() => {
    let totalWidth = 0,
      line = 1
    const maxWidth = Math.floor(screenWidth - 30)

    if (content && content?.length > 0) {
      for (let i = 0; i < content.length; i++) {
        if (line === 1 && content[i].charCodeAt(0) === 10) {
          line = 2
          totalWidth = maxWidth
        } else if (line === 2 && content[i].charCodeAt(0) === 10) {
          totalWidth = 2 * maxWidth
        } else if (content[i].charCodeAt(0) > 255) {
          //字符编码大于255，说明是双字节字符
          totalWidth += 14
        } else {
          totalWidth += 7
        }
        if (totalWidth > 2 * maxWidth) {
          return i - 1
        }
      }
    } else {
      return 0
    }
  }, [content, screenWidth])

  useEffect(() => {
    scrollViewRef.current && scrollViewRef.current?.startExpose()
    return () => {
      // eslint-disable-next-line react-hooks/exhaustive-deps
      scrollViewRef.current && scrollViewRef.current?.cleanExpose()
    }
  }, [])

  const renderSingleCoupon = () =>
    !!dealGroup?.title && (
      <View style={{ marginBottom: 6 }}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => onClickCoupon?.(item, index)}
          style={{
            height: 19,
            flexDirection: 'row',
            alignItems: 'center'
          }}
        >
          <DMImage
            style={styles.couponIcon}
            source={{
              uri: dealGroup?.icon || CouponIconUrl
            }}
          />
          {!!dealGroup?.promoPrice && (
            <View style={styles.priceWrap}>
              <Text style={styles.priceIcon}>¥</Text>
              <Text style={styles.price}>{Number(dealGroup.promoPrice)}</Text>
            </View>
          )}
          {!!dealGroup?.title && (
            <Text style={styles.couponName} numberOfLines={1}>
              {dealGroup.title}
            </Text>
          )}
          <DMImage
            style={{
              width: 11,
              height: 11,
              marginLeft: 3
            }}
            source={{
              uri: BackIconUrl
            }}
          />
        </TouchableOpacity>
      </View>
    )

  const renderButtonArea = () => {
    return (
      <View style={styles.barContainer}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            flex: 1
          }}
        >
          {!!(shop && shop?.shopName) && (
            <View style={{ marginLeft: 5, flex: 1 }}>
              <Text
                numberOfLines={1}
                style={{
                  fontWeight: '400',
                  fontSize: 12,
                  color: '#222222',
                  lineHeight: 20
                }}
              >
                {shop?.shopName}
              </Text>
              {!!(shop && shop?.star) && (
                <View
                  style={{
                    height: 18,
                    marginTop: 4,
                    flexDirection: 'row',
                    alignItems: 'center'
                  }}
                >
                  <DMImage
                    style={{
                      width: 10,
                      height: 10
                    }}
                    source={{
                      uri: 'https://p0.meituan.net/travelcube/ac4cb73e33e10e844206ef4cc67e23db966.png'
                    }}
                  />
                  <Text
                    numberOfLines={1}
                    style={[
                      styles.shopText,
                      { marginLeft: 3, color: '#D40000', fontFamily: 'MTfin2.0' }
                    ]}
                  >
                    {shop?.star}
                  </Text>
                  {!!(shop && shop?.star && shop?.distance) && <View style={styles.divider} />}
                  {!!(shop && shop?.distance) && (
                    <Text numberOfLines={1} style={styles.shopText}>
                      {shop?.distance}
                    </Text>
                  )}
                </View>
              )}
            </View>
          )}
        </View>
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            shop?.detailUrl && openUrl(shop?.detailUrl)
            lxTrackMGEClickEvent('gc', 'b_gc_tvwqqg3d_mc', 'c_gc_83vkocxv', {
              click_type: 2,
              content_id: item.contentId || '',
              deal_id: item.dealGroup?.id || '',
              distance: item.shop?.distance || '',
              index: index,
              poi_id: item.shop?.shopId || '',
              title: item.content || '',
              type: item.contentType || '',
              type_title: item.message || ''
            })
          }}
        >
          <LinearGradient
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            colors={['#FF7700', '#FF4B10']}
            style={styles.buttonView}
          >
            <Text style={styles.buttonText}>进店咨询</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    )
  }

  const renderBottomArea = () => {
    return (
      <View style={styles.bottomContainer}>
        {/* 推荐词 */}
        {!!message && (
          <View style={{ height: 17, marginBottom: 6 }}>
            <Text style={styles.recommendText} numberOfLines={1}>
              {message}
            </Text>
          </View>
        )}
        {/* 标签 */}
        {Boolean(tagList && tagList?.length > 0) && (
          <View
            style={{
              flexDirection: 'row',
              height: 18,
              marginBottom: 6,
              overflow: 'hidden',
              flexWrap: 'nowrap',
              alignItems: 'center'
            }}
          >
            {tagList.map(
              (tag, tagIndex) =>
                !!tag && (
                  <Tag
                    key={tag}
                    text={tag}
                    size={'large'}
                    tagStyle={{
                      textSize: 11,
                      textColor: '#111111',
                      bgColor: '#F6F6F6',
                      paddingHorizontal: 5,
                      borderRadius: 3
                    }}
                    style={{ marginLeft: tagIndex > 0 ? 5 : 0 }}
                  />
                )
            )}
          </View>
        )}
        {/* 标题 */}
        <View style={{ marginBottom: 6 }}>
          {content && content?.length > cutOff ? (
            <View>
              {textFold ? (
                <Text style={styles.title} numberOfLines={2}>
                  {content.substring(0, cutOff - 3) + '...'}
                </Text>
              ) : (
                <Text style={styles.title}>
                  {content}
                  <Text style={[styles.title, { color: '#fff' }]}>展开</Text>
                </Text>
              )}
              <TouchableOpacity
                style={{
                  position: 'absolute',
                  right: 0,
                  bottom: 0,
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  alignItems: 'center'
                }}
                activeOpacity={1}
                onPress={() => {
                  setTextFold(preState => !preState)
                }}
              >
                <Text
                  style={{
                    color: '#004099',
                    fontSize: 12,
                    fontWeight: '400',
                    fontFamily: 'PingFangSC-Regular',
                    lineHeight: 19.5
                  }}
                >
                  {`${textFold ? '展开' : '收起'}`}
                </Text>
                <DMImage
                  style={{
                    width: 11,
                    height: 11
                    // position: 'absolute'
                  }}
                  source={{
                    uri: textFold ? foldIconUrl : unFoldIconUrl
                  }}
                />
              </TouchableOpacity>
            </View>
          ) : (
            <Text style={styles.title}>{content}</Text>
          )}
        </View>
        {/* 用户信息 */}
        <View
          style={{
            height: 20,
            marginBottom: 6,
            flexDirection: 'row',
            alignItems: 'center'
          }}
        >
          {!!(user && user?.avatarUrl) && (
            <DMImage
              style={{
                width: 20,
                height: 20,
                borderRadius: 20,
                overflow: 'hidden'
              }}
              source={{
                uri: user?.avatarUrl || ''
              }}
            />
          )}
          {!!(user && user?.name) && (
            <Text numberOfLines={1} style={styles.userText}>
              {user?.name}
            </Text>
          )}
          {!!(user && user?.addTime) && (
            <Text numberOfLines={1} style={styles.userText}>
              {`${user?.addTime}`}
            </Text>
          )}
        </View>
        {/* 关联团购 */}
        {renderSingleCoupon()}
        <View style={styles.splitLine} />
        {/* 按钮区域 */}
        {renderButtonArea()}
      </View>
    )
  }

  return (
    <View
      style={[
        styles.container,
        index === 0
          ? undefined
          : {
              marginTop: 10
            }
      ]}
    >
      <View>
        <GCScrollPageView
          ref={scrollViewRef}
          lazy
          loop={false}
          adaptiveHeight
          autoPlay={false}
          keyExtractor={pic => `key_${pic.url}`}
          data={picInfos}
          scrollEventThrottle={16}
          // onExposeItem={(_, index) => {
          //   lxTrackMGEViewEvent('gc', 'b_gc_tuaooncv_mv', 'c_gc_278aj2mu', {
          //     father_index: props.index,
          //     index,
          //     type: '买家秀'
          //   })
          // }}
          renderItem={(pic: any, _index) => (
            <TouchableOpacity
              key={`FeedsItem-${_index}`}
              activeOpacity={1}
              onPress={() => {
                lxTrackMGEClickEvent('gc', 'b_gc_tvwqqg3d_mc', 'c_gc_83vkocxv', {
                  click_type: 0,
                  content_id: item.contentId || '',
                  deal_id: item.dealGroup?.id || '',
                  distance: item.shop?.distance || '',
                  index: index,
                  poi_id: item.shop?.shopId || '',
                  title: item.content || '',
                  type: item.contentType || '',
                  type_title: item.message || ''
                })
              }}
              style={{
                width: '100%',
                height: 500,
                justifyContent: 'center',
                overflow: 'hidden'
              }}
            >
              <DMImage
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'absolute'
                }}
                source={{
                  uri: pic?.backgroundUrl || ''
                }}
              />
              <DMImage
                style={{
                  width: '100%',
                  height: getWidth() * (pic?.width > 0 ? pic?.height / pic?.width : 0.75)
                }}
                source={{
                  uri: pic?.url || ''
                }}
              />
            </TouchableOpacity>
          )}
          onPageChanged={currentIndex => {
            setPicIndex(currentIndex)
          }}
        />
        {/* 指示器 */}
        {Boolean(picInfos && picInfos?.length > 0) && (
          <View
            style={{
              height: 6,
              flexDirection: 'row',
              alignItems: 'center',
              overflow: 'hidden',
              position: 'absolute',
              left: '50%',
              bottom: 10,
              marginLeft: (5 - picInfos?.length * 11) / 2
            }}
          >
            {picInfos.map((_, dotIndex) => (
              <View
                key={`${dotIndex}`}
                style={{
                  width: 6,
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: dotIndex === picIndex ? '#ffffff' : '#ffffff50',
                  marginLeft: dotIndex > 0 ? 5 : 0
                }}
              />
            ))}
          </View>
        )}
        {/* 页码 */}
        {Boolean(picInfos && picInfos?.length > 0) && (
          <View
            style={{
              width: 36.5,
              height: 18,
              backgroundColor: '#00000099',
              borderRadius: 10,
              position: 'absolute',
              right: 10,
              bottom: 10
            }}
          >
            <Text style={{ color: '#fff', fontSize: 12, textAlign: 'center', lineHeight: 18 }}>
              {`${picIndex + 1}/${picInfos?.length}`}
            </Text>
          </View>
        )}
      </View>
      {renderBottomArea()}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    overflow: 'hidden'
  },
  bottomContainer: {
    paddingHorizontal: 15,
    paddingTop: 12
  },
  recommendText: {
    fontWeight: '400',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    color: '#FF4B10',
    lineHeight: 17
  },
  title: {
    fontWeight: '500',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 14,
    color: '#222222',
    lineHeight: 19.5,
    flex: 1
  },
  userText: {
    marginLeft: 4,
    fontWeight: '400',
    fontSize: 11,
    color: '#999999'
  },
  couponIcon: { height: 16, width: 16 },
  priceWrap: {
    flexDirection: 'row',
    marginLeft: 6
  },
  price: {
    fontWeight: '400',
    fontFamily: 'MTfin2.0',
    fontSize: 18,
    lineHeight: 19,
    color: '#FF4B10'
  },
  priceIcon: {
    marginTop: 2.5,
    marginRight: 2,
    fontWeight: '400',
    fontFamily: 'MTfin2.0',
    fontSize: 12,
    color: '#FF4B10'
  },
  couponName: {
    fontWeight: '400',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    color: '#222222',
    marginLeft: 6
  },
  splitLine: {
    width: '100%',
    height: 0.5,
    marginTop: 6,
    backgroundColor: '#E5E5E5'
  },
  barContainer: {
    height: 44,
    marginVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  btnView: {
    width: 40,
    height: 44,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6
  },
  shopText: {
    fontWeight: '400',
    fontSize: 13,
    color: '#666666'
  },
  divider: {
    width: 0.5,
    height: 9.5,
    marginHorizontal: 5,
    backgroundColor: '#EEEEF0'
  },
  buttonView: {
    width: 112,
    height: 44,
    marginLeft: 6,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center'
  },
  buttonText: {
    fontWeight: '600',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 16,
    color: '#FFFFFF'
  }
})
