import React from 'react'
import { View, Text, StyleProp, ViewStyle } from '@mrn/react-native'

export const ListEndView: React.FC<{ style?: StyleProp<ViewStyle>; title?: string }> = ({
  style,
  title = '- 没有更多了 -'
}) => {
  return (
    <View style={[{ alignItems: 'center', marginVertical: 10 }, style]}>
      <Text
        style={{
          fontWeight: '400',
          fontFamily: 'PingFangSC-Regular',
          fontSize: 12,
          color: '#777777',
          letterSpacing: 0,
          textAlign: 'center'
        }}
      >
        {title}
      </Text>
    </View>
  )
}
