import React, { FC, useState, useCallback, useEffect, useMemo } from 'react'
import { MCListModule, MCLoadingStatusString } from '@nibfe/doraemon-practice'
import { ListItem } from './ListItem'
import { DetailScreenProps } from '../../types'
import { ListEndView } from './ListEnd'
import { getCityAndLocation, KNBCityRes, KNBLocationRes } from '../../utils/location'
import { lxTrackMGEClickEvent, openUrl } from '@mrn/mrn-utils'
import { GetUserInfoResult, getUserInfo } from '../../utils/KNBUtils'
import { getNailShowDetail } from '../../apis'
import { IS_WEB } from '@mrn/mrn-gc-utils'

interface Props {
  screenProps: DetailScreenProps
  lxLabs?: { [key: string]: any }
}

interface ListState {
  listData: any[]
  loadingStatus: MCLoadingStatusString
  loadingMoreStatus: MCLoadingStatusString
  isEmpty?: boolean
}

export const ListModule: FC<Props> = ({ screenProps }) => {
  const [listState, setListState] = useState<ListState>({
    listData: [],
    loadingStatus: 'loading',
    loadingMoreStatus: 'done',
    isEmpty: false
  })

  const params = React.useRef({
    unionId: '',
    contentId: screenProps.contentId,
    contentType: screenProps.contentType,
    shopId: screenProps.shopId,
    platform: screenProps.platform,
    limit: screenProps.limit,
    offset: screenProps.offset,
    lng: screenProps.lng,
    lat: screenProps.lat,
    cityId: screenProps.cityId,
    nodeTagIds: screenProps.nodeTagIds,
    cursor: screenProps.cursor,
    inApp: IS_WEB ? 2 : 1
  })

  const setParams = useCallback(
    newParams => {
      params.current = {
        ...params.current,
        ...newParams
      }
    },
    [params]
  )

  const onClickCoupon = useCallback((item: any, index) => {
    lxTrackMGEClickEvent('gc', 'b_gc_tvwqqg3d_mc', 'c_gc_83vkocxv', {
      click_type: 2,
      content_id: item.contentId || '',
      deal_id: item.dealGroup?.id || '',
      distance: item.shop?.distance || '',
      index: index,
      poi_id: item.shop?.shopId || '',
      title: item.content || '',
      type: item.contentType || '',
      type_title: item.message || ''
    })
    item?.dealGroup?.detailUrl && openUrl(item?.dealGroup?.detailUrl)
  }, [])

  const loadData = useCallback(() => {
    setListState(preState => ({
      ...preState,
      loadingStatus: 'loading'
    }))
    getNailShowDetail(params.current)
      .then((res: any) => {
        if (res?.code === 200) {
          const { contentList, hasNextPage, nextPageCursor } = res?.data || {}
          setListState({
            listData: contentList,
            loadingStatus: 'done',
            loadingMoreStatus: hasNextPage ? 'loading' : 'done',
            isEmpty: !(contentList && contentList?.length > 0)
          })
          setParams({
            cursor: nextPageCursor || '',
            offset: params.current?.offset + 1
          })
        }
      })
      .catch(() => {
        setListState(preState => ({
          ...preState,
          loadingStatus: 'fail',
          loadingMoreStatus: 'done',
          isEmpty: true
        }))
      })
  }, [setParams])

  const loadMoreData = useCallback(() => {
    getNailShowDetail({ ...params.current })
      .then((res: any) => {
        if (res?.code === 200) {
          const { contentList, hasNextPage, nextPageCursor } = res?.data || {}
          setListState(preState => ({
            listData: preState.listData?.concat(contentList),
            loadingStatus: 'done',
            loadingMoreStatus: hasNextPage ? 'loading' : 'done'
          }))
          setParams({
            cursor: nextPageCursor || '',
            offset: params.current?.offset + 1
          })
        }
      })
      .catch(() => {
        setListState(preState => ({
          ...preState,
          loadingStatus: 'fail',
          loadingMoreStatus: 'done'
        }))
      })
  }, [setParams])

  const isEnd = useMemo(() => {
    return listState.loadingMoreStatus === 'done'
  }, [listState.loadingMoreStatus])

  useEffect(() => {
    Promise.all([getCityAndLocation(), getUserInfo()]).then(
      ([loc, user]: [KNBCityRes & KNBLocationRes, GetUserInfoResult]) => {
        if (loc && user) {
          setParams({
            lng: loc?.lng,
            lat: loc?.lat,
            cityId: loc?.cityId,
            unionId: user?.unionId
          })
          loadData()
        }
      }
    )
  }, [loadData, setParams])

  return (
    <MCListModule
      backgroundColor="black"
      data={listState.listData}
      isEmpty={listState.isEmpty}
      loadingStatus={listState.loadingStatus}
      onRetryForLoadingFail={loadData}
      onNeedLoadMore={loadMoreData}
      loadingMoreStatus={listState.loadingMoreStatus}
      onRetryForLoadingMoreFail={loadMoreData}
      keyExtractor={item => `key_${item.contentId}`}
      renderItem={(itemData, index) => (
        <ListItem
          key={`item_${itemData.contentId}`}
          item={itemData}
          index={index}
          onClickCoupon={onClickCoupon}
          screenProps={screenProps}
        />
      )}
      // onItemExpose={(item, index: number) => {
      //   lxTrackMGEViewEvent('gc', 'b_gc_9wl51ali_mv', 'c_gc_278aj2mu', { index, type: '买家秀' })
      // }}
      footerView={isEnd ? <ListEndView style={{ marginVertical: 15 }} /> : undefined}
      viewMgeInfoExtractor={(item, index) => {
        return {
          category: 'gc',
          bid: 'b_gc_tvwqqg3d_mv',
          cid: 'c_gc_83vkocxv',
          labs: {
            content_id: item.contentId || '',
            deal_id: item.dealGroup?.id || '',
            distance: item.shop?.distance || '',
            index: index,
            poi_id: item.shop?.shopId || '',
            title: item.content || '',
            type: item.contentType || '',
            type_title: item.message || ''
          }
        }
      }}
    />
  )
}
