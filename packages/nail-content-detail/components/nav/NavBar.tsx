import React, { FC } from 'react'
import {
  View,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  ViewStyle,
  StyleProp
} from '@mrn/react-native'
import { lxTrackMGEClickEvent, pageRouterClose } from '@mrn/mrn-utils'
import { DMImage } from '@nibfe/gc-ui'

const styles = StyleSheet.create({
  navContainer: {
    paddingLeft: 14,
    paddingVertical: 7.5
  },
  navIcon: {
    width: 29,
    height: 29
  }
})

interface Props {
  style?: StyleProp<ViewStyle>
}

export const NarBarView: FC<Props> = ({ style }) => {
  return (
    <View style={[styles.navContainer, style]} pointerEvents="box-none">
      <StatusBar backgroundColor="#000" barStyle="light-content" translucent />
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => {
          lxTrackMGEClickEvent('gc', 'b_gc_cjnplu5u_mc', 'c_gc_278aj2mu', {})
          pageRouterClose()
        }}
      >
        <DMImage
          source={{
            uri: 'https://p0.meituan.net/travelcube/af7d767985f223ae1e528e12eaf3a7055286.png.webp'
          }}
          style={styles.navIcon}
        />
      </TouchableOpacity>
    </View>
  )
}
