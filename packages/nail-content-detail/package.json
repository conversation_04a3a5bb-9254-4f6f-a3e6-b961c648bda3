{"name": "mcone-template-list", "version": "1.0.0", "private": true, "description": "A-MRN-PROJECT", "author": "", "scripts": {"start:web": "mc-one start -p web", "build:web": "mc-one build -p web", "build": "mrn build", "lint": "eslint --ext .ts,.tsx,.js,.jsx,src", "fix": "yarn lint --fix", "start": "mrn start", "api": "mc-api"}, "dependencies": {"@analytics/mrn-sdk": "^1.1.4", "@max/leez-dependencies": "^2.3.35", "@max/mrn-transform-rpx": "^1.0.8", "@mrn/mcc-component-report": "0.0.22", "@mrn/mrn-base": "^3.0.38", "@mrn/mrn-gc-utils": "^0.0.6", "@mrn/mrn-knb": "^0.4.5", "@mrn/mrn-owl": "^1.0.30", "@mrn/mrn-utils": "^1.5.0", "@mrn/react-navigation": "^2.9.23", "@nibfe/doraemon-practice": "1.3.13-web.1", "@nibfe/gc-ui": "^3.2.4", "@nibfe/mc-list-component": "^1.0.4", "@nibfe/mc-one-all-deps": "1.3.0", "@nibfe/mc-one-env": "1.2.12", "@nibfe/mrn-babel-preset": "^3.0.26", "@nibfe/mrn-materials": "^2.3.1", "@nibfe/mrn-materials-lego": "^0.0.10", "@nibfe/theme-provider-lighter": "^1.2.0", "@reduxjs/toolkit": "^1.9.5", "@ss/mtd-react-native": "^0.4.5", "babel-runtime-jsx-plus": "^0.1.5", "sass": "^1.64.1", "use-immer": "^0.8.1"}, "devDependencies": {"@gfe/babel-plugin-react-add-name": "1.0.17", "@mrn/mrn-cli": "^3.0.3", "@nibfe/mrn-babel-preset": "^3.0.26", "@nibfe/prettier-config": "^1.0.0", "eslint": "^7.32.0", "eslint-plugin-prettier": "3.3.1", "react-scripts": "^4.0.3"}, "eslintConfig": {"root": true, "extends": ["plugin:@mrn/eslint-plugin/recommended"]}, "prettier": {"$schema": "http://json.schemastore.org/prettierrc", "printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "jsxSingleQuote": false, "trailingComma": "none", "bracketSpacing": true, "jsxBracketSameLine": false, "arrowParens": "avoid"}, "eslintIgnore": ["dist", "node_modules"], "husky": {"hooks": {"pre-commit": "yarn lint", "post-merge": "yarn"}}, "resolutions": {"@types/react": "^17.0.0", "@mtfe/knb-next": "0.3.1", "@mrn/react-native": "3.0.37", "@mrn/mrn-base": "3.0.47", "eventemitter3": "4.0.7"}, "engines": {"node": "<17"}}