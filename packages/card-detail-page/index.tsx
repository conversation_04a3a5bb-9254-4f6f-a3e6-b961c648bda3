import { GlobalComponentReporter } from '@mrn/mcc-component-report'
/*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2023-11-28 11:01:23
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2023-12-06 11:33:47
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/mrn-beauty/packages/card-detail-page/index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { AppRegistry } from '@mrn/react-native'
import App from './src/App'
import { owl } from '@mrn/mrn-owl'

// 初始化 SDK
owl.start({
  devMode: process.env.NODE_ENV !== 'production', // true 为开发模式，上报到 Raptor 测试环境中， false 上报到线上
  debug: false,
  autoPv: true,
  autoCatch: {
    // 自动捕获
    error: false // 自动捕获 JS 异常并上报，自动捕获之后，Native 侧不会再感知到异常，也不会展示错误页面。 建议不开启
  },
  onErrorPush: function (error) {
    // 异常上报前的回调，可以做异常的过滤限流等功能。
    return error
  },
  component: 'rn_gcbu_mrn-beauty-card-detail-page', // 当前所处的组件
  project: 'rn_gcbu_mrn-beauty-card-detail-page' //  必填，Bundle 名称
})

// 这里注册的 mrnproject 可以是全集团不冲突的任意名字
GlobalComponentReporter.start({ appKey: 'rn_gcbu_mrn-beauty-card-detail-page' })
AppRegistry.registerComponent('card-detail-page', () => App)
