/*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2024-01-03 10:39:21
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2024-01-03 16:03:29
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/mrn-beauty/packages/card-detail-page/src/hooks/fetchHooks.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useEffect, useCallback } from 'react'
import { useImmer } from 'use-immer'
import { 
  BaseInfoRequest, 
  BaseInfoResponse, 
  ServiceModuleRequest, 
  ServiceModuleResponse, 
  ShopUgcRequest, 
  ShopUgcResponse,
  FavorRequest,
  FavorResponse
} from '../types'
import { fetchDetailBaseInfo, fetchDetailService, fetchShopUgc, checkFavor } from '../api'
import MRNKNB from '@mrn/mrn-knb'
import { pageRouterClose } from '@mrn/mrn-utils'
import { getLocation, getCity, KNBLocationRes, KNBCityRes } from '../utils/location'
import { getUserInfo, KNBUserInfoRes } from '../utils/userInfo'
import { isMT } from '@mrn/mrn-gc-utils'

export const useFetchHooks = params => {
  const [baseInfo, setBaseInfo] = useImmer<BaseInfoResponse>({
      cardInfo: {
          productId: 0,
          productTypeId: 0,
          cardName: '',
          cardPrice: '',
          cardTotalPrice: '',
          marketPrice: '',
          marketTotalPrice: '',
          timesCardType: 0,
          productItemId: 0,
          saleCountInfo: '',
          totalCount: 0,
          discountDesc: '',
          video: {
              url: '',
              desc: '',
              scale: ''
          },
          status: 0,
          pictures: [],
          promoStrengthTags: [],
          promoModule: {}
      },
      projectList: [],
      reviewInfo: {
          goodReviewRatio: '',
          reviewListUrl: '',
          userPics: [],
          reviewPhrase: '',
          totalReviewDesc: ''
      },
      guarantee: [],
      guaranteeLayer: {
          title: '',
          guaranteeDetails: []
      },
      reminder: [],
      reminderLayer: {
          title: '',
          content: []
      },
      applyShop: {
          shopName: '',
          applyShopCount: 0,
          applyShopListUrl: '',
          shopUrl: '',
          shopId: 0
      },
      buyBtn: {
          btnTitle: '',
          redirectUrl: '',
          btnEnable: true,
          btnTag: '',
          priceStr: ''
      }
  })

  const [serviceModule, setServiceModule] = useImmer<ServiceModuleResponse>({
    title: '',
    moduleList: []
  })

  const [shopUgc, setShopUgc] = useImmer<ShopUgcResponse>({
    shopName: '',
    averageScore: 0,
    totalCount: 0,
    ugcContentList: [],
    scoreList: {},
    tags: []
  })

  const [favorStatus, setFavorStatus] = useImmer<any>(false)

  const getDetailBaseInfo = useCallback(
    (_params: BaseInfoRequest) => {
      fetchDetailBaseInfo(_params).then(res => {
        setBaseInfo(res)
      })
    },
    [setBaseInfo]
  )
  
  const getDetailService = useCallback(
    (_params: ServiceModuleRequest) => {
      fetchDetailService(_params).then(res => {
        setServiceModule(res)
      })
    },
    [setServiceModule]
  )

  const getShopUgc = useCallback(
    (_params: ShopUgcRequest) => {
      fetchShopUgc(_params).then(res => {
        setShopUgc(res)
      })
    },
    [setShopUgc]
  )

  const getFavorStatus = useCallback(
    (_params: FavorRequest) => {
      checkFavor(_params).then(res => {
        setFavorStatus(res)
      })
    },
    [checkFavor]
  )

  let { baseInfoParams, detailServiceParams, shopUgcParams, favorParams } = params

  const init = useCallback(async () => {
      try {
        const [ location, userInfo, city ] = await Promise.all([getLocation(), getUserInfo(), getCity()])
        const { lat, lng } = location as KNBLocationRes || {}
        const { unionId, userId, token, uuid } = userInfo  as KNBUserInfoRes || {}
        const { cityId } = city  as KNBCityRes || {}
        getDetailBaseInfo({
          ...baseInfoParams, 
          lat, 
          lng, 
          cityId,
          unionId
        })
        getDetailService({
          ...detailServiceParams, 
          unionId
        })
        if(isMT()) {
          getFavorStatus({
            ...favorParams,
            token,
            userId,
            cityId,
            uuid
          })
        }
      } catch(e) {
        console.log('没开启定位', e)
        try {
          const { unionId, userId, token, uuid } = await getUserInfo() as KNBUserInfoRes || {}
          getDetailBaseInfo({
            ...baseInfoParams, 
            unionId
          })
          getDetailService({
            ...detailServiceParams, 
            unionId
          })
          if(isMT()) {
            getFavorStatus({
              ...favorParams,
              token,
              userId,
              uuid
            })
          }
        } catch(err) {
          console.log('用户信息获取失败', err)
        }
      }
  }, [getDetailBaseInfo, getDetailService, getShopUgc])

  useEffect(() => {
    const shopId = baseInfo?.applyShop?.shopId || 0
    if(!shopId) return
    getShopUgc({...shopUgcParams, shopId})
  }, [baseInfo])

  useEffect(() => {
    MRNKNB.getUserInfo({
      success: (user: any) => {
        if (user.userId === '-1') {
          MRNKNB.login({
            success: () => {
              init()
            },
            fail: e => {
              console.log(e)
              setTimeout(() => {
                pageRouterClose()
              }, 500)
            }
          })
        }
      }
    })
    init()
  }, [init])

  return {
    baseInfo,
    serviceModule,
    shopUgc,
    favorStatus,
    getDetailBaseInfo,
    getDetailService,
    getShopUgc,
    getFavorStatus
  }
}
