/*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2023-11-28 11:01:23
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2024-01-04 17:37:53
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/mrn-beauty/packages/card-detail-page/src/App.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useState, useRef } from 'react'
import { AnimatedMCPage } from '@nibfe/doraemon-practice'
import { Animated } from '@mrn/react-native'
import TopViewProvider from '@max/leez-top-view-provider'
import { HeaderInfoModule } from './components/HeaderInfoModule'
import { PurchaseDetailModule } from './components/PurchaseDetailModule'
import { ImageTextDetailModule } from './components/ImageTextDetailModule'
import { PurchaseNotesModule } from './components/PurchaseNotesModule'
import { UGCModule } from './components/UGCModule'
import { FooterModule } from './components/FooterModule'
import { SlideModalModule } from './components/SlideModalModule'
import { ProjectGiftModule } from './components/ProjectGiftModule'
import { PurchaseBenefitModule } from './components/PurchaseBenefitModule'
import { Navigation } from './components/Navigation'
import { useFetchHooks } from './hooks/fetchHooks'
import { isDP, getAppVersion } from '@mrn/mrn-gc-utils'
import { Provider } from '@nibfe/gc-ui'

interface Props {
  productId: string
  shopId: string
  uuid: string
  lng: string
  lat: string
  version_name: string
  recommendId: string
  channelSource: number
  pass_param: string
}

const App: React.FC<Props> = props => {
  const {
    productId,
    uuid,
    recommendId = null,
    channelSource = null,
    pass_param = null,
    shopId
  } = props
  const [modalType, setModalType] = useState(null)
  const [videoStatus, setVideoStatus] = useState(null)
  const scrollY = useRef(new Animated.Value(0)).current
  const [fold, setFold] = useState(false)
  const { baseInfo, serviceModule, shopUgc, favorStatus } = useFetchHooks({
    baseInfoParams: {
      shopId,
      productId,
      recommendId,
      channelSource,
      clientType: isDP() ? 100 : 200,
      version: getAppVersion(),
      pass_param,
      lat: null,
      lng: null,
      cityId: null
    },
    detailServiceParams: {
      shopId,
      productId,
      platform: isDP() ? 1 : 2,
      clientType: isDP() ? 100 : 200,
      version: getAppVersion()
    },
    shopUgcParams: {
      shopId: null,
      shopuuid: uuid,
      channel: isDP() ? 'dp' : 'mt',
      pageno: 0
    },
    favorParams: {
      token: null,
      productId,
      userId: null,
      uuid,
      cityId: null
    }
  })

  return (
    <Provider>
      <TopViewProvider>
        <Navigation scrollY={scrollY} fold={fold} />
        <AnimatedMCPage
          mptInfo={{
            category: 'gc',
            cid: 'c_sx1dwrmk',
            labs: {
              poi_id: shopId,
              custom: {
                product_id: productId,
                index: 2,
                biz_id: 205,
                product_type: 1,
                source: isDP() ? 'dp' : 'mt',
                spu_type: '-9999'
              }
            }
          }}
          enableBounce={false}
          onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
            useNativeDriver: true
          })}
          scrollEventThrottle={1}
          paddingHorizontal={0}
          contentBackgroundColor="#F4F4F4"
          // eslint-disable-next-line react-native/no-inline-styles
          separatorLineStyle={{
            display: 'hidden-all'
          }}
          pageTopGap={0}
          pageGap={0}
          onAppear={() => setVideoStatus(true)}
          onDisappear={() => setVideoStatus(false)}
          modules={[
            {
              moduleKey: 'HeaderInfoModule',
              module: (
                <HeaderInfoModule
                  setModalType={setModalType}
                  baseInfo={baseInfo}
                  shopId={shopId}
                  productId={productId}
                  modalType={modalType}
                  videoStatus={videoStatus}
                  setVideoStatus={setVideoStatus}
                />
              )
            },
            {
              moduleKey: 'PurchaseDetailModule',
              module: <PurchaseDetailModule serviceModule={serviceModule} />
            },
            {
              moduleKey: 'ProjectGiftModule',
              module: <ProjectGiftModule baseInfo={baseInfo} />
            },
            {
              moduleKey: 'PurchaseBenefitModule',
              module: <PurchaseBenefitModule baseInfo={baseInfo} />
            },
            {
              moduleKey: 'ImageTextDetailModule',
              module: <ImageTextDetailModule baseInfo={baseInfo} />
            },
            {
              moduleKey: 'PurchaseNotesModule',
              module: <PurchaseNotesModule baseInfo={baseInfo} />
            },
            {
              moduleKey: 'UGCModule',
              module: <UGCModule shopUgc={shopUgc} baseInfo={baseInfo} />
            },
            {
              moduleKey: 'FooterModule',
              module: <FooterModule baseInfo={baseInfo} favorStatus={favorStatus} />
            },
            {
              moduleKey: 'SlideModalModule',
              module: (
                <SlideModalModule
                  setModalType={setModalType}
                  modalType={modalType}
                  baseInfo={baseInfo}
                />
              )
            }
          ]}
        />
      </TopViewProvider>
    </Provider>
  )
}

export default App
