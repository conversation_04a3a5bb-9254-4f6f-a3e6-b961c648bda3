import KNB from '@mrn/mrn-knb'

export interface KNBCityRes {
  cityId: string
  locCityId: string
  type: 'mt' | 'dp'
  cityName: string
  locCityName: string
  areaId: string
  areaName: string
  noCache: boolean
}
export interface KNBLocationRes {
  lat: number
  lng: number
}

export interface KNBLocationRes {
  lat: number
  lng: number,
}

export function getCity() {
  return new Promise(res => {
    KNB.getCity({
      success: (data: KNBCityRes) => {
        res(data)
      }
    })
  })
}

export function getLocation() {
  return new Promise((res, rej) => {
    KNB.getLocation({
      sceneToken: 'dd-473af310dbd99039', // 因隐私合规中长期方案影响，增加sceneToken，根据mode不同需要不同权限，mode为instant和accurate为需要Locate.continuous，其他需要Locate.once
      type: 'wgs84', // 可选值wgs84(标准坐标系)， gcj02（国测局 火星坐标）,1.2.0版本支持，建议微信小写，美团写大写
      timeout: 5000, //定位超时时间，1.2.0版本默认为5000，1.1.0版本默认为6000
      success: function (location: KNBLocationRes) {
        res(location)
      },
      fail: function (ret) {
        rej(ret)
      }
    })
  })
}
