// 接口信息：https://km.sankuai.com/page/472079390
import { onUserCaptureScreen, offUserCaptureScreen } from '@max/meituan-uni-screen'

import KNB from '@mrn/mrn-knb'
import { request } from '@mrn/mrn-utils'
import { IS_ANDROID, IS_IOS } from '@mrn/mrn-gc-utils'
const URL_PROD_BASE = 'https://optimus-mtsi.meituan.com/'
const URL_PROD = '/mtsi-worker/'
const EVENT_TYPE_CAPTURE_SCREEN = "601"
const EVENT_TYPE_PROMO = "602"
const BIZ_TYPE_DEAL_DETAIL_PAGE = 'deal_detail_page'

function getActUrlId() {
  return 'com.sankuai.merchantcard.timescard.exposure-/api/vc/merchantcard/timescard/navigation/baseinfo'
}

function getCacheKey(eventType) {
  return `${BIZ_TYPE_DEAL_DETAIL_PAGE}_${eventType}`
}

function getCache(eventType) {
  const key = getCacheKey(eventType)
  return new Promise((resolve) => {
    KNB.getStorage({
      key,
      success: res => {
        if (res?.value) {
          try {
            const cache = JSON.parse(res.value)
            resolve(cache)
          } catch (error) {
            resolve(null)
          }
        } else {
          resolve(null)
        }
      },
      fail: () => {
        resolve(null)
      }
    })
  })
}

function saveCache(eventType, query) {
  const key = getCacheKey(eventType)
  if(query){
    const newQuery = {
        ...query,
        cache_type: '1'
      }
    KNB.setStorage({key, value:JSON.stringify(newQuery),level: 1})
  }
  
}

function removeCache(eventType) {
  const key = getCacheKey(eventType)
  KNB.clearStorage({key})
}

//表示小程序端或者APP、H5
function getUtmMedium() {
    return IS_ANDROID ? 'android' : IS_IOS ? 'iphone' : ''
  }
  
  //表示系统平台
  function getPlatform() {
    return IS_ANDROID ? 'android' : IS_IOS ? 'iphone' : ''
  }

function generateQueryParams(eventType, params) {
  const query:any = {
    token: params?.token || '',
    poiId: params?.poiId || '',
    dealid: params?.dealid || '',
    utm_medium: getUtmMedium(),
    platform: getPlatform(),
    act_url_id: getActUrlId(),
    cache_type: '0', // 取值0、1，1表示缓存
  }
  if (eventType === EVENT_TYPE_CAPTURE_SCREEN) {
    query.is_show_more = params?.isShowMore ? '1' : '0' // 取值0、1，1表示展开更多
  }
  return query
}

function networkRequest(eventType, query) {
  if (!eventType || !query) {
    return
  }
    request({
        baseURL: URL_PROD_BASE,
        url: URL_PROD.concat(eventType),
        data: query,
        method: 'POST',
        header: {
        'content-type': 'application/json; charset=utf-8'
        }
    }).then((res) => {
        if(res?.data?.resultCode === 0){
            removeCache(eventType)
        }else{
            saveCache(eventType, query)
        }
    })
    .catch(() => {
        saveCache(eventType, query)
    })
}

/**
 * 上报缓存
 */
async function uploadCache() {
  try {
    const query = await getCache(EVENT_TYPE_PROMO)
    networkRequest(EVENT_TYPE_PROMO, query)
  } catch (error) {
    // ignore
  }
}

/**
 *
 * @param {*} eventType 上报类型：查看更多
 * @param {*} params 其他参数
 */
function uploadEvent(eventType, params) {
  try {
    const query = generateQueryParams(eventType, params)
    networkRequest(eventType, query)
  } catch (error) {
    // ignore
  }
}

function onScreenListener (callback) {
    onUserCaptureScreen(callback)
}

function offScreenListener (callback) {
  offUserCaptureScreen(callback)
}

export { EVENT_TYPE_CAPTURE_SCREEN, EVENT_TYPE_PROMO, uploadEvent, uploadCache, onScreenListener, offScreenListener }
