import React, { useState, useEffect } from 'react'
import { View, Text, StyleSheet, Image, TouchableOpacity } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { BaseInfoResponse } from '../types/index'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent, openUrl } from '@mrn/mrn-utils'
import { getWidth, isMT, IS_IOS } from '@mrn/mrn-gc-utils'
import { addFavor, cancelFavor } from '../api/index'
import { getCity, KNBCityRes } from '../utils/location'
import { getUserInfo, KNBUserInfoRes } from '../utils/userInfo'

const rate = (getWidth() - 24) / 351
const SHOP_ICON_URL = 'https://p0.meituan.net/ingee/70b90efb094c3755e9bf354ba67c830a1020.png'
const NOT_FAVOR__ICON_URL = 'https://p0.meituan.net/ingee/b154c7576280040c4c39e0f37ae330851123.png'
const FAVOR__ICON_URL = 'https://p0.meituan.net/ingee/ebad9d7e6e5e6082c6ef535cc54b376a2972.png'

interface Props {
  baseInfo: BaseInfoResponse,
  favorStatus: any
}

export const FooterModule: React.FC<Props> = (props) => {
  const [favorStatus, setFavorStatus] = useState(false)
  const [favorParams, setFavorParams] = useState({
    userInfo: null,
    city: null
  })
  const { buyBtn, applyShop, cardInfo, collect } = props.baseInfo || {}
  const redirectUrl = buyBtn?.redirectUrl || ''
  const btnTitle = buyBtn?.btnTitle || ''
  const priceStr = buyBtn?.priceStr || ''
  const btnTag = buyBtn?.btnTag || ''
  const shopUrl = applyShop?.shopUrl || ''

  lxTrackMGEViewEvent('gc', 'b_vtj31gc6', 'c_sx1dwrmk', {
    abtest: '-9999',
    product_id: cardInfo?.productId || '-9999' 
  })

  useEffect(() => {
    isMT() ? setFavorStatus(props.favorStatus) : setFavorStatus(collect)
  }, [props.favorStatus, collect])

  useEffect(() => {
    Promise.all([getUserInfo(), getCity()]).then((res) => {
      const [ userInfo, city ] = res || []
      setFavorParams({userInfo, city})
    })
  }, [getUserInfo, getCity])

  const handleFavor = () => {
    const { userInfo, city } = favorParams
    const { userId, token, uuid } = userInfo  as KNBUserInfoRes || {}
    const { cityId } = city  as KNBCityRes || {}

    const params = {
      token,
      productId: cardInfo?.productId || 0,
      userId, 
      uuid,
      cityId,
    }

    if(favorStatus) {
      cancelFavor(params).then(res => {
        setFavorStatus(res)
      })
    } else {
      addFavor(params).then(res => {
        setFavorStatus(res)
      })
    }
  }

  return (
    <MCModule 
      paddingLeft={0} 
      paddingRight={0} 
      hoverType="alwaysHoverBottom" 
      hoverOffset={0}
      gapBottom={10}
    >
      <View style={[ styles.horizontalLayOut, styles.container]}>
        <View style={styles.itemBarContainer}>
          <TouchableOpacity 
            style={styles.itemButton}
            activeOpacity={0.5}
            onPress={() => {openUrl(shopUrl)}}
          >
            <Image style={styles.itemButtonIcon} source={{ uri: SHOP_ICON_URL }}/>
            <Text style={styles.itemButtonTitle}>门店</Text>
          </TouchableOpacity>
          <TouchableOpacity
            activeOpacity={0.5}
            onPress={handleFavor}
          >
            <View style={styles.itemButton}>
              <Image style={styles.itemButtonIcon} source={{ uri: favorStatus ? FAVOR__ICON_URL : NOT_FAVOR__ICON_URL }}/>
              <Text style={styles.itemButtonTitle}>收藏</Text>
            </View>
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          activeOpacity={1}
          style={styles.rightContainer}
          onPress={() => {
            openUrl(redirectUrl)
            lxTrackMGEClickEvent('gc', 'b_fz16v9ua', 'c_sx1dwrmk', {
              product_id: cardInfo?.productId || '-9999' ,
              index: '-9999',
              product_type: 0
            })
          }}
        >
          <LinearGradient
            start={{ x: 0, y: 0.5 }}
            end={{ x: 1, y: 0.5 }}
            colors={['#FF8225', '#FF4B10']}
            style={styles.buttonContainerBg}
          >
            <View style={styles.buttonContainer}>
              <Text style={styles.buttonTitle}>{btnTitle}</Text>
              <View style={styles.buttonDetailContainer}>
                <Text style={styles.buttonPrice}>¥{priceStr}</Text>
                <View style={styles.buttonGapLine}></View>
                <Text style={styles.buttonCupon}>{btnTag}</Text>
              </View>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  horizontalLayOut: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  container: {
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 9,
    height: IS_IOS ? 82 : 62,
    backgroundColor: '#ffffff'
  },
  leftContainer: {
    flex: 1,
    height: '100%',
    justifyContent: 'center'
  },
  disAmountContainer: {
    width: '100%',
    justifyContent: 'flex-start'
  },
  disAmountText: {
    fontSize: 12.5,
    color: '#393939',
    fontWeight: '400'
  },
  priceContainer: {
    fontFamily: 'DINAlternate-Bold',
    fontSize: 23,
    color: '#FF3800',
    fontWeight: '700'
  },
  priceIcon: {
    fontFamily: 'PingFangSC-Semibold',
    fontSize: 13,
    color: '#FF3800',
    fontWeight: '600'
  },
  allDisAmountContainer: {
    fontSize: 12.5,
    color: '#7C7C7C',
    fontWeight: '400'
  },
  rightContainer: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 10
  },
  buttonText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: 15,
    color: '#222222',
    fontWeight: '500'
  },
  itemBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
    paddingBottom: 10,
  },
  itemButton: {
    flexDirection: 'column',
    alignItems: 'center',
    marginRight: 24,
  },
  itemButtonIcon: {
    width: 22,
    height: 22,
    paddingBottom: 3,
  },
  itemButtonTitle: {
    color: '#202020',
    fontFamily: 'PingFang SC',
    fontSize: 10,
    textAlign: 'center',
  },
  buttonContainerBg: {
    width: '100%',
    borderRadius: 22,
    marginVertical: 9,
  },
  buttonContainer: {
    width: 150,
    paddingVertical: 6,
    flexDirection: 'column',
    alignItems: 'center',
  },
  buttonTitle: {
    color: '#FFFFFF',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 14,
    textAlign: 'center',
  },
  buttonDetailContainer: {
    flexDirection: 'row',
    marginTop: -3,
  },
  buttonGapLine: {
    backgroundColor: '#FFFFFF', 
    height: 6, 
    width: 0.5, 
    marginHorizontal: 3, 
    marginTop: 4.5
  },
  buttonPrice: {
    color: '#FFFFFF',
    fontFamily: 'PingFang SC',
    fontSize: 10,
    textAlign: 'left',
  },
  buttonCupon: {
    color: '#FFFFFF',
    fontFamily: 'PingFang SC',
    fontSize: 10,
    textAlign: 'left',
  },
})
