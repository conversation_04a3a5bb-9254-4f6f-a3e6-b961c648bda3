import React, { useRef, useState, useEffect, useCallback } from 'react'
import { getWidth } from '@mrn/mrn-gc-utils'
import { openUrl } from '@mrn/mrn-utils'
import KNB from '@mrn/mrn-knb'
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  ScrollView,
  Animated,
  ImageBackground
} from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { MODAL_TYPE_MAP } from '../config/index'
import { BaseInfoResponse } from '../types/index'
import { Carousel } from '@ss/mtd-react-native'
import VideoView from '@hfe/max-video-view'
import { getRecommendHeight } from '@nibfe/dm-navigation'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'
import { EVENT_TYPE_PROMO, uploadEvent, uploadCache } from '../utils/anti_event'
import { getUserInfo } from '../utils/userInfo'


const rate = getWidth() / 375
const ALL_ARROW = 'https://p0.meituan.net/ingee/dd52ce335472dc4934706dd154c3067d392.png'
const COMMON_ARROW = 'https://p0.meituan.net/ingee/d0cfdf165b59e67740003a9d40ee27fa463.png'
const GUARANTEE_ICON = 'https://p0.meituan.net/ingee/7febc0d374846c0e2e4b32e8f17be6d02017.png'
const PLAY_ICON = 'https://p1.meituan.net/travelcube/612b67f80c8295a9754bbc7b279d68163570.png'

interface Props {
  setModalType: (value: string | null) => void
  baseInfo: BaseInfoResponse
  shopId?: string
  productId?: string
  modalType?: string
  videoStatus: boolean
  setVideoStatus: any
}

export const HeaderInfoModule: React.FC<Props> = props => {
  const { cardInfo, projectList, reviewInfo, guarantee, reminder, applyShop } = props.baseInfo || {}
  const { videoStatus, setVideoStatus, shopId, productId, modalType } = props
  const { pictures, video } = cardInfo || {}

  const videoRef = useRef(null)
  // const [ videoStatus, setVideoStatus ] = useState(true)
  const [switchPosition, setSwitchPosition] = useState('left')
  const slideAnim = useRef(new Animated.Value(0)).current
  const [color, setColor] = useState({
    left: '#222',
    right: '#fff'
  })

  const videoStart = () => videoRef?.current?.videoStart?.()
  const videoPause = () => videoRef?.current?.videoPause?.()
  const [userInfo, setUserInfo] = useState(null)

  useEffect(() => {
    if (videoRef?.current) {
      const timer = setTimeout(() => {
        videoStart()
        clearTimeout(timer)
      }, 500)
    }
    getUserInfo().then(res => setUserInfo(res))
    uploadCache()
  }, [])

  useEffect(() => {
    if (videoStatus) {
      videoStart()
    } else {
      videoPause()
    }
  }, [videoStatus])

  useEffect(() => {
    if(modalType === MODAL_TYPE_MAP['AllDiscounts']){
      uploadEvent(EVENT_TYPE_PROMO, {
        token: userInfo?.token,
        poiId: shopId,
        dealid: productId
      })
    }
  }, [modalType, userInfo])

  useEffect(() => {
    if (switchPosition === 'left') {
      setVideoStatus(true)
      videoStart()
    } else {
      setVideoStatus(false)
      videoPause()
    }
  }, [switchPosition])

  const renderHeader = () => {
    const { saleCountInfo, discountDesc, cardTotalPrice, marketTotalPrice } = cardInfo || {}
    return (
      <View style={[styles.headerContainer, styles.verticalCenter]}>
        <View style={[styles.priceContainer, styles.verticalCenter]}>
          <View style={[styles.salePriceContainer, styles.verticalCenterStart]}>
            <Text style={styles.salePriceIcon}>¥</Text>
            <Text style={styles.salePriceText}>{cardTotalPrice}</Text>
          </View>

          <View style={styles.discountContainer}>
            <Text style={styles.discountText}>{discountDesc}</Text>
          </View>

          <View style={styles.verticalCenterStart}>
            <Text style={styles.originPriceText}>¥{marketTotalPrice}</Text>
          </View>
        </View>

        <Text style={styles.salesCountText}>{saleCountInfo}</Text>
      </View>
    )
  }

  const renderDiscountTags = () => {
    const { promoStrengthTags } = cardInfo || {}
    const tags = promoStrengthTags?.join(' | ') || ''
    if (!tags) return null

    lxTrackMGEViewEvent('gc', 'b_gc_8g6lqytn_mv', 'c_sx1dwrmk', {
      promotion_array: promoStrengthTags,
      promotion_title: tags
    })

    return (
      <View style={[styles.discountTagContainer, styles.verticalCenter]}>
        <View style={styles.tagContainer}>
          <Text numberOfLines={1} style={styles.tagText}>
            {tags}
          </Text>
        </View>
        <TouchableOpacity
          style={[styles.allContainer, styles.verticalCenter]}
          activeOpacity={0.6}
          onPress={() => {
            props.setModalType(MODAL_TYPE_MAP['AllDiscounts'])
            lxTrackMGEClickEvent('gc', 'b_gc_8g6lqytn_mc', 'c_sx1dwrmk', {
              promotion_array: promoStrengthTags,
              promotion_title: tags
            })
          }}
        >
          <Text style={styles.allText}>全部</Text>
          <Image source={{ uri: ALL_ARROW }} style={styles.arrowIcon}></Image>
        </TouchableOpacity>
      </View>
    )
  }

  const renderTitle = () => {
    const { cardName } = cardInfo || {}
    return (
      <View style={styles.titleContainer}>
        <Text style={styles.titleText}>{cardName}</Text>
      </View>
    )
  }

  const renderProjectIntroduction = () => {
    if (!projectList?.length) return null
    const projectType = projectList.find(item => item.title === '项目种类')?.desc || '-9999'
    const projectNum = projectList.find(item => item.title === '项目次数')?.desc || '-9999'

    lxTrackMGEViewEvent('gc', 'b_gc_3eutxa4j_mv', 'c_sx1dwrmk', {
      class_count: projectType,
      item_count: projectNum
    })

    if (projectList?.length === 2) {
      return (
        <View style={styles.twoIntroContainer}>
          {projectList.map((item, index) => {
            const title = item?.title || ''
            const desc = item?.desc || ''
            return (
              <View
                key={`list-${index}`}
                style={[
                  styles.introItem,
                  index === 0 ? { borderRightWidth: 0.5, borderRightColor: '#E5E5E5' } : null
                ]}
              >
                <Text style={styles.projectIntroItemTitle}>{title}</Text>
                <Text style={styles.projectIntroItemText}>{desc}</Text>
              </View>
            )
          })}
        </View>
      )
    }

    return (
      <View style={styles.projectIntroContainer}>
        <ScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          style={styles.projectIntroScroll}
          snapToAlignment={'end'}
        >
          {projectList.map((item, index) => (
            <View
              key={`list-${index}`}
              style={[
                styles.projectIntroDivider,
                index === projectList.length - 1 ? { borderRightWidth: 0, marginRight: 18 } : null
              ]}
            >
              <Text style={styles.projectIntroItemTitle}>{item.title}</Text>
              <Text style={styles.projectIntroItemText}>{item.desc}</Text>
            </View>
          ))}
        </ScrollView>
      </View>
    )
  }

  const renderUgc = () => {
    const { goodReviewRatio, reviewPhrase, totalReviewDesc, userPics, reviewListUrl } =
      reviewInfo || {}
    if (!reviewPhrase) return null

    lxTrackMGEViewEvent('gc', 'b_gc_9nvun6dj_mv', 'c_sx1dwrmk', {})

    return (
      <TouchableOpacity
        style={[styles.ugcContainer, styles.verticalCenter]}
        activeOpacity={0.5}
        onPress={() => openUrl(reviewListUrl)}
      >
        <View style={[styles.verticalCenterStart, styles.ugcContainerLeft]}>
          <View style={styles.ugcAvatarContainer}>
            <Image source={{ uri: userPics[0] }} style={styles.ugcAvatarPic} />
          </View>
          <Text style={styles.ugcText} numberOfLines={1}>
            {goodReviewRatio} {reviewPhrase}
          </Text>
        </View>

        <View style={styles.verticalCenter}>
          <Text style={styles.ugcText}>{totalReviewDesc}</Text>
          <Image source={{ uri: COMMON_ARROW }} style={styles.arrowIcon}></Image>
        </View>
      </TouchableOpacity>
    )
  }

  const renderApplyShop = () => {
    if (!applyShop) return null
    const { shopName, applyShopCount, applyShopListUrl } = applyShop

    lxTrackMGEViewEvent('gc', 'b_8a445rc0', 'c_sx1dwrmk', {})

    return (
      <TouchableOpacity
        style={[styles.verticalCenterStart, styles.otherIntroRowContainer]}
        activeOpacity={0.6}
        onPress={() => {
          lxTrackMGEClickEvent('gc', 'b_7pdto3ew', 'c_sx1dwrmk', {})
          openUrl(applyShopListUrl)
        }}
      >
        <Text style={styles.otherIntroTitle}>门店</Text>
        <View style={[styles.verticalCenterStart, { width: 277 * rate }]}>
          <Text numberOfLines={1} style={styles.otherIntroText} ellipsizeMode={'middle'}>
            {shopName}
            {applyShopCount > 1 ? `等${applyShopCount}家门店可用` : ''}
          </Text>
          <Image source={{ uri: COMMON_ARROW }} style={styles.arrowIcon}></Image>
        </View>
      </TouchableOpacity>
    )
  }

  const renderReminder = () => {
    if (!reminder) return null

    return (
      <TouchableOpacity
        style={[styles.verticalCenterStart, styles.otherIntroRowContainer, { marginVertical: 12 }]}
        activeOpacity={0.6}
        onPress={() => props.setModalType(MODAL_TYPE_MAP['PurchaseNotes'])}
      >
        <Text style={styles.otherIntroTitle}>须知</Text>
        <View style={[styles.verticalCenterStart, { width: 277 * rate }]}>
          {reminder.map((item, index) => {
            return (
              <View key={`reminder-${index}`} style={styles.verticalCenterStart}>
                <Text
                  numberOfLines={1}
                  style={[styles.otherIntroText, { color: item.textColor }]}
                  ellipsizeMode={'middle'}
                >
                  {item.text}
                </Text>
                {Boolean(index !== reminder.length - 1) && <View style={styles.dotStyle} />}
              </View>
            )
          })}
          <Image source={{ uri: COMMON_ARROW }} style={styles.arrowIcon}></Image>
        </View>
      </TouchableOpacity>
    )
  }

  const renderGuarantee = () => {
    if (!guarantee) return null

    lxTrackMGEViewEvent('gc', 'b_gc_r90b5isu_mv', 'c_sx1dwrmk', {})

    return (
      <TouchableOpacity
        style={[styles.verticalCenterStart, styles.otherIntroRowContainer]}
        activeOpacity={0.6}
        onPress={() => {
          props.setModalType(MODAL_TYPE_MAP['GuaranteeStatement'])
          lxTrackMGEClickEvent('gc', 'b_gc_r90b5isu_mc', 'c_sx1dwrmk', {})
        }}
      >
        <Text style={styles.otherIntroTitle}>保障</Text>
        <View style={[styles.verticalCenterStart, { width: 277 * rate }]}>
          {guarantee.map((item, index) => (
            <View key={`guarantee-${index}`} style={styles.verticalCenterStart}>
              <View style={styles.verticalCenterStart}>
                {Boolean(item.icon) && (
                  <Image source={{ uri: item.icon }} style={styles.guaranteeIcon} />
                )}
                <Text
                  numberOfLines={1}
                  style={[styles.otherIntroText, { color: item.textColor }]}
                  ellipsizeMode={'middle'}
                >
                  {item.text}
                </Text>
              </View>
              {Boolean(index !== guarantee.length - 1) && <View style={styles.dotStyle} />}
            </View>
          ))}
          <Image source={{ uri: COMMON_ARROW }} style={styles.arrowIcon}></Image>
        </View>
      </TouchableOpacity>
    )
  }

  const renderSwitch = () => {
    const slideToLeft = () => {
      setSwitchPosition('left')
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true
      }).start()
      setColor({
        left: '#222',
        right: '#fff'
      })
    }

    const slideToRight = () => {
      setSwitchPosition('right')
      Animated.timing(slideAnim, {
        toValue: 38,
        duration: 150,
        useNativeDriver: true
      }).start()
      setColor({
        left: '#fff',
        right: '#222'
      })
    }

    return (
      <View style={[styles.verticalCenter, styles.switchContainer]}>
        <Animated.View
          style={[
            styles.switchPane,
            {
              transform: [{ translateX: slideAnim }]
            }
          ]}
        />
        <TouchableOpacity
          activeOpacity={0.4}
          onPress={slideToLeft}
          style={styles.switchTextContainer}
        >
          <Text style={[styles.switchText, { color: color.left }]}>视频</Text>
        </TouchableOpacity>
        <TouchableOpacity
          activeOpacity={0.4}
          onPress={slideToRight}
          style={styles.switchTextContainer}
        >
          <Text style={[styles.switchText, { color: color.right }]}>图集</Text>
        </TouchableOpacity>
      </View>
    )
  }

  const renderHeadPic = () => {
    // 头图宽高比为16:9
    if (!pictures?.length && !video) return null

    const videoUrl = video?.url || ''

    const renderPic = () => {
      // 上报第一张图片曝光点
      lxTrackMGEViewEvent('gc', 'b_95sp8mkb', 'c_sx1dwrmk', {
        item_count: pictures.length,
        item_id: 0,
        type: 0
      })

      return (
        <Carousel
          autoplay={true}
          loop={true}
          showPagination={true}
          onIndexChanged={index => {
            lxTrackMGEViewEvent('gc', 'b_95sp8mkb', 'c_sx1dwrmk', {
              item_count: pictures.length,
              item_id: index,
              type: 0
            })
          }}
        >
          {pictures.map((item, index) => {
            return (
              <TouchableOpacity
                key={`picture-${index}`}
                activeOpacity={1}
                onPress={() => {
                  lxTrackMGEClickEvent('gc', 'b_vhittkx2', 'c_sx1dwrmk', {
                    item_count: pictures.length,
                    item_id: index,
                    type: 0
                  })
                }}
              >
                <Image
                  key={index}
                  source={{ uri: item.url }}
                  style={{ width: '100%', height: '100%' }}
                />
              </TouchableOpacity>
            )
          })}
        </Carousel>
      )
    }

    const renderVideo = () => {
      lxTrackMGEViewEvent('gc', 'b_95sp8mkb', 'c_sx1dwrmk', {
        item_count: videoUrl ? 1 : 0,
        item_id: 0, // 目前最多有一个视频，index为0
        type: 1
      })

      return (
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            videoStatus ? videoPause() : videoStart()
            setVideoStatus(!videoStatus)
            lxTrackMGEClickEvent('gc', 'b_vhittkx2', 'c_sx1dwrmk', {
              item_count: 1,
              item_id: 0, // 目前最多有一个视频，index为0
              type: 1
            })
          }}
        >
          <VideoView
            ref={videoRef}
            style={{ width: '100%', height: '100%' }}
            displayMode={0}
            repeat={false}
            videoUrl={videoUrl}
            autoPlay={true}
            onVideoPlaying={() => {
              lxTrackMGEViewEvent('gc', 'b_95sp8mkb', 'c_sx1dwrmk', {
                item_count: 1,
                item_id: 0, // 目前最多有一个视频，index为0
                type: 1
              })
            }}
            onVideoCompleted={() => {
              videoPause()
              setVideoStatus(false)
            }}
          />
          {Boolean(!videoStatus) && <Image source={{ uri: PLAY_ICON }} style={styles.playIcon} />}
        </TouchableOpacity>
      )
    }

    if (pictures?.length && video) {
      return (
        <View>
          <View style={styles.headPic}>
            {switchPosition === 'left' ? renderVideo() : renderPic()}
          </View>
          {renderSwitch()}
        </View>
      )
    } else {
      return (
        <View>
          <View style={styles.headPic}>{pictures?.length ? renderPic() : renderVideo()}</View>
        </View>
      )
    }
  }

  return (
    <MCModule backgroundColor="transparent">
      {renderHeadPic()}
      <View
        style={[
          styles.baseInfoContainer,
          {
            marginTop: !pictures?.length && !video ? getRecommendHeight() - 12 : -12
          }
        ]}
      >
        {renderHeader()}
        <View style={styles.infoContainer}>
          {renderDiscountTags()}
          {renderTitle()}
          {renderProjectIntroduction()}
          {renderUgc()}
          <View style={styles.divider} />
          <View style={styles.otherIntroContainer}>
            {renderApplyShop()}
            {renderReminder()}
            {renderGuarantee()}
          </View>
        </View>
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  verticalCenter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  verticalCenterStart: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center'
  },
  headPic: {
    width: getWidth(),
    height: (9 / 16) * getWidth()
  },
  baseInfoContainer: {
    width: '100%',
    padding: 12,
    paddingBottom: 0,
    marginTop: -12,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: '#F4F4F4'
  },
  headerContainer: {
    width: getWidth() - 24,
    height: 25 * rate
  },
  priceContainer: {},
  salePriceContainer: {
    marginTop: -7,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-end'
  },
  salePriceIcon: {
    color: '#FF4B10',
    fontFamily: 'MTfin2.0',
    fontWeight: '400',
    fontSize: 16,
    marginBottom: -15
  },
  salePriceText: {
    color: '#FF4B10',
    fontFamily: 'MTfin-Regular2.0',
    fontWeight: '400',
    fontSize: 30
  },
  discountContainer: {
    borderRadius: 3,
    borderWidth: 0.5,
    borderColor: '#FF4B10',
    borderStyle: 'solid',
    marginHorizontal: 6.5,
    marginBottom: -3
  },
  discountText: {
    color: '#FF4B10',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 10,
    marginVertical: 2,
    marginHorizontal: 3
  },
  originPriceText: {
    color: '#999999',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 14,
    textDecorationLine: 'line-through'
  },
  salesCountText: {
    color: '#999999',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12,
    marginVertical: 2,
    marginHorizontal: 3
  },
  infoContainer: {
    width: getWidth() - 24,
    padding: 12,
    marginTop: 12 * rate,
    borderRadius: 12,
    backgroundColor: '#fff'
  },
  discountTagContainer: {
    width: '100%',
    height: 24 * rate
  },
  tagContainer: {
    backgroundColor: '#FFF3F0',
    borderRadius: 3,
    maxWidth: 242 * rate,
    height: 24 * rate,
    paddingHorizontal: 6.5 * rate,
    justifyContent: 'center',
    alignItems: 'center'
  },
  tagText: {
    color: '#FF4B10',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 10
  },
  allContainer: {
    height: '100%'
  },
  allText: {
    color: '#FF4B10',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12
  },
  titleContainer: {
    width: '100%',
    marginTop: 8,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center'
  },
  titleText: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 20
  },
  projectIntroScroll: {
    height: 57,
    paddingVertical: 12
  },
  projectIntroContainer: {
    width: '100%',
    height: 57,
    marginVertical: 12,
    backgroundColor: '#F9F9F9',
    borderRadius: 6
  },
  twoIntroContainer: {
    width: 327.5 * rate,
    height: 57,
    marginVertical: 12,
    backgroundColor: '#F9F9F9',
    borderRadius: 6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8.5
  },
  introItem: {
    width: 150 * rate,
    height: '100%',
    justifyContent: 'center'
  },
  projectIntroItemTitle: {
    color: '#999999',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12
  },
  projectIntroItemText: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '500',
    fontSize: 12
  },
  projectIntroArrowContainer: {
    width: 24,
    height: '100%',
    borderRadius: 6,
    backgroundColor: '#F9F9F9',
    position: 'absolute',
    top: 0,
    right: 0,
    justifyContent: 'center',
    alignItems: 'center'
  },
  projectIntroDivider: {
    paddingHorizontal: 9,
    borderRightWidth: 0.5,
    borderRightColor: '#E5E5E5'
  },
  ugcContainer: {
    width: '100%',
    height: 16.5 * rate
  },
  ugcContainerLeft: {
    width: 253 * rate
  },
  ugcAvatarContainer: {
    width: 16.5 * rate,
    height: 16.5 * rate,
    borderRadius: 9,
    overflow: 'hidden'
  },
  ugcAvatarPic: {
    width: 16.5 * rate,
    height: 16.5 * rate,
    borderRadius: 9
  },
  ugcText: {
    marginLeft: 6 * rate,
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12
  },
  otherIntroContainer: {
    width: '100%',
    // height: 60.25 * rate,
    marginTop: 6 * rate
  },
  arrowIcon: {
    width: 12 * rate,
    height: 12 * rate
  },
  playIcon: {
    width: 50 * rate,
    height: 50 * rate,
    position: 'absolute',
    left: getWidth() / 2 - 25 * rate,
    top: ((9 / 16) * getWidth()) / 2 - 25 * rate
  },
  divider: {
    width: '100%',
    height: 0.5,
    marginTop: 12,
    marginBottom: 8,
    backgroundColor: '#E5E5E5'
  },
  otherIntroRowContainer: {
    width: '100%',
    height: 12 * rate
  },
  otherIntroTitle: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 12,
    marginRight: 12 * rate,
    marginTop: -2
  },
  otherIntroText: {
    marginTop: -2,
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12
  },
  guaranteeIcon: {
    width: 10.2 * rate,
    height: 12 * rate,
    marginRight: 4.3,
    marginTop: -1
  },
  switchContainer: {
    width: 78,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#00000066',
    position: 'absolute',
    left: 13,
    bottom: 25
  },
  switchPane: {
    width: 38,
    height: 22,
    marginHorizontal: 1,
    borderRadius: 10.5,
    backgroundColor: '#fff',
    position: 'absolute'
  },
  switchTextContainer: {
    width: 38,
    height: 22,
    marginHorizontal: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  switchText: {
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 10
  },
  dotStyle: {
    width: 2,
    height: 2,
    backgroundColor: '#222222',
    marginHorizontal: 4
  }
})
