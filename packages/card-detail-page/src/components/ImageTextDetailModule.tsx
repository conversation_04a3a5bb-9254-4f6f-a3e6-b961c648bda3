import React from 'react'
import { View, Text, Image, StyleSheet } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { BaseInfoResponse } from '../types/index'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'
import LImage from '@max/leez-image'

interface Props {
  baseInfo: BaseInfoResponse
}

export const ImageTextDetailModule: React.FC<Props> = props => {
  const { imageTextDetail } = props.baseInfo || {}
  if (!imageTextDetail?.length) return null

  lxTrackMGEViewEvent('gc', 'b_z2jgmpl8', 'c_sx1dwrmk', {})

  return (
    <MCModule backgroundColor="transparent" style={styles.container}>
      <View style={styles.card}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>图文详情</Text>
        </View>
        <View style={styles.contentColumn}>
          {imageTextDetail.map(item => {
            // type:0文本 1图片
            if (item.type) {
              return (
                <View style={styles.contentDetail}>
                  <View style={styles.column}>
                    {!props.baseInfo?.heightRestricted ? (
                      <View style={styles.LImageContainer}>
                        <View style={styles.LImageBorder}>
                          <LImage style={styles.LImage} source={{ uri: item.content }} />
                        </View>
                        <View style={styles.imageBottomSpacer} />
                      </View>
                    ) : (
                      <View style={styles.imageContainer}>
                        <View style={styles.imageBorder}>
                          <Image style={styles.image} source={{ uri: item.content }} />
                        </View>
                        <View style={styles.imageBottomSpacer} />
                      </View>
                    )}
                  </View>
                </View>
              )
            } else {
              return (
                <View style={styles.contentTitleContainer}>
                  <Text style={styles.contentTitle}>{item.content}</Text>
                </View>
              )
            }
          })}
        </View>
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  column: {
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'flex-start'
  },
  container: {
    width: '100%',
    paddingHorizontal: 12
  },
  titleContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    marginBottom: 6
  },
  title: {
    paddingTop: 9,
    paddingHorizontal: 12,
    width: '100%',
    textAlign: 'left',
    color: '#222222',
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 22
  },
  card: {
    width: '100%',
    marginTop: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 12
  },
  contentColumn: {
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 12,
    paddingBottom: 12
  },
  contentTitleContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    height: 20
  },
  contentTitle: {
    width: '100%',
    textAlign: 'left',
    color: '#222222',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20
  },
  contentDetail: {
    width: '100%'
  },
  imageContainer: {
    width: '100%',
    height: 193,
    paddingTop: 9,
    paddingBottom: 12
  },
  imageBorder: {
    width: '100%',
    height: 184,
    borderRadius: 6,
    overflow: 'hidden'
  },
  image: {
    width: '100%',
    height: 184,
    resizeMode: 'cover'
  },
  imageBottomSpacer: {
    height: 9
  },
  LImageContainer: {
    width: '100%',
    height: 'auto',
    paddingTop: 1,
    paddingBottom: 1
  },
  LImageBorder: {
    width: '100%',
    height: 'auto',
    borderRadius: 6
  },
  LImage: {
    width: '100%',
    height: 'auto',
    resizeMode: 'contain'
  }
})
