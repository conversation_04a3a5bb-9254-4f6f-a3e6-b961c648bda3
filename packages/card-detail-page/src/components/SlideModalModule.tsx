import React, { useEffect, useState, useRef } from 'react'
import { View, Text, StyleSheet, Image, ScrollView, TouchableOpacity } from '@mrn/react-native'
import { getWidth } from '@mrn/mrn-gc-utils'
import { MCModule } from '@nibfe/doraemon-practice'
import SlideModal from '@max/leez-slide-modal'
import { MODAL_TYPE_MAP } from '../config/index'
import { BaseInfoResponse } from '../types/index'
import { lxTrackMGEViewEvent, lxTrackMGEClickEvent } from '@mrn/mrn-utils'
import { Popover } from '@nibfe/gc-ui'

const rate = (getWidth() - 24) / 351
const RIGHT_MASK_IMAGE = 'https://p0.meituan.net/ingee/6ca882e6faf684528340463050f9522b2886.png'
const UP_ARROW_IMAGE = 'https://p0.meituan.net/ingee/a02c2a2352b80d519db5f68e2730c05f2072.png'

interface Props {
  modalType: string
  baseInfo: BaseInfoResponse
  setModalType: (value: string | null) => void
}

export const SlideModalModule: React.FC<Props> = props => {
  const [visible, setVisible] = useState(false)
  const [scrollViewEnd, setScrollViewEnd] = useState(false)
  const { cardInfo, guaranteeLayer, reminderLayer } = props.baseInfo || {}

  const purchaseNotesScroll = useRef(null)
  const [popVisible, setPopVisible] = useState(false)
  const [refundExplainHasTracked, setRefundExplainHasTracked] = useState(false)

  useEffect(() => setVisible(!!props.modalType), [props.modalType])
  useEffect(() => {
    if (purchaseNotesScroll && visible && props.modalType === MODAL_TYPE_MAP.PurchaseNotes) {
      setTimeout(() => {
        purchaseNotesScroll.current.scrollToEnd({
          animated: true
        })
      }, 0)
    }
  }, [visible, purchaseNotesScroll])

  const onClose = () => {
    props.setModalType(null)
    setVisible(false)
  }

  //弹框自定义标题
  const renderDialogTitle = text => {
    return (
      <View style={commonStyles.titleContainer}>
        <Text style={commonStyles.title}>{text}</Text>
      </View>
    )
  }

  //全部优惠
  const renderAllDiscounts = () => {
    const { promoModule, productTypeId } = cardInfo || {}
    if (!promoModule) return null

    const { promoCalculateList, promoSummaryList } = promoModule || {}

    lxTrackMGEViewEvent('gc', 'b_gc_8bd2y70k_mv', 'c_sx1dwrmk', {
      cat_id: productTypeId,
      promotion_array: promoCalculateList
    })

    const renderPromoCalculateList = () => {
      return (
        <ScrollView
          horizontal={true}
          showsHorizontalScrollIndicator={false}
          style={allDiscountsStyles.scrollView}
          onScroll={({ nativeEvent }) =>
            setScrollViewEnd(
              nativeEvent.contentOffset.x + nativeEvent.layoutMeasurement.width >=
                nativeEvent.contentSize.width
            )
          }
          scrollEventThrottle={400}
        >
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginHorizontal: 16,
              height: '100%'
            }}
          >
            <View style={allDiscountsStyles.marketPriceLayout}>
              <View>
                <View>
                  <Text style={styles.priceBubbleNum}>¥{promoModule.marketPrice}</Text>
                </View>
                <View>
                  <Text style={styles.priceBubbleNumDesc}>到店价</Text>
                </View>
              </View>
              <View style={allDiscountsStyles.reduceSymbolContainer}>
                <View style={allDiscountsStyles.reduceSymbolText} />
              </View>
            </View>
            {promoCalculateList.map((item, index) => {
              return (
                <View key={`list-${index}`} style={{ flexDirection: 'row' }}>
                  <View>
                    <View>
                      <Text style={styles.priceBubbleNum}>
                        ¥{item.calAmount}
                        {item.count == 1 ? '' : ` x ${item.count}`}
                      </Text>
                    </View>
                    <View>
                      <Text style={styles.priceBubbleNumDesc}>{item.desc}</Text>
                    </View>
                  </View>
                  {Boolean(index !== promoCalculateList.length - 1) && (
                    <View style={allDiscountsStyles.reduceSymbolContainer}>
                      <View style={allDiscountsStyles.reduceSymbolText} />
                    </View>
                  )}
                </View>
              )
            })}
          </View>
        </ScrollView>
      )
    }

    const renderRightMask = () => {
      if (promoCalculateList.length <= 4 || scrollViewEnd) return null
      return (
        <View style={styles.whiteMask}>
          <Image style={allDiscountsStyles.imageIconSize} source={{ uri: RIGHT_MASK_IMAGE }} />
        </View>
      )
    }

    const renderDiscountsExplain = () => {
      if (!promoSummaryList?.length) return null
      return (
        <View style={allDiscountsStyles.discountsExplain}>
          <View style={allDiscountsStyles.explainTitleContainer}>
            <Text style={allDiscountsStyles.explainTitleText}>优惠促销</Text>
          </View>
          {promoSummaryList.map((item, index) => {
            const iconTag = item?.iconTag || ''
            const promoName = item?.promoName || ''
            const promoAmount = item?.promoAmount || ''

            return (
              <View key={`summary-${index}`} style={allDiscountsStyles.rowContainer}>
                <View style={allDiscountsStyles.tagContainer}>
                  <Text style={allDiscountsStyles.tagText}>{iconTag}</Text>
                </View>
                <Text style={allDiscountsStyles.explainText}>{promoName}</Text>
              </View>
            )
          })}
        </View>
      )
    }

    return (
      <View style={commonStyles.centerColumn}>
        {renderDialogTitle(MODAL_TYPE_MAP.AllDiscounts)}
        <View style={commonStyles.centerColumn}>
          <View style={{ ...commonStyles.centerRow, ...styles.price }}>
            <View>
              <Text style={styles.priceIcon}>¥</Text>
            </View>
            <View>
              <Text style={styles.priceNum}>{promoModule.promoPrice}</Text>
            </View>
          </View>
          <View style={allDiscountsStyles.calculateListContainer}>
            {renderPromoCalculateList()}
            {renderRightMask()}
            <View style={allDiscountsStyles.upArrowContainer}>
              <Image style={allDiscountsStyles.upArrowIcon} source={{ uri: UP_ARROW_IMAGE }} />
            </View>
          </View>
          {renderDiscountsExplain()}
        </View>
      </View>
    )
  }

  //保障说明
  const renderGuarantee = () => {
    const { title, guaranteeDetails } = guaranteeLayer || {}
    if (!guaranteeDetails?.length) return null

    const renderDesc = elem => {
      return elem.desc.map((ele, index) => {
        return (
          <View key={`dess-${index}`} style={guaranteeStyles.descContainer}>
            <View style={guaranteeStyles.dot} />
            <Text>
              {ele.map((e, index2) => (
                <Text
                  key={`descitem-${index2}`}
                  style={[guaranteeStyles.descText, { color: e.textColor || '#222222' }]}
                >
                  {e.text}
                </Text>
              ))}
            </Text>
          </View>
        )
      })
    }

    return (
      <View style={{ ...commonStyles.centerColumn, paddingBottom: 40 }}>
        {renderDialogTitle(title)}
        <View style={commonStyles.column}>
          {guaranteeDetails.map((item, index) => {
            return (
              <View key={`detail-${index}`}>
                <View style={guaranteeStyles.title}>
                  <Image source={{ uri: item.icon }} style={guaranteeStyles.titleIcon} />
                  <Text style={guaranteeStyles.titleText}>{item.mainTitle}</Text>
                </View>
                {item.content &&
                  item.content.map((elem, index2) => {
                    return (
                      <View key={`item-${index2}`}>
                        {Boolean(elem.title) && (
                          <Text
                            style={[
                              guaranteeStyles.subTitle,
                              { color: elem.title.textColor || '#222222' }
                            ]}
                          >
                            {elem.title.text}
                          </Text>
                        )}
                        {renderDesc(elem)}
                      </View>
                    )
                  })}
              </View>
            )
          })}
        </View>
      </View>
    )
  }

  //购买须知
  const renderPurchaseNotes = () => {
    const { content, refundTable } = reminderLayer || {}
    if (!content?.length) return null

    const renderDesc = elem => {
      return elem.desc.map((ele, index) => {
        return (
          <View key={`desc-${index}`} style={purchaseNotesStyles.descContainer}>
            <View style={purchaseNotesStyles.dot} />
            <View style={{ width: 331 * rate }}>
              <Text>
                {ele.map((e, index2) => (
                  <Text
                    key={`descitem-${index2}`}
                    style={[purchaseNotesStyles.descText, { color: e.textColor || '#222222' }]}
                  >
                    {e.text}
                  </Text>
                ))}
              </Text>
            </View>
          </View>
        )
      })
    }
    const renderRefundTable = () => {
      const Tips: React.FC = () => {
        return (
          <View
            onLayout={() => {
              if (!refundExplainHasTracked) {
                lxTrackMGEViewEvent('gc', 'b_gc_awu7pfbu_mv', 'c_sx1dwrmk', {})
                setRefundExplainHasTracked(true)
              }
            }}
            style={purchaseNotesStyles.tip}
          >
            <Text style={{ color: 'white', fontSize: 12 }}>{refundTable.toast?.text}</Text>
          </View>
        )
      }
      const renderPopover = () => {
        return (
          <Popover
            visible={popVisible}
            placement="up"
            arrowPosition="right"
            theme="dark"
            onDismiss={() => {
              setPopVisible(false)
            }}
            selections={[{ title: '' }]}
            renderItem={() => <Tips />}
            onPressItem={() => {
              setPopVisible(false)
            }}
            childrenWrapperStyle={{ flexDirection: 'row' }}
          >
            <TouchableOpacity
              activeOpacity={1}
              onPress={() => {
                lxTrackMGEClickEvent('gc', 'b_gc_awu7pfbu_mc', 'c_sx1dwrmk', {})
                setPopVisible(true)
              }}
            >
              <Image
                style={{
                  width: 12,
                  height: 12
                }}
                source={{
                  uri: 'https://p0.meituan.net/travelcube/c341825a8c63fda01e3e04b2ceed6608961.png'
                }}
              />
            </TouchableOpacity>
          </Popover>
        )
      }

      const renderTableRow = tableData => {
        return tableData.map((row, index) => {
          return index === 0 ? (
            <View
              key={`row-${index}`}
              style={[purchaseNotesStyles.tableHeader, purchaseNotesStyles.tableRow]}
            >
              <View style={[purchaseNotesStyles.tableCell, purchaseNotesStyles.leftCell]}>
                <Text>{row.useTimes.text}</Text>
              </View>

              <View style={purchaseNotesStyles.tableCell}>
                <Text>{row.refundMoney.text}</Text>
                {refundTable.toast && refundTable.toast.text ? renderPopover() : null}
              </View>
            </View>
          ) : (
            <View key={`row-${index}`} style={purchaseNotesStyles.tableRow}>
              <View style={[purchaseNotesStyles.tableCell, purchaseNotesStyles.leftCell]}>
                <Text>{row.useTimes.text}</Text>
              </View>
              <View style={purchaseNotesStyles.tableCell}>
                <Text>{row.refundMoney.text}</Text>
              </View>
            </View>
          )
        })
      }

      return (
        <View style={{ marginTop: 6 }}>
          <View style={[purchaseNotesStyles.table]}>
            <View style={purchaseNotesStyles.tableTitle}>
              <Text style={{ textAlign: 'center', lineHeight: 18 }}>
                {refundTable.tableHead?.text}
              </Text>
            </View>

            {renderTableRow(refundTable.tableBodies)}
            {refundTable.tableFoot?.text && (
              <View style={purchaseNotesStyles.tableFooter}>
                <Text style={{ color: '#999999' }}>{refundTable.tableFoot?.text}</Text>
              </View>
            )}
          </View>
        </View>
      )
    }

    return (
      <View style={[commonStyles.centerColumn, { paddingBottom: 40 }]}>
        {renderDialogTitle(MODAL_TYPE_MAP.PurchaseNotes)}
        <ScrollView
          ref={purchaseNotesScroll}
          showsVerticalScrollIndicator={false}
          style={[commonStyles.column, purchaseNotesStyles.scrollView]}
        >
          {content.map((item, index) => {
            return (
              <View key={`content-${index}`}>
                <View style={purchaseNotesStyles.title}>
                  <Image source={{ uri: item.title.icon }} style={purchaseNotesStyles.titleIcon} />
                  <Text
                    style={[
                      purchaseNotesStyles.titleText,
                      { color: item.title.textColor || '#222222' }
                    ]}
                  >
                    {item.title.text}
                  </Text>
                </View>

                <View>{renderDesc(item)}</View>
                {Boolean(index !== content.length - 1) && <View style={purchaseNotesStyles.line} />}
              </View>
            )
          })}
          {refundTable ? renderRefundTable() : null}
        </ScrollView>
      </View>
    )
  }

  const render = () => {
    switch (props.modalType) {
      case MODAL_TYPE_MAP.AllDiscounts:
        return renderAllDiscounts()
      case MODAL_TYPE_MAP.GuaranteeStatement:
        return renderGuarantee()
      case MODAL_TYPE_MAP.PurchaseNotes:
        return renderPurchaseNotes()
      default:
        return null
    }
  }

  return (
    <MCModule backgroundColor="transparent">
      <SlideModal
        visible={visible}
        showHeader={true}
        showCloseBtn={true}
        addBottomSpace={true}
        maskClosable={true}
        modalMaxHeight={'90%'}
        titleAlign="center"
        onClosePress={onClose}
        onBackPress={onClose}
      >
        <View style={{ paddingBottom: 12 }}>{render()}</View>
      </SlideModal>
    </MCModule>
  )
}

const commonStyles = StyleSheet.create({
  row: {
    width: '100%',
    flexDirection: 'row'
  },
  centerRow: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center'
  },
  alignRow: {
    height: '100%',
    flexDirection: 'row',
    alignItems: 'center'
  },
  column: {
    width: '100%',
    flexDirection: 'column'
  },
  centerColumn: {
    width: '100%',
    flexDirection: 'column',
    alignItems: 'center'
  },
  titleContainer: {
    height: 46
  },
  title: {
    paddingVertical: 12,
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 16,
    textAlign: 'center'
  }
})

const styles = StyleSheet.create({
  price: {
    height: 41
  },
  priceIcon: {
    paddingTop: 15,
    color: '#FF4B10',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 14,
    textAlign: 'left'
  },
  priceNum: {
    color: '#FF4B10',
    fontFamily: 'MTfin2.0',
    fontWeight: '400',
    fontSize: 30,
    textAlign: 'center'
  },
  whiteMask: {
    position: 'absolute',
    right: 0,
    top: 6,
    bottom: 6,
    width: 30,
    borderBottomRightRadius: 13,
    borderTopRightRadius: 13
  },
  cellTitle: {
    color: '#9E9E9E',
    fontFamily: 'PingFang SC',
    fontSize: 12,
    textAlign: 'left'
  },
  cellDeatail: {
    color: '#9E9E9E',
    fontFamily: 'PingFang SC',
    fontSize: 12,
    textAlign: 'left'
  },
  cellDetailKey: {
    color: '#FF4B10',
    fontFamily: 'PingFang SC',
    fontSize: 12,
    textAlign: 'left'
  },
  cellTitleGap: {
    paddingBottom: 9
  },
  cellDetailGap: {
    paddingBottom: 8
  },
  tagBg: {
    backgroundColor: '#FF4B10',
    borderRadius: 3,
    marginRight: 6,
    height: 16
  },
  tagText: {
    paddingHorizontal: 3,
    fontFamily: 'PingFang SC',
    fontSize: 10,
    textAlign: 'center',
    color: '#FFFFFF'
  },
  cellGap: {
    height: 15
  },
  priceBubbleNum: {
    color: '#FF4B10',
    fontFamily: 'MTfin3.0',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 3
  },
  priceBubbleNumDesc: {
    color: '#FF4B10',
    fontFamily: 'PingFang SC',
    fontSize: 10,
    textAlign: 'center'
  },
  shortLine: {},
  line: {
    backgroundColor: '#E5E5E5',
    height: 0.5,
    marginTop: 10,
    marginBottom: 9
  }
})

const guaranteeStyles = StyleSheet.create({
  title: {
    width: '100%',
    height: 20,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: 6
  },
  titleIcon: {
    width: 16,
    height: 16,
    marginRight: 6
  },
  titleText: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 14
  },
  subTitle: {
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 14,
    marginBottom: 6
  },
  descContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 6
  },
  dot: {
    width: 4,
    height: 4,
    backgroundColor: '#BBBBBB',
    marginHorizontal: 4,
    borderRadius: 2,
    marginTop: 5,
    marginRight: 6
  },
  descText: {
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 14
  }
})

const purchaseNotesStyles = StyleSheet.create({
  scrollView: {
    maxHeight: '95%'
  },
  line: {
    width: 331 * rate,
    height: 0.5,
    backgroundColor: '#E5E5E5',
    marginLeft: 20,
    marginBottom: 12,
    marginTop: 6
  },
  title: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: 6
  },
  titleIcon: {
    width: 14,
    height: 14,
    marginRight: 6
  },
  titleText: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 14
  },
  descContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start'
  },
  dot: {
    width: 4,
    height: 4,
    backgroundColor: '#BBBBBB',
    borderRadius: 2,
    marginRight: 11,
    marginLeft: 5,
    marginTop: 11
  },
  descText: {
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 26
  },
  table: {
    width: 322 * rate,
    marginLeft: 20,
    borderWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#E5E5E5',
    borderRadius: 6,
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12,
    overflow: 'hidden',
    height: 'auto'
  },
  tableTitle: {
    width: '100%',
    backgroundColor: '#F4F4F4',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 9
  },
  tableHeader: {
    backgroundColor: '#F4F4F4'
  },
  tableFooter: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    height: 36.5,
    borderTopWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#E5E5E5'
  },

  tableRow: {
    flexDirection: 'row',
    borderTopWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#E5E5E5'
  },
  tableCell: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 36
  },
  leftCell: {
    borderRightWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#E5E5E5'
  },
  tip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    maxWidth: 280
  },
  textContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 37,
    backgroundColor: '#FFFFFF',
    zIndex: 10
  },
  text: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    color: '#111111',
    fontWeight: '400',
    textAlign: 'center',
    marginRight: 5
  }
})

const allDiscountsStyles = StyleSheet.create({
  scrollView: {
    borderRadius: 12,
    height: 63,
    width: 319 * rate
  },
  marketPriceLayout: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start'
  },
  reduceSymbolContainer: {
    paddingTop: 10,
    width: 15,
    height: 28,
    alignItems: 'center'
  },
  reduceSymbolText: {
    width: 7,
    height: 3,
    backgroundColor: '#FF4B10',
    borderRadius: 0.5
  },
  calculateListContainer: {
    alignItems: 'center',
    height: 72,
    borderColor: '#FF4B10',
    borderStyle: 'solid',
    borderEndWidth: 0.3,
    borderRadius: 12,
    borderWidth: 0.5
  },
  discountsExplain: {
    width: 351 * rate
  },
  explainTitleContainer: {
    marginTop: 15,
    marginBottom: 9.25
  },
  explainTitleText: {
    color: '#9E9E9E',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12
  },
  rowContainer: {
    width: 351 * rate,
    marginBottom: 7.75,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center'
  },
  tagContainer: {
    width: 16,
    height: 16,
    borderRadius: 3,
    marginRight: 6,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FF4B10'
  },
  tagText: {
    color: '#FFFFFF',
    fontFamily: 'PingFang SC',
    fontWeight: '500',
    fontSize: 10
  },
  explainText: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12
  },
  upArrowContainer: {
    width: 21.5,
    height: 8,
    position: 'absolute',
    top: -7.5
  },
  imageIconSize: {
    height: '100%',
    width: '100%'
  },
  upArrowIcon: {
    width: 21.5,
    height: 8
  }
})
