import React, { useState } from 'react'
import { getWidth } from '@mrn/mrn-gc-utils'
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableWithoutFeedback,
  TouchableOpacity
} from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { BaseInfoResponse } from '../types/index'
import { lxTrackMGEViewEvent, lxTrackMGEClickEvent } from '@mrn/mrn-utils'
import { Popover } from '@nibfe/gc-ui'

const rate = getWidth() / 375

interface Props {
  baseInfo: BaseInfoResponse
}

export const PurchaseNotesModule: React.FC<Props> = props => {
  const { purchaseReminder } = props.baseInfo || {}
  const { content, refundTable } = purchaseReminder || {}
  const [isFolded, setIsFolded] = useState(true)
  const [popVisible, setPopVisible] = useState(false)
  const [checkMoreHasTracked, setCheckMoreHasTracked] = useState(false)
  const [refundExplainHasTracked, setRefundExplainHasTracked] = useState(false)
  if (!content) return null

  lxTrackMGEViewEvent('gc', 'b_gc_ron7y6u7_mv', 'c_sx1dwrmk', {})

  const renderRow = (elem, index) => {
    const renderDesc = ele => {
      return ele.map((el, index2) => (
        <Text key={`row-${index2}`} style={{ color: el.textColor, lineHeight: 26 }}>
          {el.text}
        </Text>
      ))
    }

    return (
      <View style={styles.row}>
        <View>
          <View style={styles.titleRow}>
            <Image source={{ uri: elem.title.icon }} style={styles.titleIcon} />
            <Text style={[styles.rowTitle, { color: elem.title.textColor || '#222222' }]}>
              {elem.title.text}
            </Text>
          </View>
          {elem.desc.map((ele, index) => {
            return (
              <View key={`desc-${index}`} style={styles.subTitleContainer}>
                <View style={styles.dot} />
                <View style={{ width: 307 * rate }}>
                  <Text style={styles.subTitle}>{renderDesc(ele)}</Text>
                </View>
              </View>
            )
          })}
          {Boolean(index !== content.length - 1) && <View style={styles.line} />}
        </View>
      </View>
    )
  }

  const renderRefundTable = () => {
    const Tips: React.FC = () => {
      return (
        <View
          onLayout={() => {
            if (!refundExplainHasTracked) {
              lxTrackMGEViewEvent('gc', 'b_gc_awu7pfbu_mv', 'c_sx1dwrmk', {})
              setRefundExplainHasTracked(true)
            }
          }}
          style={styles.tip}
        >
          <Text style={{ color: 'white', fontSize: 12 }}>{refundTable.toast?.text}</Text>
        </View>
      )
    }
    const renderPopover = () => {
      return (
        <Popover
          visible={popVisible}
          placement="up"
          arrowPosition="right"
          theme="dark"
          onDismiss={() => {
            setPopVisible(false)
          }}
          selections={[{ title: '' }]}
          renderItem={() => <Tips />}
          onPressItem={() => {
            setPopVisible(false)
          }}
          childrenWrapperStyle={{ flexDirection: 'row' }}
        >
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
              lxTrackMGEClickEvent('gc', 'b_gc_awu7pfbu_mc', 'c_sx1dwrmk', {})
              setPopVisible(true)
            }}
          >
            <Image
              style={{
                width: 12,
                height: 12
              }}
              source={{
                uri: 'https://p0.meituan.net/travelcube/c341825a8c63fda01e3e04b2ceed6608961.png'
              }}
            />
          </TouchableOpacity>
        </Popover>
      )
    }

    const renderCollapse = () => {
      return (
        <TouchableWithoutFeedback
          onLayout={() => {
            if (!checkMoreHasTracked) {
              lxTrackMGEViewEvent('gc', 'b_gc_z5cfwcmj_mv', 'c_sx1dwrmk', {})
              setCheckMoreHasTracked(true)
            }
          }}
          onPress={() => {
            if (!isFolded) {
              lxTrackMGEClickEvent('gc', 'b_gc_z5cfwcmj_mc', 'c_sx1dwrmk', {})
            }
            setIsFolded(!isFolded)
          }}
        >
          <View style={[styles.textContainer, { marginTop: isFolded ? -18 : null }]}>
            <View>
              <Text style={styles.text}>{isFolded ? '查看更多' : '收起'}</Text>
            </View>
            <View>
              <Image
                style={{
                  width: 12,
                  height: 12,
                  transform: [{ rotate: isFolded ? '0deg' : '180deg' }],
                  marginTop: 12
                }}
                source={{
                  uri: 'https://p0.meituan.net/travelcube/30508df25caf9faddb59090e72856d60392.png'
                }}
              />
            </View>
          </View>
        </TouchableWithoutFeedback>
      )
    }

    const renderTableRow = tableData => {
      if (isFolded) {
        tableData = tableData.slice(0, 8)
      }
      return tableData.map((row, index) => {
        return index === 0 ? (
          <View key={`row-${index}`} style={[styles.tableHeader, styles.tableRow]}>
            <View style={[styles.tableCell, styles.leftCell]}>
              <Text>{row.useTimes.text}</Text>
            </View>

            <View style={styles.tableCell}>
              <Text>{row.refundMoney.text}</Text>
              {refundTable.toast && refundTable.toast.text ? renderPopover() : null}
            </View>
          </View>
        ) : (
          <View key={`row-${index}`} style={styles.tableRow}>
            <View style={[styles.tableCell, styles.leftCell]}>
              <Text>{row.useTimes.text}</Text>
            </View>
            <View style={styles.tableCell}>
              <Text>{row.refundMoney.text}</Text>
            </View>
          </View>
        )
      })
    }

    return (
      <View style={{ marginTop: 6 }}>
        <View style={[styles.table]}>
          <View style={styles.tableTitle}>
            <Text style={{ textAlign: 'center', lineHeight: 18 }}>
              {refundTable.tableHead.text}
            </Text>
          </View>

          {renderTableRow(refundTable.tableBodies)}
          {refundTable.tableBodies.length > 7 && refundTable.tableFoot?.text && !isFolded ? (
            <View style={styles.tableFooter}>
              <Text style={{ color: '#999999' }}>{refundTable.tableFoot?.text}</Text>
            </View>
          ) : null}
          {refundTable.tableBodies.length <= 7 && refundTable.tableFoot?.text ? (
            <View style={styles.tableFooter}>
              <Text style={{ color: '#999999' }}>{refundTable.tableFoot?.text}</Text>
            </View>
          ) : null}
        </View>

        {refundTable.tableBodies.length <= 7 ? null : renderCollapse()}
      </View>
    )
  }

  return (
    <MCModule backgroundColor="transparent" style={styles.container}>
      <View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>购买须知</Text>
        </View>
        <View style={styles.card}>
          {content.map((item, index) => renderRow(item, index))}
          {refundTable ? renderRefundTable() : null}
        </View>
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12
  },
  titleContainer: {
    height: 42,
    marginTop: 12,
    paddingLeft: 12,
    justifyContent: 'center',
    alignItems: 'flex-start',
    backgroundColor: 'transparent'
  },
  title: {
    color: '#222222',
    fontSize: 18,
    fontWeight: '500'
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12
  },
  row: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start'
  },
  dot: {
    width: 4,
    height: 4,
    backgroundColor: '#BBBBBB',
    borderRadius: 2,
    marginRight: 11,
    marginLeft: 5,
    marginTop: 11
  },
  titleRow: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: 6
  },
  titleIcon: {
    width: 14,
    height: 14,
    marginRight: 6
  },
  rowTitle: {
    color: '#222222',
    fontSize: 14,
    fontWeight: '600'
  },
  subTitleContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start'
  },
  subTitle: {
    color: '#222222',
    fontSize: 14,
    fontWeight: '400'
  },
  line: {
    width: 307 * rate,
    height: 0.5,
    backgroundColor: '#E5E5E5',
    marginLeft: 20,
    marginBottom: 12,
    marginTop: 6
  },
  table: {
    width: 307 * rate,
    marginLeft: 20,
    borderWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#E5E5E5',
    borderRadius: 6,
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12,
    overflow: 'hidden',
    height: 'auto'
  },
  tableTitle: {
    width: '100%',
    backgroundColor: '#F4F4F4',

    justifyContent: 'center',
    alignItems: 'center',
    padding: 9
  },
  tableHeader: {
    backgroundColor: '#F4F4F4'
  },
  tableFooter: {
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    height: 36.5,
    borderTopWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#E5E5E5'
  },

  tableRow: {
    flexDirection: 'row',
    borderTopWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#E5E5E5'
  },
  tableCell: {
    flex: 1,
    fontSize: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 36
  },
  leftCell: {
    borderRightWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#E5E5E5'
  },
  tip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    maxWidth: 280
  },
  textContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 36,
    backgroundColor: '#ffffff',
    zIndex: 10
  },
  text: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    color: '#111111',
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 12
  }
})
