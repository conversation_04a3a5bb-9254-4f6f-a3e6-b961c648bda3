import React from 'react'
import {
  View,
  Animated,
  StyleSheet,
  TouchableOpacity,
  Image
} from '@mrn/react-native'
import { getWidth, IS_IOS } from '@mrn/mrn-gc-utils'
import { pageRouterClose } from '@mrn/mrn-utils'
import { getInset } from '@mrn/react-native-safe-area-view'

const rate = (getWidth() - 24) / 351
const THEME_COLOR = 'white'

export const Navigation = ({ scrollY, fold }) => {
  return (
    <View style={styles.extraView}>
      <TouchableOpacity
        onPress={() => pageRouterClose()}
        style={styles.topRow}
        activeOpacity={1}
      >
        <Image source={{uri: 'https://p0.meituan.net/ingee/3209e72d43ab3747437cf0a46e45053b2515.png'}} style={{
          width: 30,
          height: 30
        }} />
      </TouchableOpacity>
      <Animated.View
        style={{
          height: IS_IOS ? 70 : 40,
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          backgroundColor: THEME_COLOR,
          opacity: scrollY.interpolate({
            inputRange: [0, 50],
            outputRange: [0, 1],
          }),
        }}
        pointerEvents={fold ? 'auto' : 'none'}
      >
        <TouchableOpacity
          onPress={() => pageRouterClose()}
          style={styles.bottomRow}
          activeOpacity={1}
        >
          <Image source={{uri: 'https://img.meituan.net/dpmobile/5aca942d6f53719051e13f043ad7ccbb1043.png'}} style={{
            width: 30,
            height: 30
          }} />
        </TouchableOpacity>
      </Animated.View>
    </View>
  )
}


const styles = StyleSheet.create({
  extraView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 20,
  },
  topRow: {
    width: getWidth(),
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 7,
    marginTop: IS_IOS ? getInset('top') : 0
  },
  bottomRow: {
    width: getWidth(),
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 7,
    marginLeft: 11,
    marginTop: IS_IOS ? 30 : 0,
  },
})
