import React, {useEffect} from 'react'
import KNB from '@mrn/mrn-knb'
import { getWidth } from '@mrn/mrn-gc-utils'
import { openUrl } from '@mrn/mrn-utils'
import { View, Text, ImageBackground, TouchableOpacity, Image, StyleSheet } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import Toast from '@max/leez-toast'
import { ServiceModuleResponse } from '../types/index'
interface Props {
  serviceModule: ServiceModuleResponse
}

const rate = getWidth() / 375

export const PurchaseDetailModule: React.FC<Props> = (props) => {
  const { moduleList } = props.serviceModule || {}

  if(!moduleList?.length) return null
  
  const renderSku = (ele) => {
    if(!ele) return null
    const title = ele?.title || ''
    const count = ele?.count || ''
    const price = ele?.price || ''

    const renderRichText = (valueAttrs) => {
      return valueAttrs?.map((value, index) => {
        const name = value?.name || ''
        const newValues = value?.values || []
        const desc = newValues?.find(item => item.name === '说明')?.value || ''
        const info = value?.info || ''

        return (
          <View style={[styles.richTextContainer, {marginTop: index === 0 ? 2 : null}]}>
            <View style={styles.richTextLeftContainer}>
              <View style={styles.richTextNumberContainer}>
                <Text style={styles.richTextNumberText}>{index + 1}</Text>
              </View>
              <View style={styles.richTextTitleContainer}>
                <Text style={styles.richTextTitleText}>{name}</Text>
                {Boolean(newValues.length) && <Text style={styles.richTextSubtitleText}>{desc}</Text>}
              </View>
            </View>
            <View style={styles.richTextTimeContainer}>
              <Text style={styles.richTextTimeText}>{info}</Text>
            </View>
          </View>
        ) 
      }) || null
    }

    const renderStructure = (type, value) => {
      return value.items?.map(item => {

        const name = item?.name || ''
        const valueAttrs = item?.valueAttrs || []
        const itemValue = item?.value || ''

        return (
          <View style={type === 'outer' ? styles.skuRowContainerOuter : styles.skuRowContainerInner}>
            <Text style={type === 'outer' ? styles.skuRowNameOuter : styles.skuRowNameInner}>{name}{type === 'outer' ? '' : '：'}</Text>
            {
              valueAttrs.length ? 
              <View>{renderRichText(valueAttrs)}</View> : (
                <View style={{width: type === 'outer' ? 260 * rate : 234 * rate}}>
                  <Text style={type === 'outer' ? styles.skuRowContentOuter : styles.skuRowContentInner}>{itemValue}</Text>
                </View>
              )
            }
          </View>
        )
      }) || null
    }

    const renderSkuTitle = () => {
      return (
        <View style={styles.skuTitleContainer}>
          <Text style={styles.skuTitleText}>{title}</Text>
          <View style={styles.skuInfoContainer}>
            <Text style={styles.projectCount}>({count}份)</Text>
            <Text style={styles.projectPrice}>¥{price}</Text>
          </View>
        </View>
      )
    }

    const renderSkuBody = () => {
      const subSkuList = ele.subSkuList?.map(item => {
        const title = item?.title || ''
        return (
          <View >
            {Boolean(item.type === 2) && 
              <View style={[styles.contentTitleContainer, {marginBottom: 6}]}>
                <View style={styles.line}/>
                <Text style={styles.contentTitleText}>{title}</Text>
                <View style={styles.line}/>
              </View>
            }
            {Boolean(item.type !== 2) && (
              <View style={{marginBottom: 6}}>
                <View style={styles.subSkuContainer}>
                  <View style={styles.subSkuDot}/>
                  <Text style={styles.subSkuTitle}>{title}</Text>
                </View>
                {renderStructure('inner', item)}
              </View>
            )}
          </View>
        )
      }) || null
      
      return (
        <View style={styles.skuBodyContainer}>
          {renderStructure('outer', ele)}
          {subSkuList}
        </View>
      )
    }

    return (
      <View style={styles.oneSkuContainer}>
        {renderSkuTitle()}
        {renderSkuBody()}
      </View>
    )
  }

  return (
    <MCModule backgroundColor='transparent'>
      <View style={styles.cardContainer}>
        <View style={styles.title}>
          <Text style={styles.titleText}>项目详情</Text>
        </View>
        <View style={styles.content}>
          {
            Boolean(moduleList) && moduleList.map((elem, index) => {
              const title = elem?.title || ''
              const skuList = elem?.skuList || []
              return (
                <>
                  <View style={[styles.contentTitleContainer, {marginTop: index === 0 ? 14 : 10}]}>
                    <View style={styles.line}/>
                    <Text style={styles.contentTitleText}>{title}</Text>
                    <View style={styles.line}/>
                  </View>
                  <View style={styles.contentSkuListContainer}>
                    {skuList.map(ele => renderSku(ele))}
                  </View>
                </>
              )
            })
          }
        </View>
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  cardContainer: {
    width: '100%',
    paddingHorizontal: 12
  },
  title: {
    width: '100%',
    height: 42,
    marginTop: 12,
    paddingLeft: 12,
    justifyContent: 'center',
    alignItems: 'flex-start',
    backgroundColor: 'transparent'
  },
  titleText: {
    color: '#222222',
    fontSize: 18,
    fontWeight: '500',
  },
  content: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
  },
  contentTitleContainer: {
    width: '100%',
    height: 14,
    marginTop: 10,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentTitleText: {
    color: '#222222',
    fontSize: 11,
    fontWeight: '400',
    marginHorizontal: 10
  },
  contentSkuListContainer: {
    width: '100%',
    justifyContent: 'center',
    alignContent: 'center',
    marginHorizontal: 6,
  },
  oneSkuContainer: {
    width: 339 * rate, 
  },
  skuTitleContainer: {
    width: 339 * rate,
    minHeight: 36,
    backgroundColor: '#F9F9F9',
    paddingHorizontal: 6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  skuTitleText: {
    width: 230 * rate,
    marginVertical: 9,
    color: '#222222',
    fontSize: 14,
    fontWeight: '600'
  },
  skuInfoContainer: {
    height: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  projectCount: {
    color: '#999999',
    fontSize: 12,
    fontWeight: '400'
  },
  projectPrice: {
    color: '#222222',
    fontSize: 14,
    marginLeft: 14
  },
  skuBodyContainer: {
    borderWidth: 0.5,
    borderTopWidth: 0,
    borderColor: '#F0F0F0',
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
    paddingVertical: 6,
    marginBottom: 6
  },
  skuRowContainerInner: {
    width: 302 * rate,
    minHeight: 17,
    backgroundColor: '#FFFFFF',
    marginVertical: 3,
    marginLeft: 25,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  skuRowContainerOuter: {
    width: 302 * rate,
    minHeight: 17,
    backgroundColor: '#FFFFFF',
    marginVertical: 3,
    marginLeft: 6,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  skuRowNameInner: {
    color: '#666666',
    fontSize: 12,
    fontWeight: '400',
    lineHeight: 17,
    marginRight: 3
  },
  skuRowNameOuter: {
    color: '#666666',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20,
    marginRight: 9
  },
  skuRowContentOuter: {
    color: '#222222',
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '400',
  },
  skuRowContentInner: {
    color: '#666666',
    fontSize: 12,
    lineHeight: 17,
    fontWeight: '400',
  },
  richTextContainer: {
    width: 262 * rate,
    marginVertical: 3,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  richTextLeftContainer: {
    width: 196 * rate,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start'
  },
  richTextNumberContainer: {
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: '#FFF1EC',
    justifyContent:'center',
    alignItems: 'center',
    marginRight: 6,
    marginTop: 1,
  },
  richTextNumberText: {
    color: '#FF4B10',
    fontSize: 10,
    fontFamily: 'MTfin2.0',
    fontWeight: '500',
  },
  richTextTitleContainer: {
    marginTop: -2,
  },
  richTextTitleText: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 14,
    marginBottom: 4
  },
  richTextSubtitleText: {
    color: '#666666',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12
  },
  richTextTimeContainer: {
    width: 46 * rate, 
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems:'flex-start'
  },
  richTextTimeText: {
    color: '#999999',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12,
  },
  line: {
    width: 40,
    height: 0.5,
    backgroundColor: '#D8D8D8'
  },
  subSkuContainer: {
    width: 323 * rate,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  subSkuDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#BBBBBB',
    marginHorizontal: 11
  },
  subSkuTitle: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4
  }
})

