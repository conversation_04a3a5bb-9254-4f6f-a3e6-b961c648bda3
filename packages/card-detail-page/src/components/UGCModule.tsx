/*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2023-12-06 14:01:53
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2024-01-03 19:04:31
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/mrn-beauty/packages/card-detail-page/src/components/UGC.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useState, useRef, useEffect, useCallback } from 'react'
import { getWidth, GCStyle, isDP } from '@mrn/mrn-gc-utils'
import { openUrl } from '@mrn/mrn-utils'
import { View, Text, TouchableOpacity, Image, StyleSheet, ScrollView } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { GravelStar } from '@mrn/gravel-react-native'
import { ShopUgcResponse, BaseInfoResponse } from '../types/index'

interface Props {
  shopUgc: ShopUgcResponse,
  baseInfo: BaseInfoResponse
}

const arrowUrl = 'https://p1.meituan.net/scarlett/177d3875a85d5fb9a2401394b3aec1e1441.png'
const ugcUpArrowUrl = 'https://p0.meituan.net/ingee/18a79a416970e0a61a1999e760ec7ab5473.png'
const ugcDownArrowUrl = 'https://p0.meituan.net/ingee/423e5f7ad2729a6246b0a763d9410b40528.png'
const defaultAvatar = 'https://p0.meituan.net/ingee/7626a3a414b5533142505e14f9c755ca13768.png'

export const UGCModule: React.FC<Props> = (props) => {
  const ugcRef0 = useRef(null)
  const ugcRef1 = useRef(null)
  const { totalCount, ugcContentList, tags } = props.shopUgc || {}
  const reviewListUrl = props.baseInfo?.reviewInfo?.reviewListUrl || ''
  const [showCommentContent, setShowCommentContent] = useState([false, false])
  const [showAllTextBtn, setShowAllTextBtn] = useState([true, true])
  const [commentContentHeight, setCommentContentHeight] = useState([null, null])
  const [numberOfLines, setNumberOfLines] = useState([0, 0])
  const [clickTag, setClickTag] = useState([false, false])

  // 首屏评论内容不足3行，隐藏展开收起按钮
  const changeShowAllTextBtn = useCallback((index) => {
    const newShowAllTextBtn = [...showAllTextBtn]
    newShowAllTextBtn[index] = false
    setShowAllTextBtn(newShowAllTextBtn)
  }, [showAllTextBtn, setShowAllTextBtn])

  // 切换展开收起状态
  const changeBtnStatus = useCallback((index) => {
    const newShowCommentContent = [...showCommentContent]
    newShowCommentContent[index] = !newShowCommentContent[index]
    setShowCommentContent(newShowCommentContent)
  }, [showCommentContent, setShowCommentContent])

  // 切换父容器高度
  const changeLineHeight = useCallback((index) => {
    const newCommentContentHeight = [...commentContentHeight]
    newCommentContentHeight[index] = commentContentHeight[index] ? null : 56.49
    setCommentContentHeight(newCommentContentHeight)
  }, [commentContentHeight, setCommentContentHeight])

  // 标识按钮是否已点击
  const changeClickTag = useCallback((index) => {
    const newIsClick = [...clickTag]
    newIsClick[index] = true
    setClickTag(newIsClick)
  }, [clickTag, setClickTag])

  const clickBtn = useCallback((index) => {
    changeClickTag(index)
    changeBtnStatus(index)
    changeLineHeight(index)
  }, [changeClickTag, changeBtnStatus, changeLineHeight])

  useEffect(() => {
    numberOfLines.map((item, index) => {
      if(item == 0) return

      if(!clickTag[index] && item > 3) {
        clickBtn(index)
      } else if(!clickTag[index] && item > 0 && item <= 3) {
        changeClickTag(index)
        changeShowAllTextBtn(index)
      }
    })
  }, [numberOfLines])

  if(!ugcContentList?.length) return null

  const renderCardTitle = () => {
    return (
      <View style={styles.titleContainer}>
        <View style={styles.titleContent}>
          <Text style={styles.titleText}>评价</Text>
          <Text style={styles.commentCount}>({totalCount})</Text>
        </View>
        <TouchableOpacity 
          style={styles.showMoreContainer}
          activeOpacity={0.5}
          onPress={() => openUrl(reviewListUrl)}
        >
          <Text style={styles.showMoreText}>查看全部</Text>
          <Image style={styles.arrow} source={{ uri: arrowUrl }}></Image>
        </TouchableOpacity>
      </View>
    )
  }

  const renderCardTag = () => {
    if(!tags) return null
    const tagNumOneLine = Math.floor((getWidth() - 12 * 4) / 77)
    const emptyTagNum = Math.ceil(tags.length / tagNumOneLine) * tagNumOneLine - tags.length
    
    return (
      <View style={styles.tagContainer}>
        {tags.map(item => {
          return (
            <TouchableOpacity 
              style={styles.tagContent}
              activeOpacity={0.5}
              onPress={() => openUrl(reviewListUrl)}
            >
              <Text style={styles.tagText}>{item.name}</Text>
            </TouchableOpacity>
          )
        })}
        {new Array(emptyTagNum).fill(0).map(() => <View style={{width: 77, height: 30}} />)}
      </View>
    )
  }

  const renderComment = (item, index) => {
    const renderCommentTitle = () => {
      const { userInfo, createDate } = item || {}
      if(!userInfo) return null
  
      return (
        <View style={styles.spaceBetweenRow}>
          <View style={[styles.centerColumn, styles.centerRow]}>
            <View style={styles.commentAvatarContainer}>
              <Image style={styles.avatar} source={{ uri: userInfo.picUrl || defaultAvatar}} />
            </View>
            <Text style={styles.nickNameText}>{userInfo.nickName || '匿名用户'}</Text>
            <View style={styles.vipLevelContainer}>
              <Image style={styles.avatar} source={{ uri: userInfo.vipLevelIconUrl }} />
            </View>
          </View>
          <Text style={styles.commentCreateDate}>{createDate}</Text>
        </View>
      )
    }
  
    const renderCommentInfo = () => {
      const { score, totalPrice } = item || {}
      return (
        <View style={styles.commentInfoContainer}>
          <View style={styles.centerRow}>
            <GravelStar 
              score={score}
              size={GCStyle.selectValue(12, 13)}
              fontSizeOfScore={12}
              ratingMargin={isDP() ? 2 : 1}
              scoreText={''}
            />
            <View style={styles.commentInfoGap}/>
            <Text style={styles.commentInfoDesc}>消费后评价</Text>
            <View style={styles.commentInfoGap}/>
            {Boolean(totalPrice) && <Text style={styles.commentInfoSource}>¥{totalPrice}</Text>}
            {Boolean(totalPrice) && <View style={styles.commentInfoGap}/>}
            <Text style={styles.commentInfoSource}>来自大众点评</Text>
          </View>
        </View>
        
      )
    }
  
    const renderCommentContent = () => {
      const { reviewBody } = item || {}
      return (
        <TouchableOpacity
          activeOpacity={1}
          style={[styles.commentContentContainer, { height: commentContentHeight[index] }]}
          onPress={() => openUrl(reviewListUrl)}
        >
          <Text 
            style={styles.commentContentText} 
            ellipsizeMode={'tail'}
            onTextLayout={(event) => {
              const newNumberOfLines = [...numberOfLines]
              newNumberOfLines[index] = event?.nativeEvent?.lines?.length || 0
              setNumberOfLines(newNumberOfLines)
            }}
          >{reviewBody}</Text>
          {
            Boolean(showAllTextBtn[index]) && (
              <View style={[styles.commentContentBtnContainer, styles.centerRow]}>
                <LinearGradient
                  start={{ x: 0, y: 0.5 }}
                  end={{ x: 1, y: 0.5 }}
                  colors={['#FFFFFF99', '#FFFFFF']}
                  style={{
                    width: 12,
                    height: '100%'
                  }}
                >
                  <View style={styles.commentContentBtnLinearContainer}/>
                </LinearGradient>
                <TouchableOpacity
                  ref={index === 0 ? ugcRef0 : ugcRef1}
                  activeOpacity={1}
                  onPress={() => { clickBtn(index) }}
                  style={[styles.commentButtonRow, styles.centerRow]}
                >
                  <Text style={styles.commentButtonTitle}>全文</Text>
                  <Image style={styles.ugcArrow} source={{ uri: showCommentContent[index] ? ugcDownArrowUrl : ugcUpArrowUrl }} />
                </TouchableOpacity>
              </View>
            )
          }
        </TouchableOpacity>
      )
    }
  
    const renderCommentTradePic = () => {
      const { picUrls } = item || {}
      if(!picUrls) return
  
      return (
        <TouchableOpacity 
          style={styles.commentPicContainer}
          activeOpacity={1}
          onPress={() => openUrl(reviewListUrl)}
        >
          <ScrollView 
            horizontal={true} 
            showsHorizontalScrollIndicator={false}
          >
            {picUrls.map(item => (
                <View style={styles.commentPicRow}>
                  <Image style={styles.commentPic} source={{ uri: item }} />
                </View>
            ))}
          </ScrollView>
        </TouchableOpacity>
      )
    }

    return (
      <View style={styles.commentContainer}>
        {renderCommentTitle()}
        {renderCommentInfo()}
        {renderCommentContent()}
        {renderCommentTradePic()}
      </View>
    )
  }

  const renderCardFooter = () => {
    return (
      <TouchableOpacity
        activeOpacity={0.5}
        style={styles.commentDesc}
        onPress={() => openUrl(reviewListUrl)}
      >
        <Text style={styles.showMoreText}>全部 {totalCount} 条评价</Text>
        <Image style={styles.arrow} source={{ uri: arrowUrl }}></Image>
      </TouchableOpacity>
    )
  }

  return (
    <MCModule backgroundColor='transparent'>
      <View style={styles.cardContainer}>
        {renderCardTitle()}
        <View style={styles.cardContent}>
          {renderCardTag()}
          {ugcContentList.map((item, index) => {
            return renderComment(item, index)
          })}
          {renderCardFooter()}
        </View>
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  centerRow: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  centerColumn: {
    flexDirection: 'column',
    alignItems: 'center'
  },
  spaceBetweenRow: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  cardContainer: {
    width: '100%',
    paddingHorizontal: 12
  },
  titleContainer: {
    height: 42,
    marginTop: 12,
    paddingLeft: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  titleContent: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center'
  },
  titleText: {
    color: '#222222',
    fontSize: 18,
    fontWeight: '500',
  },
  commentCount: {
    paddingLeft: 3,
    color: '#222222',
    fontSize: 12,
    fontWeight: '400',
  },
  tipContainer: {
    width: 143,
    height: 16,
    paddingLeft: 3,
    marginLeft: 3,
    backgroundColor: '#FF4B10',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  tipText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '400',
  },
  tipIconContainer: {
    marginLeft: 3,
    height: 10,
    width: 10,
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    position: 'absolute',
    top: 3,
    right: 3,
  },
  tipIcon: {
    paddingTop: 2,
    textAlign: 'center',
    color: '#FF5703',
    fontSize: 8,
    fontWeight: '500',
    lineHeight: 8,
  },
  showMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingRight: 3,
  },
  showMoreText: {
    textAlign: 'right',
    color: '#222222',
    fontSize: 12,
    fontWeight: '400',
  },
  ugcArrow: {
    width: 12,
    height: 12,
  },
  arrow: {
    marginTop: 1,
    marginLeft: 1,
    width: 9,
    height: 9,
    tintColor: '#222222',
  },
  cardContent: {
    width: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
  },
  tagContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
    marginBottom: 6
  },
  tagContent: {
    width: 77,
    height: 30,
    backgroundColor: '#FFF1EC',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  tagText: {
    color: '#FF4B10',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12,
  },
  commentContainer: {
    marginBottom: 12,
  },
  commentAvatarContainer: {
    width: 27,
    height: 27,
    borderRadius: 13.5,
    overflow: 'hidden',
  },
  avatar: {
    width: '100%',
    height: '100%',
  },
  nickNameText: {
    paddingLeft: 6,
    color: '#222222',
    fontWeight: '500',
    fontSize: 14,
  },
  vipLevelContainer: {
    marginLeft: 3,
    width: 14,
    height: 14,
  },
  commentCreateDate: {
    color: '#999999',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 27
  },
  commentInfoContainer: {
    marginTop: 3,
  },
  commentInfoGap: {
    paddingVertical: 2,
    marginHorizontal: 6,
    height: 12,
    width: 1,
    backgroundColor: '#E5E5E5'
  },
  commentInfoDesc: {
    color: '#FF4B10',
    fontWeight: '400',
    fontSize: 12,
    textAlign: 'left',
  },
  commentInfoSource: {
    color: '#999999',
    fontWeight: '400',
    fontSize: 12,
    textAlign: 'left',
  },
  commentContentContainer: {
    overflow: 'hidden',
    marginTop: 10,
    marginBottom: 12,
  },
  commentContentText: {
    color: '#222222',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 18.83,
    textAlign: 'left'
  },
  commentContentBtnContainer: {
    position: 'absolute',
    bottom: 0,
    right: -3,
    width: 55,
    height: 19,
    marginBottom: -2
  },
  commentContentBtnLinearContainer: {
    width: 12,
    height: 19,
  },
  commentButtonRow: {
    height: '100%',
    backgroundColor: '#FFFFFF',
  },
  commentButtonTitle: {
    color: '#004099',
    fontSize: 14,
    fontWeight: '500',
  },
  commentPicContainer: {
    width: '100%',
    borderRadius: 6,
    overflow: 'hidden',
  },
  commentPicRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    width: (getWidth() - 54) / 3 + 3
  },
  commentPic: {
    width: (getWidth() - 54) / 3,
    height: (getWidth() - 54) / 3,
  },
  commentDesc: {
    width: '100%',
    height: 24,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
})




