import React from 'react'
import { getWidth } from '@mrn/mrn-gc-utils'
import { View, Text, StyleSheet } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { BaseInfoResponse } from '../types/index'

const rate = getWidth() / 375

interface Props {
  baseInfo: BaseInfoResponse
}

export const PurchaseBenefitModule: React.FC<Props> = (props) => {
  const { benefits } = props.baseInfo || {}
  const { content } = benefits || {}

  if(!content) return null

  const renderRow = (elem, index) => {
    const renderDesc = (ele) => {
      return ele.map(el => <Text style={{ color: el.textColor, lineHeight: 20 }}>{el.text}</Text>)
    }

    return (
      <View style={styles.row}>
        <View style={styles.dot}/>
        <View style={{
          width: getWidth() - 24 - 34 - 15,
        }}>
          <Text style={[styles.rowTitle, {color: elem.title.textColor || '#222222'}]}>{elem.title.text}</Text>
          {elem.desc.map(ele => <Text style={styles.subTitle}>{renderDesc(ele)}</Text>)}
        </View>
      </View>
    )
  }

  return (
    <MCModule backgroundColor='transparent' style={styles.container}>
      <View style={styles.card}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>购买福利</Text>
        </View>
        <View style={styles.listContainer}>
          { content.map((item, index) => renderRow(item, index)) }
        </View>
      </View>
    </MCModule>

  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12
  },
  card: {
    width: getWidth() - 24,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingTop: 9,
    paddingHorizontal: 12,
    marginTop: 12,
  },
  titleContainer: {
    width: '100%',
    height: 22,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 6
  },
  title: {
    color: '#222222',
    fontSize: 18,
    fontWeight: '500',
  },
  listContainer: {
    width: 327 * rate
  },
  row: {
    width: getWidth() - 24 - 34,
    paddingLeft: 3,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  dot: {
    width: 4,
    height: 4,
    backgroundColor: '#BBBBBB',
    borderRadius: 2,
    marginRight: 11,
    marginTop: 8
  },
  rowTitle: {
    color: '#222222',
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 6
  },
  subTitle: {
    color: '#222222',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20,
  },
  line: {
    width: 307 * rate,
    height: 0.5,
    backgroundColor: '#E5E5E5',
    marginTop: 5
  },
})

