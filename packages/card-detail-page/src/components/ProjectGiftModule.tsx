import React from 'react'
import { getWidth } from '@mrn/mrn-gc-utils'
import { View, Text, StyleSheet } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { BaseInfoResponse } from '../types/index'

const rate = getWidth() / 375

interface Props {
  baseInfo: BaseInfoResponse
}

export const ProjectGiftModule: React.FC<Props> = (props) => {
  const { freeItems } = props.baseInfo || {}

  if(!freeItems?.length) return null


  const renderRow = (elem, index) => {
    const itemName = elem?.itemName || ''
    const desc = elem?.desc || ''
    const availableTimes = elem?.availableTimes || ''
    const marketPrice = elem?.marketPrice || ''

    return (
      <View style={styles.row}>
        <View style={styles.dot}/>
        <View style={{
          width: getWidth() - 24 - 34 - 15
        }}>
          <View style={styles.rowTitleContainer}>
            <Text style={styles.rowTitle}>{itemName}</Text>
            <View style={styles.rowTitleRightContainer}>
              <Text  style={styles.availableTimes}>({availableTimes}次)</Text>
              <Text style={styles.marketPrice}>¥{marketPrice}</Text>
            </View>
          </View>
          <View style={{
            width: getWidth() - 24 - 34 - 15,
          }}>
            <Text style={styles.descText}>{desc}</Text>
          </View>
        </View>
      </View>
    )
  }

  return (
    <MCModule backgroundColor='transparent' style={styles.container}>
      <View style={styles.card}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>项目赠送</Text>
        </View>
        <View style={styles.listContainer}>
          { freeItems.map((item, index) => renderRow(item, index)) }
        </View>
      </View>
    </MCModule>

  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 12
  },
  card: {
    width: getWidth() - 24,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingTop: 9,
    paddingHorizontal: 12,
    marginTop: 12,
  },
  titleContainer: {
    width: '100%',
    height: 22,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 6
  },
  title: {
    color: '#222222',
    fontSize: 18,
    fontWeight: '500',
  },
  listContainer: {
    width: 327 * rate
  },
  row: {
    width: getWidth() - 24 - 34,
    paddingLeft: 3,
    marginBottom: 12,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  dot: {
    width: 4,
    height: 4,
    backgroundColor: '#BBBBBB',
    borderRadius: 2,
    marginRight: 11,
    marginTop: 8
  },
  rowTitleContainer: {
    marginBottom: 6,
    width: getWidth() - 24 - 34 - 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  rowTitle: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 14,
  },
  rowTitleRightContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center'
  },
  availableTimes: {
    color: '#999999',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 12,
  },
  marketPrice: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 14,
    marginLeft: 9
  },
  descText: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 20
 }
})

