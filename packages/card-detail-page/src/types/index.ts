/*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2024-01-03 10:40:09
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2024-01-03 15:08:45
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/mrn-beauty/packages/card-detail-page/src/types/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// 次卡详情
export interface BaseInfoRequest {
  productId: number,
  shopId?: string,
  recommendId?: string,
  channelSource?: number,
  clientType?: number,
  version?: string,
  pass_param?: string,
  lat: string,
  lng: string,
  cityId?: number
}

export interface BaseInfoResponse {
  cardInfo: CardInfoDTO,
  collect: boolean,
  projectList: ProjectListDTO[],
  reviewInfo: ReviewInfoDTO,
  guarantee: GuaranteeItemDTO[],
  guaranteeLayer: GuaranteeLayerDTO,
  reminder: GuaranteeItemDTO[],
  reminderLayer: ReminderLayerDTO,
  applyShop: ApplyShopDTO,
  buyBtn: BuyBtnDTO,
  imageTextDetail: imageTextDetailDTO[],
  purchaseReminder: {
    refundTable?: RefundTableDTO
    content: purchaseReminderContentDTO[]
  },
  freeItems: freeItemsDTO[]
  benefits: {
    content: purchaseReminderContentDTO[]
  },
  heightRestricted?: boolean
}

export interface freeItemsDTO {
  itemName: string,
  desc: string,
  availableTimes: number,
  marketPrice: string
}

export interface imageTextDetailDTO {
  type: string,
  content: string
}

export interface purchaseReminderContentDTO {
  title: ContentItemDTO,
  desc: ContentItemDTO[][]
}

export interface ContentItemDTO {
  text: string,
  icon: string,
  textColor: string
}

export interface CardInfoDTO {
  productId: number,
  productTypeId: number,
  cardName: string,
  cardPrice: string,
  cardTotalPrice: string,
  marketPrice: string,
  marketTotalPrice: string,
  timesCardType: number,
  productItemId: number,
  saleCountInfo: string,
  totalCount: number,
  discountDesc: string,
  video: {
      url: string,
      desc: string,
      scale: string
  },
  status: number,
  pictures: CardInfoPicturesDTO[],
  promoStrengthTags: string[],
  promoModule: PromoModuleDTO
}

export interface PromoModuleDTO {
  promoPrice: string,
  marketPrice: string,
  promoCalculateList: promoCalculateDTO[],
  promoSummaryList: promoSummaryListDTO[]
}

export interface promoSummaryListDTO {
  iconTag: string,
  promoName: string,
  promoAmount: string
}

export interface promoCalculateDTO {
  calAmount: string,
  count: number,
  desc: string
}

export interface CardInfoPicturesDTO {
  url: string,
  desc: string,
  scale: string
}

export interface ProjectListDTO {
  title: string,
  desc: string
}

export interface ReviewInfoDTO {
  goodReviewRatio: string,
  reviewListUrl: string,
  userPics: string[],
  reviewPhrase: string,
  totalReviewDesc: string
}

export interface GuaranteeItemDTO {
  text: string,
  textColor: string,
  icon: string
}

export interface GuaranteeLayerDTO {
  title: string,
  guaranteeDetails: GuaranteeDetailsDTO[]
}

export interface GuaranteeDetailsDTO {
  icon: string,
  content: GuaranteeDetailsContentDTO[],
  mainTitle: string
}

export interface GuaranteeDetailsContentDTO {
  title: GuaranteeItemDTO
  desc: GuaranteeItemDTO[][]
}

export interface ReminderLayerDTO {
  title: string
  content: GuaranteeDetailsContentDTO[]
  refundTable?: RefundTableDTO
}

export interface RefundTableDTO {
  tableHead: ContentItemDTO
  tableBodies: RefundTableItemDTO[]
  tableFoot: ContentItemDTO
  toast: ContentItemDTO
}

export interface RefundTableItemDTO {
  useTimes: ContentItemDTO
  refundMoney: ContentItemDTO
}

export interface ApplyShopDTO {
  shopName: string
  applyShopCount: number
  applyShopListUrl: string
  shopUrl: string
  shopId: number
}

export interface BuyBtnDTO {
  btnTitle: string,
  redirectUrl: string,
  btnEnable: boolean,
  btnTag: string,
  priceStr: string
}

// 次卡服务项目模块
export interface ServiceModuleRequest {
  productId: number,
  platform: number,
  clientType?: number,
  version?: string
}

export interface ServiceModuleResponse {
  title: string,
  moduleList: any
}

export interface moduleListDTO {
  title: string,
  skuList: SkuListDTO[]
}

export interface SkuListDTO {
  title: string,
  subTitle: string,
  desc: string,
  count: number,
  price: string,
  icon: string,
  items: SkuListItemsDTO[]
}

export interface SkuListItemsDTO {
  name: string,
  value: string,
  icon: string,
  valueAttrs: ValueAttrsDTO
}

export interface ValueAttrsDTO {
  info: string[],
  values: string,
  name: string
}

// UGC模块
export interface ShopUgcRequest {
  shopid: number,
  shopuuid: number,
  channel: string,
  pageno: number
}

export interface ShopUgcResponse {
  shopName: string,
  averageScore: number,
  totalCount: number,
  ugcContentList: UgcContentDTO[],
  scoreList: any,
  tags: TagsDTO[]
}

export interface UgcContentDTO {
  userInfo: UserInfoDTO,
  reviewId: number,
  reviewBody: string,
  createDate: string,
  score: number,
  productItems: any,
  picTotal: number,
  totalPrice: any,
  picUrls: string[],
  scanCount: number,
  flowerTotal: number,
  followNoteNo: number,
  feedDetailUrl: string,
  technician: TechnicianDTO[],
  reviewScores: ReviewScoresDTO[],
  anonymous: boolean,
  platform: number
}

export interface UserInfoDTO {
  nickName: string,
  picUrl: string,
  userLevel: number,
  vipLevel: number,
  vipLevelIconUrl: string
}

export interface TechnicianDTO {
  technicianId: number,
  name: string,
  title: string,
  photoUrl: string
}

export interface ReviewScoresDTO {
  icon: string,
  scoreValue: number,
  desc: string
}

export interface TagsDTO {
  name: string,
  affection: number,
  count: number,
  rankType: number
}

export interface FavorRequest {
  token: string,
  productId: number,
  userId: string,
  uuid: string,
  cityId: string,
}

export interface FavorResponse {

}
