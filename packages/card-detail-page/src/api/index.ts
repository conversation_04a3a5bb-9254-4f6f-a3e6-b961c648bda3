/*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2024-01-03 10:39:45
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2024-01-08 15:43:22
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/mrn-beauty/packages/card-detail-page/src/api/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 禁用风控 //disableRisk即 SAKBaseModel中disableRisk // iOS MRN 2.5.3 支持
import { request } from '@mrn/mrn-utils'
import Dialog from '@max/leez-dialog'
import {
  BaseInfoRequest,
  BaseInfoResponse,
  ServiceModuleRequest,
  ServiceModuleResponse,
  ShopUgcRequest,
  ShopUgcResponse,
  FavorRequest,
  FavorResponse
} from '../../src/types'
import { isDP, getAppVersion, isMT } from '@mrn/mrn-gc-utils'
import { env } from '@mrn/mrn-utils'

const MT_API_ORIGIN = env.isBeta ? 'https://collection.wpt.test.sankuai.com' : 'https://apimobile.meituan.com'
const DP_API_ORIGIN = env.isBeta ? 'https://m.51ping.com' : 'https://m.dianping.com'

const errorHandle = error => {
  const title = error.message || '网络异常，请稍后重试'
  const dialogInstance = Dialog.open({
    title: title,
    showCancelButton: false,
    showConfirmButton: false,
    showCloseIcon: false,
    maskClosable: true,
    onClosePress: () => {
      dialogInstance.close()
    }
  })
}

const requestConfig = {
  url: '',
  method: 'GET',
  // baseURL: process.env.NODE_ENV !== 'production' ? 'https://mapi.51ping.com' : 'https://mapi.dianping.com',
  baseURL: 'https://mapi.dianping.com',
  params: {},
  // 模拟登陆用
  headers: {
    // cookie: 'testUserId=**********;',
    appName: isDP() ? '' : 'meituan'
  },
  options: {
    disableRisk: false,
    registerCandyHost: false
  }
}

export const fetchDetailBaseInfo: (params: BaseInfoRequest) => Promise<BaseInfoResponse> = (params: BaseInfoRequest) => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/api/vc/merchantcard/timescard/navigation/baseinfo',
    params
  })
  console.log('params', params)
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.msg
      } else {
        console.error(`fetchDetailBaseInfo error`)
      }
    })
    .catch(error => {
      // errorHandle(error)
      console.error(`fetchDetailBaseInfo:${error}`)
    })
}

export const fetchDetailService: (params: ServiceModuleRequest) => Promise<ServiceModuleResponse> = (params: ServiceModuleRequest) => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/api/vc/merchantcard/timescard/navigation/servicemodule',
    params
  })
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.msg
      } else {
        console.error(`fetchDetailService error`)
      }
    })
    .catch(error => {
      console.error(`fetchDetailService:${error}`)
    })
}

export const fetchShopUgc: (params: ShopUgcRequest) => Promise<ShopUgcResponse> = (params: ShopUgcRequest) => {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/dzbook/getshopugcs.json2',
    params
  })
  return request(_requestConfig)
    .then(response => {
      if (response.data.code === 200) {
        return response.data.data
      } else {
        console.error(`fetchDetailService error`)
      }
    })
    .catch(error => {
      console.error(`fetchDetailService:${error}`)
    })
}

export const checkFavor: (params: any) => Promise<any> = (params: any) => {
  const { userId, productId, token } = params
  const url = `/group/v1/user/${userId}/checkCollections?businessSource=22` // 原逻辑只有美团app需要查看初始收藏状态
  const newParams = {
    token,
    type: 22,
    ids: productId,
  }
  const _requestConfig = Object.assign({}, requestConfig, {
    baseURL: MT_API_ORIGIN,
    url,
    params: newParams
  })

  return request(_requestConfig)
    .then(response => {
      if(response && response.data.code === 1) {
        const ids = response.data.data || []
        if(ids.indexOf(Number(productId)) > -1 || ids.indexOf(productId) > -1) {
          return true
        } else {
          return false
        }
      } else {
        console.error(`checkFavor error`)
      }
    })
    .catch(error => {
      console.error(`checkFavor:${error}`)
    })
}

export const addFavor: (params: FavorRequest) => Promise<boolean> = (params: FavorRequest) => {
  const { userId, uuid, cityId, productId, token } = params
  const url = isDP() ? `${DP_API_ORIGIN}/merchantcard/timescard/navigation/collect/add.raw` :
  `${MT_API_ORIGIN}/group/v1/user/${userId}/addCollections?uuid=${uuid}&ci=${cityId}&businessSource=22`

  let newParams, newRequestConfig, _requestConfig


  if(isDP()) {
    newParams = {
      productId
    }
    newRequestConfig = requestConfig
    _requestConfig = Object.assign({}, newRequestConfig, {
      baseURL: isDP() ? DP_API_ORIGIN : MT_API_ORIGIN,
      url,
      params: newParams
    })

  } else {
    newParams = {
      token,
      type: 22,
      ids: productId,
    }
    newRequestConfig = {
      ...requestConfig,
      method: 'POST',
      headers: {
        ...requestConfig.headers,
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }

    _requestConfig = Object.assign({}, newRequestConfig, {
      baseURL: isDP() ? DP_API_ORIGIN : MT_API_ORIGIN,
      url,
      data: newParams
    })
  }

  return request(_requestConfig)
    .then(response => {
      if ([1, 200].includes(response.data.code)) {
        return true
      } else {
        console.error(`addFavor error`)
      }
    })
    .catch(error => {
      console.error(`addFavor:${error}`)
    })
}

export const cancelFavor: (params: FavorRequest) => Promise<boolean> = (params: FavorRequest) => {
  const { userId, cityId, productId, token } = params
  const url = isDP() ? `${DP_API_ORIGIN}/merchantcard/timescard/navigation/collect/cancel.raw` :
  `${MT_API_ORIGIN}/group/v1/user/${userId}/delCollections?ci=${cityId}&businessSource=22`

  let newParams, newRequestConfig, _requestConfig

  if(isDP()) {
    newParams = {
      productId
    }
    newRequestConfig = requestConfig
    _requestConfig = Object.assign({}, newRequestConfig, {
      baseURL: isDP() ? DP_API_ORIGIN : MT_API_ORIGIN,
      url,
      params: newParams
    })

  } else {
    newParams = {
      token
    }

    const version = getAppVersion() || ''
    if (Number(version.slice(0, 3)) > 8.1) {
      newParams.deleteIds = productId + '_22';
    } else {
      newParams.type = 2;
      newParams.ids = productId;
    }

    newRequestConfig = {
      ...requestConfig,
      method: 'POST',
      headers: {
        ...requestConfig.headers,
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    }

    _requestConfig = Object.assign({}, newRequestConfig, {
      baseURL: isDP() ? DP_API_ORIGIN : MT_API_ORIGIN,
      url,
      data: newParams
    })
  }

  return request(_requestConfig)
    .then(response => {
      if ([1,200].includes(response.data.code)) {
        return false
      } else {
        console.error(`cancelFavor error`)
      }
    })
    .catch(error => {
      console.error(`cancelFavor:${error}`)
    })
}
