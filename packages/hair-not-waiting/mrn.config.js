// mrn.config.js 配置文档
// http://mrn.sankuai.com/docs/guide/conf.html#mrn-config-js
let iconfont
try {
  // 由于 talos 流水线 MRN-CAT 插件在 Yarn 插件前，
  // 该插件会 require('mrn.config.js')，但此时尚未安装 npm 依赖。
  // 此时直接忽略错误即可。
  iconfont = require('@max/leez-icon/font.js').fonts
} catch (e) {}
module.exports = {
  name: 'mrn-beauty-hair-not-waiting',
  main: './index.tsx', // 页面入口地址
  biz: 'gcbu',
  bundleType: 1,
  bundleDependencies: ['@mrn/mrn-base'],
  debugger: {
    //
    moduleName: 'index',
    initialProperties: {
      hideNavigationBar: true,
      gc_channel_containerId: 'gc_channel_containerId'
    }
  },
  // 转 H5 配置
  // one: {
  //   appConfig: {
  //     pages: [
  //       {
  //         name: 'mcone-template-mpa',
  //         path: 'index.tsx',
  //         enableShareAppMessage: true
  //       }
  //     ]
  //   }
  // }
  fonts: {
    ...iconfont,
    'Meituan Type': './src/assets/MeituanType-Bold.ttf'
  }
}
