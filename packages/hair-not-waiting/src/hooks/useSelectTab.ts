import { lxTrackMGEClickEvent } from '@mrn/mrn-utils'
import { useState, useEffect } from 'react'
interface SelectTab {
  projectCode: string
  intentDay: string
  intentTime: string
}

const useSelectTab = tabData => {
  const [selectTab, setSelectTab] = useState<SelectTab>({
    projectCode: '',
    intentDay: '',
    intentTime: ''
  })

  useEffect(() => {
    const projectCode = tabData?.projectList?.find(project => project?.selected)?.tagValue
    const intentDay = tabData?.dayList?.find(day => day?.selected)?.tagValue
    let intentTime
    for (let index in tabData?.multiTimeList) {
      const timeList = tabData?.multiTimeList?.[index]
      intentTime = timeList?.find(time => time?.selected)?.tagValue
      if (intentTime) break
    }
    setSelectTab({
      projectCode,
      intentDay,
      intentTime
    })
  }, [tabData?.dayList, tabData?.multiTimeList, tabData?.projectList])

  const onSelectItem = (selectItem: { [key: string]: string }, name: string) => {
    lxTrackMGEClickEvent('gc', 'b_gc_0ywdo4lv_mc', 'c_gc_kq3wm1df', {
      title: name
    })
    setSelectTab(preState => ({
      ...preState,
      ...selectItem
    }))
  }

  return { selectTab, onSelectItem }
}

export default useSelectTab
