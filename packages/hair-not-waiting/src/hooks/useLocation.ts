import { useState, useEffect } from 'react'
import { KNBCityRes, KNBLocationRes, getCityAndLocation } from '../utils/location'

interface State {
  cityId: number
  lng: number
  lat: number
}

const useLocation = () => {
  const [location, setLocation] = useState<State>({
    cityId: null,
    lng: null,
    lat: null
  })

  useEffect(() => {
    getCityAndLocation().then((loc: KNBCityRes & KNBLocationRes) => {
      const { lng, lat, cityId } = loc || {}
      if (cityId) {
        setLocation({ lng, lat, cityId: Number(cityId) })
      }
    })
  }, [])

  return location
}

export default useLocation
