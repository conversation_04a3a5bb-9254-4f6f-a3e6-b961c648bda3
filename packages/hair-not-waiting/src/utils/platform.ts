import { env } from '@mrn/mrn-utils'
import { Platform } from '@mrn/react-native'
import { oneEnv } from '@nibfe/mc-one-env'

export const isAndroid = Platform.OS === 'android'
export const isIOS = Platform.OS === 'ios'

export function getAppVersion() {
  return env ? env.version : ''
}

// 点评所有有关端环境 点评app、点评微信小程序、点评站外等
export const isDP = oneEnv.app('dp')
// 美团所有有关端环境
export const isMT = oneEnv.app('mt')
// 小程序原生页面
export const isNativeMP = oneEnv.platform('mp')
// 小程序页面（原生 + h5）
export const isMP = oneEnv.platform('mp') || oneEnv.engine('wxmph5') || oneEnv.engine('ksmph5')
// h5页面，包含小程序h5
export const isH5 = oneEnv.platform('web')
// 美团所有端h5页面
export const isMTWeb = oneEnv.app('mt') && isH5
// 点评所有端h5页面
export const isDPWeb = oneEnv.app('dp') && isH5
//  微信小程序webview
export const isWXMPWebview = oneEnv.engine('wxmph5')
// 公司内app环境，目前包括美团 + 点评
export const isApp = oneEnv.platform('mrn') || (oneEnv.platform('web') && oneEnv.engine('titans'))

// 用于保存当前的h5环境是测试还是线上
export const H5EnvInfo = {
  isTest: false
}
