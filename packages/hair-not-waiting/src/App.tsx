import React, { useRef } from 'react'
import { View, ImageBackground, Dimensions, Animated } from '@mrn/react-native'
import { AnimatedMCPage } from '@nibfe/doraemon-practice'
import { NavModule } from 'src/components/nav/NavModule'
import { getRecommendHeight } from '@nibfe/dm-navigation'
import { isMT } from '@mrn/mrn-gc-utils'
import LX from '@analytics/mrn-sdk'
import {
  Provider as ThemeProvider,
  APPDP_THEME_GCUI,
  APPMT_THEME_GCUI
} from '@nibfe/theme-provider-lighter'
import { MTDProvider } from '@ss/mtd-react-native'
import { RecommendedList } from 'components/list/RecommendedList'
import { BookedShopList } from 'components/list/BookedShopList'
import { SearchModule } from 'components/search/SearchModule'
import useModel from './model'

// 定义Props接口
interface HairNotWaitingProps {
  gc_channel_containerId: string // 根据实际需要的类型替换
}

const HairNotWaiting: React.FC<HairNotWaitingProps> = ({ gc_channel_containerId }) => {
  const {
    showModal,
    setShowModal,
    tabData,
    orderData,
    loadMoreData,
    listState,
    loadData,
    dataSource,
    handleSubmit,
    getTabInfo,
    status,
    selectDay,
    submitSuccess
  } = useModel()

  const scrollY = useRef(new Animated.Value(0)).current

  return (
    <View style={{ flex: 1 }}>
      <NavModule
        scrollY={scrollY}
        gc_channel_containerId={gc_channel_containerId}
        title="新年美发不用等"
      />
      <AnimatedMCPage
        contentBackgroundColor="#F8F8F8"
        pageGap={0}
        pageTopGap={getRecommendHeight()}
        pageBottomGap={0}
        paddingHorizontal={0}
        showScrollIndicator={false}
        separatorLineStyle={{
          display: 'hidden-all'
        }}
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
          useNativeDriver: true
        })}
        scrollEventThrottle={1}
        contentBackgroundView={
          <ImageBackground
            source={{
              uri: 'https://p0.meituan.net/travelcube/1fe53ce03c075db1b7923bc5bd0b2cdd408397.png'
            }}
            style={{
              height: getRecommendHeight() + 20,
              width: Dimensions.get('window').width
            }}
          />
        }
        mptInfo={{
          category: 'gc',
          cid: 'c_gc_kq3wm1df',
          labs: {
            cat_id: '-9999'
          }
        }}
        onDisappear={() => {
          LX.pageDisappear({
            category: 'gc',
            pageInfoKey: 'hair-not-waiting'
          })
        }}
        modules={[
          {
            moduleKey: 'SearchModule',
            module: (
              <SearchModule
                gc_channel_containerId={gc_channel_containerId}
                showModal={showModal}
                tabData={tabData}
                orderData={orderData}
                onSubmit={handleSubmit}
                toggleModal={() => {
                  if (!showModal) getTabInfo()
                  setShowModal(!showModal)
                }}
                status={status}
                selectDay={selectDay}
                submitSuccess={submitSuccess}
              />
            )
          },
          {
            moduleKey: 'BookedShopList',
            module: <BookedShopList dataSource={dataSource} />
          },
          {
            moduleKey: 'RecommendedList',
            module: (
              <RecommendedList
                listState={listState}
                loadData={loadData}
                loadMoreData={loadMoreData}
              />
            )
          }
        ]}
      />
    </View>
  )
}

export default (props: HairNotWaitingProps) => {
  return (
    <ThemeProvider theme={isMT() ? APPMT_THEME_GCUI : APPDP_THEME_GCUI}>
      <MTDProvider>
        <HairNotWaiting {...props} />
      </MTDProvider>
    </ThemeProvider>
  )
}
