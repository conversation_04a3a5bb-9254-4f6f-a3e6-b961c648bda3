import React from 'react'
import { View, StyleSheet } from '@mrn/react-native'
import { getInset } from '@mrn/react-native-safe-area-view'
import { MCModule } from '@nibfe/doraemon-practice'
import Shadow from '@max/leez-shadow'
import LButton from '@max/leez-button'

interface Props {}

export const FooterModule: React.FC<Props> = () => {
  const onPress = () => {}
  return (
    <MCModule paddingLeft={0} paddingRight={0} hoverType="alwaysHoverBottom" hoverOffset={0}>
      <Shadow
        shadowColor="rgba(0,0,0,0.2)"
        xOffset={0}
        yOffset={-1}
        shadowBlur={6}
        elevationAndroid={3}
        style={{ with: '100%' }}
      >
        <View style={styles.container}>
          <LButton style={{ width: '100%' }} onPress={onPress} text={'提交'} />
        </View>
      </Shadow>
      {/* <View style={styles.container}>
        <Text>底部按钮</Text>
      </View> */}
    </MCModule>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    height: 64 + getInset('bottom'),
    backgroundColor: '#ffffff'
  },
  left: {},
  backImage: {
    width: 24,
    height: 24
  },
  title: {
    fontWeight: '500',
    fontSize: 16
  },
  right: {}
})
