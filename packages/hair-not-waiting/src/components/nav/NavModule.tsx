import React from 'react'
import { Icon } from '@nibfe/gc-ui'
import { View, Text, Animated, TouchableWithoutFeedback, StyleSheet } from '@mrn/react-native'
import { getRecommendHeight, getRecommendPaddingTop } from '@nibfe/dm-navigation'
import { pageRouterClose } from '@mrn/mrn-utils'
import KNB from '@mrn/mrn-knb'
interface Props {
  scrollY: Animated.Value
  gc_channel_containerId: string
  title: string
}

export const NavModule: React.FC<Props> = ({ scrollY, title, gc_channel_containerId }) => {
  const back = () => {
    if (gc_channel_containerId) {
      KNB.publish({
        type: 'native',
        action: 'gc_switch_to_home',
        data: {
          containerId: gc_channel_containerId
        }
      })
    } else {
      pageRouterClose()
    }
  }

  // 下层按钮
  const BottomButtons = () => {
    return (
      <View style={styles.row}>
        <TouchableWithoutFeedback onPress={back}>
          <Icon name="back" tintColor="white" size={18} />
        </TouchableWithoutFeedback>
        <Text style={styles.title}>{title}</Text>
      </View>
    )
  }
  // 上层按钮
  const TopButtons = () => {
    return (
      <Animated.View
        style={[
          styles.topButton,
          {
            opacity: scrollY.interpolate({
              inputRange: [0, 50],
              outputRange: [0, 1]
            })
          }
        ]}
      >
        <View style={styles.row}>
          <TouchableWithoutFeedback onPress={back}>
            <Icon name="back" tintColor="white" size={18} />
          </TouchableWithoutFeedback>
          <Text style={styles.title}>{title}</Text>
        </View>
      </Animated.View>
    )
  }
  return (
    <View style={styles.wrap}>
      <BottomButtons />
      <TopButtons />
    </View>
  )
}

const styles = StyleSheet.create({
  wrap: {
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 2
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 15,
    marginTop: getRecommendPaddingTop(),
    height: getRecommendHeight() - getRecommendPaddingTop()
  },
  backIcon: {
    width: 29,
    height: 29
  },
  title: {
    marginLeft: 6,
    color: '#fff',
    fontFamily: 'Meituan Type',
    fontWeight: '700',
    fontSize: 19
  },
  topButton: {
    height: getRecommendHeight(),
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FE4238'
  }
})
