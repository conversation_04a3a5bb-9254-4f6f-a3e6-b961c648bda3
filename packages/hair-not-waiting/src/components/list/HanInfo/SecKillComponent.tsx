import React from 'react'
import { View, Image, Text } from '@mrn/react-native'
import { GCStyle, isDP, isMT } from '@mrn/mrn-gc-utils'

/**
 * 秒杀组件
 */
export class SecKillComponent extends React.PureComponent<
  { secKillEndTime: number; onSecKillEnd: () => void },
  { countTime: string }
> {
  // 标签的总高度
  tagHeight = 15
  // 默认字体大小
  textSize = 10
  // 定时器句柄
  intervalHandler

  constructor(props) {
    super(props)
    this.state = {
      countTime: this.formatTimestamp(this.props.secKillEndTime - new Date().getTime())
    }
  }

  componentDidMount() {
    this.intervalHandler = setInterval(() => {
      if (this.props.secKillEndTime - new Date().getTime() < 0) {
        clearInterval(this.intervalHandler)
        this.props.onSecKillEnd && this.props.onSecKillEnd()
      }
      this.setState({
        countTime: this.formatTimestamp(this.props.secKillEndTime - new Date().getTime())
      })
    }, 1000)
  }

  componentWillUnmount() {
    this.intervalHandler && clearInterval(this.intervalHandler)
  }

  formatTimestamp = timeStamp => {
    if (timeStamp <= 0) {
      return '00:00:00'
    } else {
      return this.toHHmmss(timeStamp)
    }
  }

  toHHmmss(timeStamp: number) {
    const days: number = Math.floor((timeStamp / 1000 / 60 / 60 / 24) % 365)
    const hours: number = Math.floor((timeStamp / 1000 / 60 / 60) % 24)
    const minutes: number = Math.floor((timeStamp / 1000 / 60) % 60)
    const seconds: number = Math.floor((timeStamp / 1000) % 60)

    let finalHours = 0
    if (days > 0) {
      finalHours += days * 24
    }
    if (hours > 0) {
      finalHours += hours
    }

    const finalMinuts = minutes < 0 ? 0 : minutes
    const finalSeconds = seconds < 0 ? 0 : seconds

    const time =
      (finalHours < 10 ? '0' + finalHours : finalHours) +
      ':' +
      (finalMinuts < 10 ? '0' + finalMinuts : finalMinuts) +
      ':' +
      (finalSeconds < 10 ? '0' + finalSeconds : finalSeconds)
    return time
  }

  render() {
    return (
      <View style={{ flexShrink: 1 }}>
        <View style={{ flexShrink: 1 }}>
          <View
            style={{
              alignItems: 'center',
              flexDirection: 'row',
              flexShrink: 1,
              height: this.tagHeight
            }}
          >
            <Image
              source={GCStyle.selectValue(
                require('./img/promo_bg_dp.png'),
                require('./img/promo_bg_mt.png')
              )}
              resizeMode={'stretch'}
              capInsets={{ left: 4, top: 0, right: 8, bottom: 0 }}
              style={{
                position: 'absolute',
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
                width: undefined,
                height: undefined
              }}
            />
            {Boolean(isMT()) && (
              <Image
                source={{
                  uri: 'https://p1.meituan.net/travelcube/9f7276ceaa123ee0898b9a1d376b006b5239.png'
                }}
                style={{ width: 29, height: 15 }}
                resizeMode="stretch"
              />
            )}
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginLeft: 0,
                flexShrink: 1,
                marginRight: 6,
                height: '100%'
              }}
            >
              <Text
                style={{
                  color: GCStyle.selectValue('#FF6633', '#FF2727'),
                  flexShrink: 1,
                  fontSize: this.textSize,
                  fontWeight: GCStyle.selectValue('normal', 'bold'),
                  marginLeft: GCStyle.selectValue(5, 0)
                }}
                numberOfLines={1}
              >
                {isDP() ? '仅剩 ' + this.state.countTime : this.state.countTime}
              </Text>
            </View>
          </View>
        </View>
      </View>
    )
  }
}
