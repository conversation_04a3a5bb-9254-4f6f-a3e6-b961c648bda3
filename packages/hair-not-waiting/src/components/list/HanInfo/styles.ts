import { GCStyle, IS_ANDROID } from '@mrn/mrn-gc-utils'
import { StyleSheet } from '@mrn/react-native'

export const styles = StyleSheet.create({
  Container: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    height: 16
  },
  LeftPromoContainer: {
    width: 88,
    paddingRight: 6,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
  HanIconContainer: {
    width: 16,
    height: 16
  },
  HanIcon: {
    width: 16,
    height: 16
  },
  specialshofar: {
    fontSize: 10,
    color: '#FF4B10',
    marginLeft: 2,
    fontWeight: 'bold',
    marginTop: IS_ANDROID ? 3 : 3.5
  },
  Price: {
    fontSize: 14,
    color: '#FF4B10',
    marginLeft: 0,
    fontFamily: 'MTfin-Regular3.0',
    fontWeight: 'bold',
    marginTop: -1
  },
  NormalShofar: {
    fontSize: 10,
    color: '#999999',
    marginLeft: 1,
    marginRight: 1,
    marginTop: 2,
    textDecorationLine: 'line-through'
  },
  priceContainer: {
    flexDirection: 'row',
    marginLeft: 2
  },
  OriginalPrice: {
    fontSize: 12,
    color: '#999999',
    fontWeight: '400',
    textDecorationLine: 'line-through'
  },
  DiscountContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 2,
    borderRadius: 3,
    borderWidth: 0.5,
    borderColor: '#FF4B10',
    height: 16
  },
  DiscountText: {
    paddingHorizontal: 3,
    color: '#FF4B10',
    fontSize: 10,
    fontFamily: 'PingFang SC',
    fontWeight: '500'
  },
  Title: {
    flex: 1,
    flexShrink: 1,
    marginLeft: 5,
    fontSize: 12,
    color: '#222'
  },
  Sale: {
    fontSize: 11,
    color: '#999',
    marginLeft: 4
  },
  CompareIcon: {
    width: 16,
    height: 16
  },
  CompareText: {
    fontSize: 10,
    color: '#FF4B10',
    marginRight: 3
  },
  timeCardText: {
    fontSize: GCStyle.selectValue(12, 10),
    color: GCStyle.selectValue('#FF6633', '#FF6000'),
    lineHeight: GCStyle.selectValue(16, 10),
    fontWeight: GCStyle.selectValue('bold', 'normal'),
    marginTop: IS_ANDROID ? 3 : 5
  },
  CompareContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 2,
    borderRadius: 3,
    borderWidth: 0.5,
    borderColor: '#FF4B10',
    height: 16,
    overflow: 'hidden'
  }
})
