/* eslint-disable react-native/no-inline-styles */
import React, { FC, useState } from 'react'
import { View, Image, Text } from '@mrn/react-native'
import { styles } from './styles'
import { SecKillComponent } from './SecKillComponent'
import { GCMRNTextView } from '@mrn/mrn-module-component'
import { GCStyle } from '@mrn/mrn-gc-utils'
export interface HanInfoProps {
  // 下挂数据
  richTitle: string
  originalPrice: string
  discount: string
  secKill: number
  promoLabel: string
  title: string
  price: string
  iconUrl: string
  tradeType?: number
  // 是否展示特殊样式的价格羊角符
  specialshofar?: boolean
  promoLabelImage?: string
  promoLabelImageWH?: number
  compare?: string
  shouldHideActivityView?: boolean
  extraJson?: string
}

/**
 * 下挂Item
 */
export const HanInfo: FC<HanInfoProps> = props => {
  /**
   * 秒杀结束的回调,重新刷新
   */
  const [, setUpdate] = useState({})
  const onSecKillEnd = () => {
    setUpdate({})
  }

  const {
    compare = '',
    promoLabelImageWH = 0,
    promoLabelImage = '',
    secKill,
    specialshofar,
    promoLabel = '',
    iconUrl,
    price,
    discount,
    originalPrice,
    richTitle,
    title,
    shouldHideActivityView = false,
    tradeType,
    extraJson
  } = props
  // 是否展示下挂数据
  if (!(title || richTitle)) return null
  // 左侧活动及优惠信息组件
  // 优先级： 秒杀活动 > 其它优惠标签
  let activityComponent: React.ReactElement | null = null
  // 是否要展示秒杀活动(秒杀结束时间有返回值且结束时间大于当前时间且优惠标签弹窗的优惠项<=1)
  const hasSeckill = secKill > 0 && secKill > new Date().getTime()
  // 是否要展示优惠标签
  const hasDiscount = promoLabel && promoLabel.length > 0
  // 是否要展示优惠标签图片
  const hasDiscountImage = promoLabelImage && promoLabelImage.length > 0
  // 是否是富文本标题
  let isRichTitle: boolean = false
  // 是否包含透传次数
  let isRichSuffixTitle: boolean = false

  let richtexts = []
  try {
    if (extraJson) {
      const priceSuffix = JSON.parse(extraJson)?.priceSuffix
      if (priceSuffix) {
        const richtextlist = JSON.parse(priceSuffix)?.richtextlist
        if (richtextlist && richtextlist.length > 0) {
          isRichSuffixTitle = true
          richtexts = richtextlist
        }
      }
    }
  } catch (_) {}

  try {
    if (richTitle) {
      isRichTitle = JSON.parse(richTitle)?.richtextlist ? true : false
    } else {
      isRichTitle = false
    }
  } catch (_) {}

  if (hasSeckill) {
    // 秒杀标签
    activityComponent = <SecKillComponent secKillEndTime={secKill} onSecKillEnd={onSecKillEnd} />
  } else if (hasDiscount) {
    // 优惠标签
    activityComponent = (
      <View
        style={[
          {
            alignItems: 'center',
            flexDirection: 'row',
            flexShrink: 1,
            height: 15
          }
        ]}
      >
        <Image
          source={GCStyle.selectValue(
            require('./img/promo_bg_dp.png'),
            require('./img/promo_bg_mt.png')
          )}
          resizeMode={'stretch'}
          capInsets={{ left: 4, top: 0, right: 8, bottom: 0 }}
          style={{
            position: 'absolute',
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            width: undefined,
            height: undefined
          }}
        />
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            marginLeft: 5,
            flexShrink: 1,
            marginRight: 6,
            height: '100%'
          }}
        >
          <Text
            style={{
              color: GCStyle.selectValue('#FF6633', '#FF6000'),
              flexShrink: 1,
              fontSize: 10
            }}
            numberOfLines={1}
          >
            {promoLabel}
          </Text>
        </View>
      </View>
    )
  } else if (hasDiscountImage) {
    activityComponent = (
      <View
        style={{
          flexShrink: 1,
          height: 16
        }}
      >
        <Image
          source={{ uri: promoLabelImage }}
          style={{
            width: 16 * promoLabelImageWH,
            height: 16
          }}
        />
      </View>
    )
  } else {
    activityComponent = null
  }

  return (
    <View style={styles.Container}>
      {!shouldHideActivityView ? (
        <View style={styles.LeftPromoContainer}>
          {/* 活动标签 */}
          {activityComponent}
        </View>
      ) : null}
      {/* 下挂icon */}
      <View style={styles.HanIconContainer}>
        <Image source={{ uri: iconUrl }} style={styles.HanIcon} resizeMode="contain" />
      </View>
      <View style={styles.priceContainer}>
        {/* 特殊样式的羊角符 */}
        {specialshofar && Boolean(price) && <Text style={styles.specialshofar}>¥</Text>}
        {/* 价格 */}
        {Boolean(price) && <Text style={styles.Price}>{price}</Text>}
        {Boolean(tradeType === 8) && <Text style={styles.timeCardText}>/次</Text>}
        {/* 透传次数(仅显示第一个) */}
        {isRichSuffixTitle ? (
          <GCMRNTextView
            style={{ marginLeft: 2 }}
            textModelList={richtexts}
            text={richtexts[0].text ?? ''}
          />
        ) : null}
      </View>
      {/* 折扣 */}
      {Boolean(discount) && (
        <View style={styles.DiscountContainer}>
          <Text style={styles.DiscountText}>{discount}</Text>
        </View>
      )}
      {/* 比价标签 */}
      {Boolean(compare && compare.length > 0) && (
        <View style={styles.CompareContainer}>
          <Image
            source={{
              uri: GCStyle.selectValue(
                'https://p1.meituan.net/travelcube/64f3fa52d455d52b78f20bdc54469ad81288.png',
                'https://p0.meituan.net/travelcube/8f63ce6e89cc2285aeadaae35f558ade2355.png'
              )
            }}
            style={styles.CompareIcon}
          />
          <Text style={styles.CompareText}>{compare}</Text>
        </View>
      )}
      {/* 原价 */}
      <View style={styles.priceContainer}>
        {Boolean(originalPrice) && (
          <View style={{ justifyContent: 'center', alignItems: 'center', flexDirection: 'row' }}>
            <Text style={[styles.OriginalPrice, { textDecorationLine: 'none' }]} numberOfLines={1}>
              {'¥'}
            </Text>
            {/* 数字 */}
            <Text style={styles.OriginalPrice} numberOfLines={1}>
              {originalPrice}
            </Text>
          </View>
        )}
      </View>
      {/* 下挂名称 */}
      {richTitle && isRichTitle ? (
        <GCMRNTextView
          style={styles.Title}
          paragraphStyle={{ numberOfLines: 1 }}
          text={richTitle}
        />
      ) : (
        <Text style={styles.Title} numberOfLines={1}>
          {title}
        </Text>
      )}
    </View>
  )
}
