import React, { FC, useRef } from 'react'
import { MCModule } from '@nibfe/doraemon-practice'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'
import { ShopItem } from './ShopItem'
import { GCScrollView, GCScrollViewRef } from '@nibfe/gc-ui'

interface Props {
  lxLabs?: { [key: string]: any }
}

export const BookedShopList: FC<any> = props => {
  const { dataSource } = props
  const scrollViewRef = useRef<GCScrollViewRef | null>(null)

  const onClickItem = (item: any, index) => {
    lxTrackMGEClickEvent('gc', 'b_gc_aeuro2i5_mc', 'c_gc_kq3wm1df', {
      poi_id: item?.shopBaseInfo?.shopId || '-9999',
      index: index,
      type: 0
    })
  }

  return (
    <MCModule
      gapBottom={5}
      // loadingStatus={dataSource.loadingStatus}
      backgroundColor="#F8F8F8"
      onAppear={() => {
        scrollViewRef.current && scrollViewRef.current?.startExpose()
      }}
      onDisappear={() => {
        scrollViewRef.current && scrollViewRef.current?.cleanExpose()
      }}
    >
      <GCScrollView
        ref={scrollViewRef}
        bounces={false}
        showsVerticalScrollIndicator={false}
        scrollEnabled={false}
        scrollEventThrottle={16}
        keyExtractor={item => `key_${item?.shopBaseInfo?.shopId}`}
        data={dataSource?.listData || []}
        renderItem={(item, index) => {
          return <ShopItem item={item} index={index} type={0} onClickItem={onClickItem} />
        }}
        onExposeItem={(item, index) => {
          lxTrackMGEViewEvent('gc', 'b_gc_aeuro2i5_mv', 'c_gc_kq3wm1df', {
            poi_id: item?.shopBaseInfo?.shopId || '',
            index,
            type: 0
          })
          const tradeList = item?.tradeItems?.slice(0, 2) ?? []
          for (let i in tradeList) {
            const tradeItem = tradeList[i]
            lxTrackMGEViewEvent('gc', 'b_gc_b42lv5cl_mv', 'c_gc_kq3wm1df', {
              deal_id: tradeItem?.tradeId || '-9999',
              poi_id: item?.shopBaseInfo?.shopId || '-9999',
              index: index,
              type: 0
            })
          }
        }}
      />
    </MCModule>
  )
}
