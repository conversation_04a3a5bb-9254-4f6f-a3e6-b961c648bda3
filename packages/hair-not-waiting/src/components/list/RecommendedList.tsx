import React, { FC, useMemo } from 'react'
import { MCListModule, MCModule } from '@nibfe/doraemon-practice'
import { lxTrackMGEViewEvent, lxTrackMGEClickEvent } from '@mrn/mrn-utils'
import { ShopItem } from './ShopItem'
import { HeaderView, FooterView, EmptyView } from './ListExtraView'

export const RecommendedList: FC<any> = props => {
  const { listState, dataSource, loadData, loadMoreData } = props

  const onClickItem = (item: any, index) => {
    lxTrackMGEClickEvent('gc', 'b_gc_aeuro2i5_mc', 'c_gc_kq3wm1df', {
      poi_id: item?.shopBaseInfo?.shopId || '-9999',
      index: index,
      type: 1
    })
  }

  const isEnd = useMemo(() => {
    if (listState.loadingMoreStatus === 'done') return <FooterView />
    else return null
  }, [listState.loadingMoreStatus])

  if (!listState?.isEmpty) {
    return (
      <MCListModule
        backgroundColor="#F8F8F8"
        data={listState?.listData}
        isEmpty={listState.isEmpty}
        loadingStatus={listState.loadingStatus}
        onRetryForLoadingFail={loadData}
        onNeedLoadMore={loadMoreData}
        loadingMoreStatus={listState.loadingMoreStatus}
        onRetryForLoadingMoreFail={loadMoreData}
        // @ts-ignore
        keyExtractor={item => `key_${item?.shopBaseInfo?.shopId}`}
        renderItem={(item, index) => (
          <ShopItem
            // @ts-ignore
            key={`item_${item?.shopBaseInfo?.shopId}`}
            item={item}
            index={index}
            type={1}
            onClickItem={onClickItem}
          />
        )}
        exposeType="normal"
        onItemExpose={(item, index: number) => {
          lxTrackMGEViewEvent('gc', 'b_gc_aeuro2i5_mv', 'c_gc_kq3wm1df', {
            // @ts-ignore
            poi_id: item?.shopBaseInfo?.shopId || '',
            index,
            type: 1
          })
          // @ts-ignore
          const tradeList = item?.tradeItems?.slice(0, 2) ?? []
          for (let i in tradeList) {
            const tradeItem = tradeList[i]
            lxTrackMGEViewEvent('gc', 'b_gc_b42lv5cl_mv', 'c_gc_kq3wm1df', {
              deal_id: tradeItem?.tradeId || '-9999',
              // @ts-ignore
              poi_id: item?.shopBaseInfo?.shopId || '-9999',
              index: index,
              type: 0
            })
          }
        }}
        emptyView={<></>}
        headerView={<HeaderView title="或者，你可以直接预约商家，部分需要二次确认" />}
        footerView={isEnd}
      />
    )
  } else {
    return (
      <MCModule backgroundColor="transparent">
        <EmptyView style={{ marginTop: dataSource?.listData?.length ? 25 : 120 }} />
      </MCModule>
    )
  }
}
