/* eslint-disable react-native/no-inline-styles */
import React, { FC } from 'react'
import { View, Text, TouchableOpacity, StyleSheet, Image } from '@mrn/react-native'
import { GCStyle, isDP } from '@mrn/mrn-gc-utils'
import { GravelStar } from '@mrn/gravel-react-native'
import { Commontag } from '@mrn/mrn-gc-pflcomponent'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { lxTrackMGEClickEvent, openUrl } from '@mrn/mrn-utils'
import { HanInfo } from './HanInfo'

interface Props {
  item: any
  index: number
  type: number
  onClickItem?: (item: any, index) => void
}

export const ShopItem: FC<Props> = ({ item, index, type, onClickItem }) => {
  const { shopBaseInfo, activityTags, poiTags, bookInfo, buttonText, tradeItems } = item
  if (!item?.shopBaseInfo) return null

  const PoiInfo = () => {
    const { shopPic, shopName, shopScore, shopPower, regionName, distance } = shopBaseInfo
    return (
      <View style={styles.item}>
        <View style={styles.shopImageView}>
          <Image
            source={{
              uri: shopPic || ''
            }}
            style={styles.shopImage}
          />
          {Boolean(activityTags && activityTags?.length > 0) && (
            <View style={styles.tagView}>
              <Text style={styles.tagText}>{activityTags?.[0]?.tagText}</Text>
            </View>
          )}
        </View>
        <View style={styles.poiWrapper}>
          {Boolean(shopName) && (
            <Text style={styles.poiTitle} numberOfLines={1}>
              {shopName}
            </Text>
          )}
          <View style={styles.shopInfo}>
            <View style={styles.starInfo}>
              <GravelStar
                score={Number(shopPower) || 0}
                size={GCStyle.selectValue(12, 10)}
                fontSizeOfScore={12}
                ratingMargin={isDP() ? 2 : 1}
                scoreText={shopScore || ''}
              />
              <Text style={styles.location} numberOfLines={1}>
                {regionName || ''}
              </Text>
            </View>
            {Boolean(distance) && (
              <View>
                <Text style={styles.location} numberOfLines={1}>
                  {distance || ''}
                </Text>
              </View>
            )}
          </View>
          <View style={styles.bookWrapper}>
            <View style={{ flex: 1, marginRight: 10 }}>
              {Boolean(poiTags && poiTags.length > 0) && (
                <View style={styles.poiTag}>
                  <Commontag
                    tags={poiTags?.map(tag => {
                      return {
                        ...tag,
                        tagBizType: tag?.tagBizType === 140 ? 0 : tag?.tagBizType,
                        textContainerStyle:
                          tag?.tagBizType === 140
                            ? {
                                paddingHorizontal: 3,
                                backgroundColor: '#FFEDDE',
                                borderRadius: 8,
                                borderBottomLeftRadius: 1
                              }
                            : {},
                        textStyle: tag?.tagBizType === 140 ? { color: '#8E3C12' } : {},
                        tagIcon: tag?.tagBizType === 140 ? '' : tag?.tagIcon
                      }
                    })}
                    styles={{
                      tagItem: { marginLeft: 0, marginRight: 5 },
                      tagIcon: { width: 72.5, height: 16 },
                      tagLinear: { height: 16, paddingHorizontal: 5, backgroundColor: '#f6f6f6' },
                      tagText: { fontSize: 10, fontWeight: '400', color: '#666666' }
                    }}
                  />
                </View>
              )}
              <View style={styles.bookInfo}>
                {Boolean(bookInfo) && (
                  <Text style={styles.bookText} numberOfLines={1}>
                    {bookInfo || ''}
                  </Text>
                )}
              </View>
            </View>
            <LinearGradient
              style={styles.buttonBg}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              colors={['#FF661A', '#FF1F1F']}
            >
              <Text style={styles.buttonText}>{buttonText || ''}</Text>
            </LinearGradient>
          </View>
          {Boolean(tradeItems && tradeItems?.length > 0) && <View style={styles.line} />}
        </View>
      </View>
    )
  }

  const DealList = () => {
    const tradeList = tradeItems?.slice(0, 2) ?? []

    if (tradeList && tradeList?.length > 0) {
      return (
        <View>
          {tradeList.map((tradeItem: any, _index) => {
            const {
              extraJson,
              preTag,
              tradeContent,
              tradeIcon,
              promoTags,
              tradePrice,
              originalPrice,
              secKill,
              tradeTags,
              tradeUrl
            } = tradeItem
            return (
              <View style={{ marginBottom: _index !== tradeList.length - 1 ? 6 : 0 }}>
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={() => {
                    lxTrackMGEClickEvent('gc', 'b_gc_b42lv5cl_mc', 'c_gc_kq3wm1df', {
                      deal_id: tradeItem?.tradeId || '-9999',
                      poi_id: shopBaseInfo?.shopId || '-9999',
                      index: index,
                      type
                    })
                    if (tradeUrl) {
                      openUrl(tradeUrl)
                    }
                  }}
                >
                  <HanInfo
                    extraJson={extraJson}
                    specialshofar={true}
                    promoLabel={preTag?.tagText}
                    title={tradeContent}
                    richTitle={tradeContent}
                    iconUrl={tradeIcon}
                    discount={promoTags?.[0]?.tagText}
                    price={tradePrice}
                    originalPrice={originalPrice}
                    secKill={secKill}
                    promoLabelImage={preTag?.tagIcon}
                    compare={tradeTags?.[0]?.tagText}
                    promoLabelImageWH={preTag?.height > 0 ? preTag?.width / preTag?.height : 0}
                  />
                </TouchableOpacity>
              </View>
            )
          })}
        </View>
      )
    } else {
      return null
    }
  }

  return (
    <View style={styles.cardWrapper}>
      <TouchableOpacity
        activeOpacity={1}
        onPress={() => {
          onClickItem && onClickItem(item, index)
          if (shopBaseInfo?.shopUrl) {
            openUrl(shopBaseInfo?.shopUrl)
          }
        }}
      >
        <PoiInfo />
        <DealList />
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  cardWrapper: {
    marginHorizontal: 10,
    marginBottom: 10,
    padding: 10,
    borderRadius: 12,
    backgroundColor: '#fff'
  },
  item: {
    width: '100%',
    flexDirection: 'row'
  },
  shopImageView: {
    width: 80,
    height: 80,
    borderRadius: 6,
    overflow: 'hidden'
  },
  shopImage: {
    width: 80,
    height: 80
  },
  tagView: {
    width: '100%',
    height: 18,
    position: 'absolute',
    left: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FE3337'
  },
  tagText: {
    color: '#fff',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '400',
    fontSize: 12
  },
  poiWrapper: {
    flex: 1,
    marginLeft: 8
  },
  poiTitle: {
    marginTop: -2,
    height: 18,
    color: '#222222',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '500',
    fontSize: 15
  },
  shopInfo: {
    height: 18,
    marginTop: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  starInfo: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  location: {
    marginLeft: 6,
    color: '#666666',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '400',
    fontSize: 12
  },
  bookWrapper: {
    marginTop: 5,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end'
  },
  poiTag: {
    height: 16,
    overflow: 'hidden'
  },
  bookInfo: {
    height: 18,
    marginTop: 5,
    marginBottom: -3,
    flexDirection: 'row',
    alignItems: 'center'
  },
  bookText: {
    color: '#FE6C1E',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '400',
    fontSize: 12
  },
  buttonBg: {
    width: 68.5,
    height: 33.5,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center'
  },
  buttonText: {
    color: '#FFFFFF',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '600',
    fontSize: 13
  },
  line: {
    width: '100%',
    height: 0.5,
    backgroundColor: '#EEEEF0',
    marginVertical: 8
  }
})
