import React from 'react'
import { View, Text, StyleProp, ViewStyle, StyleSheet, Image } from '@mrn/react-native'

export const FooterView: React.FC<{ style?: StyleProp<ViewStyle>; title?: string }> = ({
  style,
  title = '- 没有更多了 -'
}) => {
  return (
    <View style={[styles.footerView, style]}>
      <Text style={styles.footerText}>{title}</Text>
    </View>
  )
}

export const HeaderView: React.FC<{ style?: StyleProp<ViewStyle>; title?: string }> = ({
  style,
  title
}) => {
  return (
    <View style={[styles.headerView, style]}>
      <Text style={styles.headerText}>{title}</Text>
    </View>
  )
}

export const EmptyView: React.FC<{ style?: StyleProp<ViewStyle>; title?: string }> = ({
  style
}) => {
  return (
    <View style={[styles.emptyView, style]}>
      <Image
        source={{
          uri: 'https://p0.meituan.net/travelcube/99b910e7282639b97c17df62c080683b387231.png'
        }}
        style={styles.emptyImage}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  headerView: {
    height: 35,
    paddingLeft: 10,
    flexDirection: 'row',
    alignItems: 'center'
  },
  headerText: {
    color: '#000',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '500',
    fontSize: 15
  },
  footerView: {
    alignItems: 'center',
    marginVertical: 10
  },
  footerText: {
    fontWeight: '400',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    color: '#777777',
    textAlign: 'center'
  },
  emptyView: {
    width: '100%',
    marginTop: 120,
    flexDirection: 'row',
    justifyContent: 'center'
  },
  emptyImage: {
    width: 250,
    height: 187.5,
    overflow: 'visible'
  }
})
