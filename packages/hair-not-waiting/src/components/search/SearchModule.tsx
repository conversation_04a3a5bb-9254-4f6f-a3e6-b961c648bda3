import React, { useRef, useEffect, useMemo, useCallback } from 'react'
import { MCModule } from '@nibfe/doraemon-practice'
import { Text, TouchableOpacity, View, StyleSheet, ScrollView, Image } from '@mrn/react-native'
import { SlideModal } from '@nibfe/gc-ui'
import Shadow from '@max/leez-shadow'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'
import useSelectTab from '../../hooks/useSelectTab'

interface Props {
  showModal?: boolean
  tabData?: any
  orderData?: any
  onSubmit?: (data: any) => void
  toggleModal?: () => void
  status?: string | number
  RevealOuts?: string
  selectDay?: string
  gc_channel_containerId?: string
  submitSuccess?: boolean
}

const SearchResult: React.FC<Props> = ({
  orderData,
  toggleModal,
  status,
  RevealOuts,
  submitSuccess
}) => {
  const isPolling = useCallback(
    (text: string) => {
      if ((!text || submitSuccess) && status === 2) {
        return {
          cardPic: 'https://p0.meituan.net/ingee/b4c4a0b5e239091f335094a19111c7fb1995.png',
          cardSubTitle:
            '正马不停蹄地与门店确认中，非营业时间回复较慢，请耐心等待，结果会以短信通知',
          cardTitle: '平台正与商家确认空闲中'
        }
      } else {
        return orderData
      }
    },
    [submitSuccess, orderData, status]
  )

  return (
    <Shadow shadowColor="#0000000F" xOffset={0} yOffset={3} shadowBlur={6} elevationAndroid={3}>
      <View style={styles.wrap}>
        <View style={styles.titleWrap}>
          <Text style={styles.title1}>平台帮你问</Text>
          <Text numberOfLines={1} style={styles.title2}>
            {RevealOuts}
          </Text>
        </View>
        <TouchableOpacity
          hitSlop={{
            top: 10,
            left: 10,
            bottom: 10,
            right: 10
          }}
          activeOpacity={1}
          onPress={() => {
            lxTrackMGEClickEvent('gc', 'b_gc_57ulnd1c_mc', 'c_gc_kq3wm1df', {
              title: orderData?.btn?.mainTitle || '-9999'
            })
            toggleModal()
          }}
        >
          {!orderData?.btn?.type ? (
            <LinearGradient
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              colors={['#FF661A', '#FF1F1F']}
              style={[styles.bigBtn]}
            >
              <Text style={styles.bigBtnText}>{orderData?.btn?.mainTitle}</Text>
            </LinearGradient>
          ) : (
            <Text style={styles.smallBtn}>{orderData?.btn?.mainTitle}</Text>
          )}
        </TouchableOpacity>
      </View>
      {Boolean(orderData?.hasIntent) && (
        <View style={styles.resultWrapper}>
          <View style={styles.result}>
            <Image
              source={{
                uri: isPolling(orderData?.cardPic)?.cardPic || ''
              }}
              style={styles.resultIcon}
            />
            <Text style={styles.mainTitle}>{isPolling(orderData?.cardTitle)?.cardTitle || ''}</Text>
          </View>
          <View style={styles.subTitleView}>
            <Text style={styles.subTitle}>
              {isPolling(orderData?.cardSubTitle)?.cardSubTitle || ''}
            </Text>
          </View>
        </View>
      )}
    </Shadow>
  )
}

const Modal: React.FC<Props> = ({
  showModal,
  tabData,
  onSubmit,
  toggleModal,
  gc_channel_containerId
}) => {
  const { selectTab, onSelectItem } = useSelectTab(tabData)
  const scrollViewRef = useRef(null)

  const selectedDayIndex = useMemo(() => {
    const index = tabData?.dayList?.findIndex(item => item?.tagValue === selectTab?.intentDay)
    return index > -1 ? index : 0
  }, [selectTab?.intentDay, tabData?.dayList])
  useEffect(() => {
    if (tabData?.multiTimeList && showModal) {
      let selectIndex = 0
      for (let index in tabData?.multiTimeList) {
        const timeList = tabData?.multiTimeList?.[index]
        selectIndex = timeList?.findIndex(time => time?.selected)
        if (selectIndex > -1) break
      }
      setTimeout(() => {
        if (scrollViewRef && selectIndex > 0) {
          const value = selectIndex * 82
          scrollViewRef?.current?.scrollTo({ x: value, animated: false })
        }
      }, 10)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tabData?.multiTimeList])

  useEffect(() => {
    if (showModal) {
      lxTrackMGEViewEvent('gc', 'b_gc_ki00h8mx_mv', 'c_gc_kq3wm1df', {})
      lxTrackMGEViewEvent('gc', 'b_gc_0330yoeo_mv', 'c_gc_kq3wm1df', {
        filter: selectTab
      })
      tabData?.projectList &&
        tabData?.projectList?.forEach(item => {
          lxTrackMGEViewEvent('gc', 'b_gc_0ywdo4lv_mv', 'c_gc_kq3wm1df', {
            title: item?.tagName || '-9999'
          })
        })
      tabData?.dayList &&
        tabData?.dayList?.forEach(item => {
          lxTrackMGEViewEvent('gc', 'b_gc_0ywdo4lv_mv', 'c_gc_kq3wm1df', {
            title: item?.tagName || '-9999'
          })
        })
      tabData?.multiTimeList?.[selectedDayIndex] &&
        tabData?.multiTimeList?.[selectedDayIndex]?.forEach(item => {
          lxTrackMGEViewEvent('gc', 'b_gc_0ywdo4lv_mv', 'c_gc_kq3wm1df', {
            title: item?.tagName || '-9999'
          })
        })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showModal])

  return (
    <SlideModal
      visible={showModal}
      direction="up"
      wrapperStyles={gc_channel_containerId ? styles.homepageModalWrapper : styles.modalWrapper}
      modalProps={{
        maskClosable: true,
        animateable: false,
        onPressClose: toggleModal
      }}
    >
      <View style={styles.modalView}>
        <LinearGradient colors={['#FFDEDE', '#FFFFFF']}>
          <View style={styles.title}>
            <View style={styles.titleHeader}>
              <Text style={styles.titleMain}>{tabData?.mainTitle?.[0]}</Text>
              <View style={styles.breakPoint} />
              <Text style={styles.titleTime}>{tabData?.mainTitle?.[1]}</Text>
            </View>
            <View>
              <Text style={styles.titleSize}>{tabData?.subTitle}</Text>
            </View>
          </View>
        </LinearGradient>
        <View style={styles.listTitle}>
          <Text style={styles.listTitleText}>{tabData?.guideTitle}</Text>
        </View>
        <View style={styles.buttonList}>
          <ScrollView
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            style={{ flexDirection: 'row' }}
          >
            {tabData?.projectList?.map(project => {
              const selected = project?.tagValue === selectTab?.projectCode
              return (
                <TouchableOpacity
                  activeOpacity={1}
                  hitSlop={{
                    top: 10,
                    bottom: 10
                  }}
                  onPress={() => {
                    onSelectItem({ projectCode: project.tagValue }, project?.tagName)
                  }}
                >
                  <View
                    style={selected ? styles.buttonSelect : styles.buttonFlex}
                    key={project?.tagName}
                  >
                    <Text style={selected ? styles.textSelect : styles.textNormal}>
                      {project?.tagName}
                    </Text>
                  </View>
                </TouchableOpacity>
              )
            })}
          </ScrollView>
          <View style={{ flexDirection: 'row', marginTop: 30 }}>
            {tabData?.dayList?.map((day, i) => {
              const selected = day?.tagValue === selectTab?.intentDay
              return (
                <TouchableOpacity
                  activeOpacity={1}
                  hitSlop={{
                    top: 10,
                    bottom: 10
                  }}
                  onPress={() => {
                    const intentTime = tabData?.multiTimeList?.[i]?.[0]?.tagValue
                    scrollViewRef?.current?.scrollTo({ x: 0, animated: false })
                    onSelectItem({ intentDay: day?.tagValue, intentTime }, day?.tagName)
                  }}
                >
                  <View
                    style={selected ? styles.buttonSelect : styles.buttonFlex}
                    key={day?.tagName}
                  >
                    <Text style={selected ? styles.textSelect : styles.textNormal}>
                      {day?.tagName}
                    </Text>
                  </View>
                </TouchableOpacity>
              )
            })}
          </View>
          <ScrollView
            ref={scrollViewRef}
            horizontal={true}
            showsHorizontalScrollIndicator={false}
            style={{ flexDirection: 'row', marginTop: 30 }}
          >
            {tabData?.multiTimeList?.[selectedDayIndex]?.map((time, i) => {
              const selected = time?.tagValue === selectTab?.intentTime
              return (
                <TouchableOpacity
                  activeOpacity={1}
                  hitSlop={{
                    top: 10,
                    bottom: 10
                  }}
                  onPress={() => {
                    onSelectItem({ intentTime: time?.tagValue }, time?.tagName)
                    if (scrollViewRef && i > 0) {
                      const value = i * 82
                      scrollViewRef?.current?.scrollTo({ x: value, animated: true })
                    }
                  }}
                >
                  <View
                    style={[styles.buttonView, selected ? styles.buttonSelect : styles.buttonFlex]}
                    key={time?.tagName}
                  >
                    <Text style={[selected ? styles.textSelect : styles.textNormal]}>
                      {time?.tagName}
                    </Text>
                  </View>
                </TouchableOpacity>
              )
            })}
          </ScrollView>
        </View>
        <View style={styles.tips}>
          <Text style={styles.tipsSize}>{tabData?.btn?.tip}</Text>
        </View>
        <View style={styles.parting} />
        <View style={styles.footButton}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={() => {
              onSubmit(selectTab)
            }}
          >
            <LinearGradient
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              colors={['#FF661A', '#FF1F1F']}
              style={[styles.footButtonBackground, { opacity: tabData?.btn?.available ? 1 : 0.3 }]}
            >
              <Text style={styles.footButtonSize}>{tabData?.btn?.mainTitle}</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
        <View style={styles.closeButton}>
          <TouchableOpacity
            hitSlop={{
              top: 10,
              left: 10,
              bottom: 10,
              right: 10
            }}
            activeOpacity={1}
            onPress={toggleModal}
          >
            <Image
              source={{
                uri: 'https://p0.meituan.net/travelcube/0f11d81ef4d781f11b6e5d18dd8e3b493597.png'
              }}
              style={{
                width: 22,
                height: 22
              }}
            />
          </TouchableOpacity>
        </View>
      </View>
    </SlideModal>
  )
}

export const SearchModule: React.FC<Props> = ({
  showModal,
  tabData,
  orderData,
  onSubmit,
  toggleModal,
  status,
  selectDay,
  gc_channel_containerId,
  submitSuccess
}) => {
  const RevealOuts = useMemo(() => {
    if (selectDay === orderData?.mainTitle && status === 2 && selectDay) {
      return orderData?.mainTitle
    } else {
      if (!selectDay) {
        return orderData?.mainTitle
      }
      return selectDay
    }
  }, [selectDay, orderData, status])

  return (
    <MCModule
      backgroundColor="transparent"
      gapBottom={10}
      onExpose={() => {
        lxTrackMGEViewEvent('gc', 'b_gc_57ulnd1c_mv', 'c_gc_kq3wm1df', {
          title: orderData?.btn?.mainTitle || '-9999'
        })
      }}
    >
      <SearchResult
        orderData={orderData}
        toggleModal={toggleModal}
        status={status}
        RevealOuts={RevealOuts}
        submitSuccess={submitSuccess}
      />
      {showModal ? (
        <Modal
          gc_channel_containerId={gc_channel_containerId}
          showModal={showModal}
          tabData={tabData}
          toggleModal={toggleModal}
          onSubmit={onSubmit}
        />
      ) : null}
    </MCModule>
  )
}

const styles = StyleSheet.create({
  wrap: {
    height: 40,
    marginHorizontal: 10,
    paddingLeft: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 31.5
  },

  titleWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },

  title1: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000'
  },

  title2: {
    marginLeft: 8,
    fontSize: 13,
    fontWeight: '400',
    color: '#4D4D4D'
  },

  smallBtn: {
    marginRight: 24,
    fontSize: 13,
    fontWeight: '600',
    color: '#FF5500'
  },

  bigBtn: {
    width: 68.5,
    height: 33.5,
    marginRight: 3,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center'
  },

  bigBtnText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#FFF'
  },

  resultWrapper: {
    marginTop: 18,
    paddingHorizontal: 10
  },

  result: {
    flexDirection: 'row',
    alignItems: 'center'
  },

  resultIcon: {
    width: 18,
    height: 18
  },

  mainTitle: {
    marginLeft: 6,
    fontSize: 18,
    fontWeight: '500',
    color: '#222222'
  },

  subTitleView: {
    marginTop: 8,
    marginBottom: 5
  },

  subTitle: {
    fontSize: 12,
    fontWeight: '400',
    color: '#666666',
    lineHeight: 18
  },

  modalWrapper: {
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    overflow: 'hidden'
  },

  homepageModalWrapper: {
    paddingBottom: 90,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    overflow: 'hidden'
  },

  modalView: {
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    height: 480
  },

  titleMain: {
    fontFamily: 'Meituan Type',
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FF1111'
  },

  titleTime: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000'
  },

  breakPoint: {
    width: 2,
    height: 2,
    borderRadius: 50,
    backgroundColor: '#000000',
    marginHorizontal: 3
  },

  title: {
    marginLeft: 20,
    marginTop: 21
  },

  listTitle: {
    marginLeft: 20,
    marginVertical: 24
  },

  listTitleText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#000000'
  },

  titleHeader: {
    flexDirection: 'row',
    alignItems: 'center'
  },

  titleSize: {
    fontSize: 12,
    color: '#999999',
    marginTop: 3
  },
  tips: {
    marginTop: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },

  tipsSize: {
    color: '#999999',
    fontSize: 12
  },

  parting: {
    width: '100%',
    height: 1,
    backgroundColor: '#E5E5E5',
    marginTop: 15
  },

  buttonList: {
    marginHorizontal: 15,
    paddingVertical: 2,
    overflow: 'hidden'
  },

  buttonView: {
    minWidth: 76
  },

  buttonFlex: {
    height: 37,
    paddingHorizontal: 21,
    paddingVertical: 10.5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F7F7F7',
    borderWidth: 0.5,
    borderColor: '#FF4B1000',
    borderRadius: 6,
    marginRight: 6
  },

  buttonSelect: {
    height: 37,
    paddingHorizontal: 21,
    paddingVertical: 10.5,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFF4F0',
    borderWidth: 0.5,
    borderColor: '#FF4B10',
    borderStyle: 'solid',
    borderRadius: 6,
    marginRight: 6
  },

  textNormal: {
    fontSize: 13,
    color: '#4D4D4D'
  },

  textSelect: {
    fontSize: 13,
    fontWeight: '600',
    color: '#FF4B10'
  },

  footButton: {
    position: 'absolute',
    bottom: 33,
    left: 12,
    right: 12
  },

  footButtonBackground: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'red',
    height: 44,
    borderRadius: 48
  },

  footButtonSize: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500'
  },

  closeButton: {
    position: 'absolute',
    top: 22.5,
    right: 15
  }
})
