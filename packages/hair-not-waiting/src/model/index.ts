/* eslint-disable react-hooks/exhaustive-deps */
import { useState, useRef, useCallback } from 'react'
import { Toast } from '@nibfe/gc-ui'
import useLocation from 'src/hooks/useLocation'
import {
  createIntentOrder,
  getBookShopList,
  getFreeShopList,
  getIntentOrder,
  getIntentTabInfo
} from '../apis'
import useSelectTab from 'src/hooks/useSelectTab'
import useUpdateEffect from 'src/hooks/useUpdateEffect'
import { CREATE_STATUS } from 'src/types'
import { isApp } from 'src/utils/platform'
import { lxTrackMGEClickEvent } from '@mrn/mrn-utils'
import { arraysAreIdentical } from 'src/controller'

const useModel = () => {
  const [showModal, setShowModal] = useState<boolean>(false)
  const [tabData, setTabData] = useState({})
  const [orderData, setOrderData] = useState({})
  // 预约商户列表状态
  const [listState, setListState] = useState<any>({
    pageNo: 1,
    listData: [],
    loadingStatus: 'loading',
    loadingMoreStatus: 'done',
    isEmpty: false
  })
  // 空闲商户列表状态
  const [dataSource, setDataSource] = useState<any>({
    listData: []
  })
  const [status, setStatus] = useState<any>(CREATE_STATUS.WAIT)
  const [selectDay, setSelectDay] = useState<string>(null)
  const [flag, setFlag] = useState(null)
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false)

  const location = useLocation()
  const { selectTab } = useSelectTab(tabData)

  const { lng, lat, cityId } = location
  const { projectCode, intentDay, intentTime } = selectTab

  const timeoutId = useRef(null)

  // 获取tab
  const getTabInfo = () => {
    const params = {
      lng,
      lat,
      cityid: cityId
    }
    return getIntentTabInfo(params)
      .then((res: any) => {
        if (res?.code === 200) {
          if (!res?.data?.hasIntent) {
            setShowModal(true)
            setStatus(CREATE_STATUS.WAIT)
          } else {
            setStatus(CREATE_STATUS.PROCESS)
          }
          setTabData(res?.data)
        }
      })
      .catch(() => {})
  }

  // 获取订单
  const getOrderInfo = () => {
    const params = {
      lng,
      lat,
      cityid: cityId
    }
    return getIntentOrder(params)
      .then((res: any) => {
        if (res?.code === 200) {
          if (
            res?.data?.cardTitle !== orderData?.cardTitle ||
            res?.data?.mainTitle !== orderData?.mainTitle
          ) {
            setOrderData(res?.data)
            setSubmitSuccess(false)
            return true
          }
        }
      })
      .catch(() => {})
  }

  // 创建订单
  const createOrder = tab => {
    const params = {
      projectCode: tab?.projectCode || projectCode,
      intentDay: tab?.intentDay || intentDay,
      intentTime: tab?.intentTime || intentTime,
      lng,
      lat,
      cityid: cityId
    }
    return createIntentOrder(params)
      .then((res: any) => {
        if (res?.code === 200) {
          Toast.open('修改成功')
          setShowModal(false)
          setStatus(CREATE_STATUS.PROCESS)
          onChangeDay(tab)
          setSubmitSuccess(true)
          return true
        } else if (res?.msg) {
          Toast.open(res?.msg)
          setSubmitSuccess(false)
          return false
        }
      })
      .catch(() => {})
  }

  const onChangeDay = tab => {
    // @ts-ignore
    const day = tabData?.dayList?.filter(item => item?.tagValue === tab?.intentDay)[0]?.tagName
    // @ts-ignore
    const project = tabData?.projectList?.filter(item => item?.tagValue === tab?.projectCode)[0]
      ?.tagName
    const textTitle = `${day}${tab?.intentTime}${project}有店空闲吗？`
    setSelectDay(textTitle)
  }

  // 预约列表
  const loadData = (tab?: any) => {
    setListState(preState => ({
      ...preState,
      loadingStatus: 'loading'
    }))
    const params = {
      projectCode: tab?.projectCode || projectCode,
      intentTime: tab?.intentTime || intentTime,
      intentDay: tab?.intentDay || intentDay,
      pageNo: listState.pageNo,
      pageSize: 20,
      inApp: isApp ? 1 : 0,
      lng,
      lat,
      cityId
    }
    console.log('tab------', tab, selectTab, params)
    return getBookShopList(params)
      .then((res: any) => {
        if (res?.code === 200) {
          const { shopList, hasNext } = res?.data || {}
          setListState(preState => ({
            listData: shopList,
            loadingStatus: 'done',
            loadingMoreStatus: hasNext ? 'loading' : 'done',
            isEmpty: !(shopList && shopList?.length > 0) && !hasNext,
            pageNo: preState.pageNo + 1
          }))
        }
      })
      .catch(() => {
        setListState(preState => ({
          ...preState,
          loadingStatus: 'fail',
          loadingMoreStatus: 'done',
          isEmpty: true
        }))
      })
  }

  // 预约列表加载更多
  const loadMoreData = () => {
    const params = {
      projectCode,
      intentTime,
      intentDay,
      pageNo: listState.pageNo,
      pageSize: 20,
      inApp: isApp ? 1 : 0,
      lng,
      lat,
      cityId
    }
    return getBookShopList(params)
      .then((res: any) => {
        if (res?.code === 200) {
          const { shopList, hasNext } = res?.data || {}
          setListState(preState => ({
            listData: preState.listData?.concat(shopList),
            loadingStatus: 'done',
            loadingMoreStatus: hasNext ? 'loading' : 'done',
            pageNo: preState.pageNo + 1
          }))
        }
      })
      .catch(() => {
        setListState(preState => ({
          ...preState,
          loadingStatus: 'fail',
          loadingMoreStatus: 'done'
        }))
      })
  }

  // 空闲列表
  const loadDataList = () => {
    const listParams = { lng, lat, cityId, inApp: isApp ? 1 : 0 }
    return getFreeShopList(listParams)
      .then((res: any) => {
        if (res?.code === 200) {
          const { shopList } = res?.data || {}
          const shopListArr = shopList.map(item => item.bookInfo)
          const listDataArr = dataSource?.listData.map(item => item.bookInfo)
          if (
            shopList?.length !== dataSource?.listData?.length ||
            !arraysAreIdentical(shopListArr, listDataArr)
          ) {
            setDataSource({
              listData: shopList
            })
            return true
          }
        }
      })
      .catch(() => {})
  }

  const handleSubmit = (tab: any) => {
    lxTrackMGEClickEvent('gc', 'b_gc_0330yoeo_mc', 'c_gc_kq3wm1df', {
      filter: [selectTab]
    })
    // @ts-ignore
    if (!tabData?.btn?.available) {
      return
    }
    setStatus(CREATE_STATUS.WAIT)
    createOrder(tab)
      .then(res => {
        if (res) {
          loadData(tab)
          pollFetchResult()
        }
      })
      .catch(() => {})
  }

  const pollFetchResult = async () => {
    const res = await Promise.all([getOrderInfo(), loadDataList()])
    if (res.filter(Boolean).length > 0) {
      setFlag(res.filter(Boolean))
      stopPolling()
    }
  }

  /**
   * 轮询
   * @param fn
   * @param interval
   */
  const poll = useCallback(
    (fn, interval) => {
      if (timeoutId.current) {
        stopPolling()
      }
      const executePoll = async () => {
        try {
          await fn()
        } catch (error) {}
        timeoutId.current = setTimeout(executePoll, interval)
      }
      timeoutId.current = setTimeout(executePoll, interval)
    },
    [flag]
  )

  const stopPolling = () => {
    clearTimeout(timeoutId.current)
    timeoutId.current = null
  }

  useUpdateEffect(() => {
    Promise.all([getTabInfo(), getOrderInfo(), loadData(), loadDataList()])
  }, [location])

  useUpdateEffect(() => {
    if (status === CREATE_STATUS.PROCESS && !showModal && !timeoutId.current) {
      poll(pollFetchResult, 4000)
    }
    if (showModal) {
      stopPolling()
    }
    return () => stopPolling()
  }, [status, showModal, poll, timeoutId.current])

  return {
    showModal,
    setShowModal,
    tabData,
    orderData,
    loadMoreData,
    listState,
    loadData,
    loadDataList,
    dataSource,
    handleSubmit,
    getTabInfo,
    status,
    stopPolling,
    selectDay,
    submitSuccess
  }
}
export default useModel
