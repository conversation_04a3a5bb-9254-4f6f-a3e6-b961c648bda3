import { isDP } from '@mrn/mrn-gc-utils'
import { request } from '@mrn/mrn-utils'
import { BookParams, CreateParams, FreeParams, IntentParams } from 'src/types'

const requestConfig = {
  url: '',
  method: 'GET',
  // baseURL: isDP() ? 'https://m.51ping.com' : 'https://test.i.meituan.com',
  baseURL: isDP() ? 'https://m.dianping.com' : 'https://i.meituan.com',
  params: {},
  // // 模拟登陆用
  // headers: {
  //   cookie: 'testUserId=**********;'
  // },
  options: {
    disableRisk: false,
    registerCandyHost: false
  }
}

// 意向单筛选框查询
export function getIntentTabInfo(params: IntentParams) {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/beauty/user/intentselectmodel',
    params
  })
  return request(_requestConfig)
    .then(response => {
      return response.data
    })
    .catch(error => {
      console.error(`getIntentTabInfo:${error}`)
    })
}

// 创建意向单
export function createIntentOrder(params: CreateParams) {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/beauty/user/intentcreateorder',
    method: 'POST',
    data: params
  })
  return request(_requestConfig)
    .then(response => {
      return response.data
    })
    .catch(error => {
      console.error(`getIntentOrder:${error}`)
    })
}

// 查询意向单
export function getIntentOrder(params: IntentParams) {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/beauty/user/intentqueryorder',
    params
  })
  return request(_requestConfig)
    .then(response => {
      return response.data
    })
    .catch(error => {
      console.error(`getIntentOrder:${error}`)
    })
}

// 可预约商家列表
export function getBookShopList(params: BookParams) {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/beauty/user/bookshoplist',
    params
  })
  return request(_requestConfig)
    .then(response => {
      return response.data
    })
    .catch(error => {
      console.error(`getBookShopList:${error}`)
    })
}

// 空闲商家列表
export function getFreeShopList(params: FreeParams) {
  const _requestConfig = Object.assign({}, requestConfig, {
    url: '/beauty/user/freeshoplist',
    params
  })
  return request(_requestConfig)
    .then(response => {
      return response.data
    })
    .catch(error => {
      console.error(`getFreeShopList:${error}`)
    })
}
