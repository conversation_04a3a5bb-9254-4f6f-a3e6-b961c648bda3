// mrn.config.js 配置文档
// http://mrn.sankuai.com/docs/guide/conf.html#mrn-config-js

module.exports = {
  name: 'mrn-manicure-style-list-page',
  main: './index.tsx', // 页面入口地址
  biz: 'gcbu',
  bundleType: 1,
  bundleDependencies: ['@mrn/mrn-base'],
  debugger: {
    moduleName: 'manicure-style-list-page',
    initialProperties: {
      hideNavigationBar: true
    }
  },
  // 转 H5 配置
  one: {
    appConfig: {
      pages: [
        {
          name: 'mrnMnicure',
          path: 'index.tsx',
          enableShareAppMessage: true
        }
      ]
    }
  }
}
