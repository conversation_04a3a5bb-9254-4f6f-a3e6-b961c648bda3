import { GlobalComponentReporter } from '@mrn/mcc-component-report' /*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2023-11-28 11:01:23
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2023-12-06 11:33:47
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/mrn-beauty/packages/card-detail-page/index.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { AppRegistry } from '@mrn/react-native'
import App from './src/App'

// 这里注册的 mrnproject 可以是全集团不冲突的任意名字
GlobalComponentReporter.start({ appKey: 'rn_gcbu_mrn-manicure-style-list-page' })
AppRegistry.registerComponent('manicure-style-list-page', () => App)
