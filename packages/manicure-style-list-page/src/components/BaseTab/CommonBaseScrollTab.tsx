import React from 'react'
import { View, TouchableOpacity } from '@mrn/react-native'
import { IS_ANDROID } from '@mrn/mrn-gc-utils'
import { GCScrollView, GCScrollViewRef } from '@nibfe/gc-ui'
interface State {}
interface Props {
  style?: any
  tabs: any[]
  currentIndex?: number
  bounces?: boolean
  renderTab: ({ item, index, isLast }) => any
  onSelected?: ({ item, index }) => void
  onExposeCallBack?: ({ item, index }) => void
  bottomLinearColorStart?: string
  bottomLinearColorEnd?: string
  gap?: number
  appear?: boolean
  contentContainerStyle?: any
}

export class CommonBaseScrollTab extends React.Component<Props, State> {
  _scrollView: GCScrollViewRef = null
  _containerMeasurements = null
  _tabsMeasurements = []
  _tabContainerMeasurements = null

  constructor(props) {
    super(props)
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (this.props.currentIndex != nextProps.currentIndex && nextProps.currentIndex > 0) {
      this.updateTabPosition(nextProps.currentIndex)
    }
    if (this.props.appear != nextProps.appear) {
      if (nextProps.appear) {
        this.startExpose()
      } else {
        this.pauseExpose()
      }
    }
  }

  startExpose = () => {
    this._scrollView && this._scrollView?.startExpose()
  }

  pauseExpose = () => {
    this._scrollView && this._scrollView?.pauseExpose()
  }

  render() {
    return (
      <View
        style={[{ width: '100%', height: 45 }, this.props.style]}
        onLayout={this.onContainerLayout.bind(this)}
      >
        <GCScrollView
          ref={ref => (this._scrollView = ref)}
          horizontal
          bounces={this.props.bounces || false}
          showsHorizontalScrollIndicator={false}
          style={[{ width: '100%' }]}
          contentContainerStyle={this.props?.contentContainerStyle}
          data={this.props.tabs}
          directionalLockEnabled={true}
          keyExtractor={(item: any, index: number) => `title_${index}`}
          gap={this.props.gap}
          scrollsToTop={false}
          scrollEventThrottle={16}
          onLayoutItem={(_item: any, index: number, event) => {
            this.onTabItemLayout(index, event)
          }}
          onExposeItem={(item, index) => {
            this.props.onExposeCallBack && this.props.onExposeCallBack({ item, index })
          }}
          renderItem={(item: any, index: number) => {
            const isLast = this.props.tabs.length - 1 === index
            return (
              <TouchableOpacity
                key={`title_${index}`}
                activeOpacity={1}
                onPress={() => {
                  this.props.onSelected && this.props.onSelected({ item, index })
                  if (index != this.props.currentIndex) {
                    this.updateTabPosition(index)
                  }
                }}
              >
                {this.props.renderTab({ item, index, isLast })}
              </TouchableOpacity>
            )
          }}
        />
      </View>
    )
  }

  updateTabPosition(index) {
    if (!this._tabsMeasurements[index] || !this._containerMeasurements) return

    const containerWidth = this._containerMeasurements.width
    const tabWidth = this._tabsMeasurements[index].width
    const tabOffset = this._tabsMeasurements[index].left

    let newScrollX = tabOffset
    newScrollX -= (containerWidth - tabWidth) / 2 - 0
    newScrollX = newScrollX >= 0 ? newScrollX : 0
    this._scrollView && this._scrollView?.scrollTo({ x: newScrollX, y: 0, animated: true })
  }

  onSrollToLeft() {
    this._scrollView &&
      this._scrollView?.scrollTo({ x: 0, y: 0, animated: IS_ANDROID ? false : true })
  }
  onTabItemLayout(page, event) {
    const { x, width, height } = event.nativeEvent.layout
    this._tabsMeasurements[page] = { left: x, right: x + width, width, height }
  }

  onContainerLayout(e) {
    this._containerMeasurements = e.nativeEvent.layout
  }
}
