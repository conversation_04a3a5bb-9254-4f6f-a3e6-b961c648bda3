import { PFLDataCenter } from '@mrn/mrn-gc-datacenter'
// import { CommonFilterItemInfoVO } from 'components/CommonTab/Models/CommonFilterItemInfoVO'
import { FilterNode } from '../FilterTree/FilterNode'
import { NailStyleFilterDTO } from 'packages/manicure-style-list-page/src/Models/NailStyleFilterDTO'

export interface TabProps {
  // root?: FilterRoot.FilterRoot<string, string> // 根节点
  // parent?: FilterGroup<number, string> // 当前tab父节点
  pflDataCenter?: PFLDataCenter
  wbKey?: string //通信用的wb
  appear?: boolean // 是否在屏幕上出现
  hover?: boolean // 是否是置顶状态
  autoHover?: boolean // 是否点击自动吸顶
  hoverOffset?: number // 置顶的高度
  extraInfo?: any
  hasOneLevelTabs?: boolean
  isVisible?: boolean
  refreshTab?: () => void
  subTabs?: FilterNode<NailStyleFilterDTO.t, string>[]
  dismissPopFilter?: boolean // 是否要默认关闭弹窗
  subTabSelectFilterItems?: (item: FilterNode<NailStyleFilterDTO.t, string>, index) => void
  scrollToTop?: () => void
  renderActivityTabItem?: (item, index) => any
  renderFastFilterTabItem?: (item, index) => any
  hasExtendPageTheme?: boolean
  isSerial?: boolean // tab和list是否串行，如果是，则需要主动通知到list刷新
  bidView?: { bid: string; param?: {} }
  bidClick?: { bid: string; param?: {} }
  abtest?: string
  disableSecondSelect?: boolean
  hoverChanged?: (boolean) => void
}

export interface TabStyleProps {
  styles?: any
  bgUrl?: string
  tabItemSpace?: number
  bottomLinearColorStart?: string
  bottomLinearColorEnd?: string
  isGradientColor?: boolean
  gradientColor?: string
  gradientRadius?: number
  gradientSectionHoverStyle?: any
  containerHoverStyle?: any
  containerNormalStyle?: any
  isGradientRadius?: boolean
  isTopFilterNeedBgView?: boolean
  secondLevelTabsHeight?: number
  navigationFilterItemType?: number // 一级Tab 样式
  addressDefaultSelected?: boolean // 地址默认选中
  subFilterDefaultImageHeight?: number
}
