// import { FilterParent } from './FilterParent'
import { OnSelectChangeListener } from './OnSelectChangeListener'

export class FilterNode<T, V> {
  //展示名称
  private mDisplayName: string

  private mSubInfo: string

  //节点id，用于数据关联
  private mID: string = ''

  private mMutexCodes: Set<string> = new Set()

  private mData: T

  private mTag: V

  // private mParent: FilterParent<T, V> | null = null

  private mOnSelectChangeListener: OnSelectChangeListener<T, V> | null = null

  public mIsSelected: boolean = false

  public deepIndex: number = -1

  public horizontalIndex: number = -1

  public uniqueSelectedKey: boolean = false // 唯一选中key

  public needRefresh: boolean = true

  private mIsDefaultSelected: boolean = false

  private mDefaultDisplayName: string

  /**
   * 设置展示名称
   *
   * @param displayName 展示名称
   */
  public setDisplayName(displayName: string): void {
    this.mDisplayName = displayName
  }

  /**
   * 获取展示名称
   *
   * @return 展示名称
   */
  public getDisplayName(): string {
    return this.mDisplayName
  }

  public setSubInfo(info: string): void {
    this.mSubInfo = info
  }

  public getSubInfo(): string {
    return this.mSubInfo
  }

  /**
   * 设置节点id，用于节点选中及关联
   * id
   *
   * @param id 节点id
   */
  public setID(id: string): void {
    this.mID = id
  }

  /**
   * 获取节点id
   *
   * @return 节点id
   */
  public getID(): string {
    return this.mID
  }

  /**
   * 设置节点关联的数据
   *
   * @param data 与节点关联的数据
   */
  public setData(data: T): void {
    this.mData = data
  }

  /**
   * 获取节点关联的数据
   *
   * @return 与节点关联的数据
   */
  public getData(): T {
    return this.mData
  }

  /**
   * 设置与节点关联的tag
   *
   * @param tag 与节点关联的tag
   */
  public setTag(tag: V): void {
    this.mTag = tag
  }

  /**
   * 获取与节点关联的tag
   *
   * @return 与节点关联的tag
   */
  public getTag(): V {
    return this.mTag
  }

  /**
   * 设置节点的parent
   *
   * @param parent 节点的父节点
   */
  // public setParent(parent: FilterParent<T, V> | null): void {
  //   this.mParent = parent
  // }

  /**
   * 获取节点的parent
   *
   * @return 节点的父节点
   */
  // public getParent(): FilterParent<T, V> | null {
  //   return this.mParent
  // }

  /**
   * 设置节点选中状态变化监听器
   *
   * @param listener
   */
  public setOnSelectChangeListener(listener: OnSelectChangeListener<T, V>): void {
    this.mOnSelectChangeListener = listener
  }

  /**
   * 是否叶子节点
   *
   * @return 是否叶子节点
   */
  public isLeaf(): boolean {
    return true
  }

  /**
   * 请求root刷新整个筛选树状态
   *
   * @param selected 是否选中
   */
  // public requestSelect(selected: boolean): void {
  //   if (this.isSelected() !== selected && this.mParent !== null) {
  //     this.mParent.requestParentSelect(this, selected)
  //   }
  // }

  /**
   * 刷新该节点及其下层节点状态
   *
   * @param selected 是否选中
   * @return 是否新选中
   */
  public forceSelect(selected: boolean): boolean {
    return this.setSelected(selected)
  }

  /**
   * 刷新该节点状态
   *
   * @param selected 是否选中
   * @return 是否新选中
   */
  public setSelected(selected: boolean): boolean {
    if (this.isSelected() !== selected) {
      this.mIsSelected = selected
      if (this.mOnSelectChangeListener !== null) {
        this.mOnSelectChangeListener.onSelectChange(this, selected)
      }
      return selected
    }
    return false
  }

  /**
   * 是否选中
   *
   * @return 是否选中
   */
  public isSelected(): boolean {
    return this.mIsSelected
  }

  /**
   * 根据触发节点刷新该节点及下层节点状态
   *
   * @param trigger  触发刷新节点
   * @param selected 是否选中
   * @return 是否新选中
   */
  public refreshSelectState(trigger: FilterNode<T, V>, selected: boolean): boolean {
    // 分析这段代码，发现这里的逻辑是：
    // 1. 如果当前节点是触发节点，那么直接刷新当前节点的选中状态
    // 2. 如果当前节点是选中状态，而且触发节点与当前节点互斥，那么取消当前节点的选中状态
    // 3. 如果当前节点是选中状态，而且触发节点与当前节点不互斥，那么不做任何操作
    // 4. 如果当前节点是未选中状态，那么不做任何操作
    // 5. 返回是否新选中
    if (trigger.isEquals(this)) {
      return this.forceSelect(selected)
    } else if (
      selected &&
      (this.isExclusive(trigger) || this.uniqueSelectedKey || trigger.uniqueSelectedKey)
    ) {
      this.forceSelect(false)
    }
    return false
  }

  /**
   * 添加互斥code，用于节点间互斥
   *
   * @param mutexCode 互斥code
   */
  public addMutexCode(mutexCode: string) {
    this.mMutexCodes.add(mutexCode)
  }

  private isExclusive(node: FilterNode<T, V>) {
    if (this.mMutexCodes.size === 0 || node.mMutexCodes.size === 0) {
      return false
    }

    for (let code of node.mMutexCodes) {
      if (this.mMutexCodes.has(code)) {
        return true
      }
    }
    return false
  }

  public isEquals(o: Object): boolean {
    if (o instanceof FilterNode) {
      const right: FilterNode<T, V> = o as FilterNode<T, V>
      if (this.mID == null || this.mID.length == 0 || right.mID == null || right.mID.length == 0) {
        return this == right
      }
      return this.mID == right.mID
    }
    return false
  }

  public contain(node: FilterNode<T, V>, accordingReference: boolean): boolean {
    return (accordingReference && this == node) || (!accordingReference && this.isEquals(node))
  }

  public findNode(node: FilterNode<T, V>, accordingReference: boolean): FilterNode<T, V> | null {
    if ((accordingReference && this == node) || (!accordingReference && this.isEquals(node))) {
      return this
    }
    return null
  }

  /**
   * 设置是否默认选中
   *
   * @param defaultSelected 是否默认选中
   * @returns 是否新选中
   */
  public setDefaultSelected(defaultSelected: boolean): boolean {
    if (this.mIsDefaultSelected !== defaultSelected) {
      this.mIsDefaultSelected = defaultSelected
      return defaultSelected
    }
    return false
  }

  /**
   * 是否是默认选中
   *
   * @returns 是否是默认选中
   */
  public isDefaultSelected(): boolean {
    return this.mIsDefaultSelected
  }

  /**
   * 请求root刷新整个筛选树默认选中状态
   *
   * @param defaultSelected 是否默认选中
   */
  // public requestDefaultSelect(defaultSelected: boolean): void {
  //   if (this.isDefaultSelected() !== defaultSelected && this.mParent !== null) {
  //     this.setDefaultSelected(defaultSelected)
  //     this.mParent.requestParentDefaultSelect(this, defaultSelected)
  //   }
  // }

  public setDefaultDisplayName(defaultDisplayName: string): void {
    this.mDefaultDisplayName = defaultDisplayName
  }

  public getDefaultDisplayName(): string {
    return this.mDefaultDisplayName
  }
}
