/* eslint-disable react/react-in-jsx-scope */
/* eslint-disable react-native/no-inline-styles */
import React from 'react'
import { getWidth } from '@mrn/mrn-gc-utils'
import { View, TouchableOpacity, Image, StyleSheet } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { Carousel } from '@ss/mtd-react-native'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'
interface Props {
  pictures?: string[]
}

export const HeaderInfoModule: React.FC<Props> = props => {
  const pictures = props.pictures || []
  const renderHeadPic = () => {
    // 头图宽高比为0.68
    const renderPic = () => {
      // 上报第一张图片曝光点
      lxTrackMGEViewEvent('gc', 'b_95sp8mkb', 'c_sx1dwrmk', {
        item_count: pictures.length,
        item_id: 0,
        type: 0
      })

      return (
        <Carousel
          autoplay={false}
          loop={false}
          showPagination={false}
          onIndexChanged={index => {
            lxTrackMGEViewEvent('gc', 'b_95sp8mkb', 'c_sx1dwrmk', {
              item_count: pictures.length,
              item_id: index,
              type: 0
            })
          }}
        >
          {pictures.map((item, index) => {
            return (
              <TouchableOpacity
                style={styles.headPic}
                activeOpacity={1}
                key={index}
                onPress={() => {
                  lxTrackMGEClickEvent('gc', 'b_vhittkx2', 'c_sx1dwrmk', {
                    item_count: pictures.length,
                    item_id: index,
                    type: 0
                  })
                }}
              >
                <Image
                  key={index}
                  source={{ uri: item }}
                  style={{ width: '100%', height: '100%' }}
                />
              </TouchableOpacity>
            )
          })}
        </Carousel>
      )
    }
    return (
      <View style={styles.container}>
        <View style={styles.headPic}>{renderPic()}</View>
      </View>
    )
  }

  return <MCModule backgroundColor="transparent">{renderHeadPic()}</MCModule>
}

const styles = StyleSheet.create({
  container: {
    width: getWidth(),
    height: 0.68 * getWidth() - 20
  },
  headPic: {
    width: getWidth(),
    height: 0.68 * getWidth()
  }
})
