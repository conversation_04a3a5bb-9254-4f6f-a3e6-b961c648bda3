/* eslint-disable react-native/no-inline-styles */
import React from 'react'
import { View, Animated, StyleSheet, TouchableOpacity, Image } from '@mrn/react-native'
import { getDeviceStatusBarHeight, isWeb } from '@mrn/mrn-gc-utils'
import { pageRouterClose } from '@mrn/mrn-utils'

const THEME_COLOR = 'white'

const navbackblack = 'https://p0.meituan.net/travelcube/cc66a628a5bfda83c44c31f1af895f1a1550.png'
interface Props {
  scrollY
  titleUrl: string
  shareAction: () => void
}
export const Navigation: React.FC<Props> = props => {
  return (
    <View style={styles.extraView}>
      <TouchableOpacity onPress={() => pageRouterClose()} style={styles.topRow} activeOpacity={1}>
        <Image
          source={{
            uri: navbackblack
          }}
          style={styles.icon}
        />
      </TouchableOpacity>
      <Animated.View
        style={{
          height: getDeviceStatusBarHeight() + 44,
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          backgroundColor: THEME_COLOR,
          opacity: props.scrollY.interpolate({
            inputRange: [0, getDeviceStatusBarHeight() + 44],
            outputRange: [0, 1]
          })
        }}
      >
        <TouchableOpacity
          onPress={() => pageRouterClose()}
          style={styles.bottomRow}
          activeOpacity={1}
        >
          <Image
            source={{
              uri: navbackblack
            }}
            style={styles.icon}
          />
        </TouchableOpacity>
      </Animated.View>
      <Image
        source={{
          uri: props.titleUrl
        }}
        style={styles.titleImg}
      />
      {!isWeb() && (
        <TouchableOpacity
          onPress={() => props.shareAction()}
          style={styles.share}
          activeOpacity={1}
        >
          <Image
            source={{
              uri: 'https://p1.meituan.net/travelcube/d8ca7ed7ba82ba5ad3fdb66231b299ee1109.png'
            }}
            style={styles.icon}
          />
        </TouchableOpacity>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  extraView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 20
  },
  topRow: {
    width: 50,
    height: 30,
    flexDirection: 'row',
    // alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 9,
    marginTop: getDeviceStatusBarHeight() + 10
  },
  bottomRow: {
    width: 50,
    height: 30,
    flexDirection: 'row',
    // alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 9,
    // marginLeft: 11,
    marginTop: getDeviceStatusBarHeight() + 10
  },
  titleImg: {
    height: 119 / 3,
    width: 489 / 3,
    // backgroundColor: '#643511',
    position: 'absolute',
    // bottom: 20,
    marginTop: getDeviceStatusBarHeight() + 0,
    left: 35
  },
  subTitle: {
    color: 'rgba(0, 0, 0, 0.6)',
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 10,
    position: 'absolute',
    bottom: 0,
    left: 43
  },
  share: {
    // width: 30,
    // height: 30,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'absolute',
    right: 10,
    marginTop: getDeviceStatusBarHeight() + 10
  },
  icon: {
    width: 22,
    height: 22
  }
})
