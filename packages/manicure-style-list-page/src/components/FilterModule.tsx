import React, { useCallback, useState, useEffect } from 'react'
import { StyleSheet } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
// import { BaseInfoResponse } from '../types/index'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'
import MixTabPopFilterComponent from './BuComponent/MixTabPopFilterCompoment'
import { FilterNode } from './BaseTab/FilterTree/FilterNode'
import { getDeviceStatusBarHeight, isWeb } from '@mrn/mrn-gc-utils'
import { TabProps } from '@ss/mtd-react-native'
import { TabStyleProps } from './BaseTab/Interface/TabStruct'
import { fetchFilter } from '../TSApis'
import { NailStyleFilterDTO } from '../Models/NailStyleFilterDTO'
import { KNBCityRes, KNBLocationRes, getCityAndLocation } from '../utils/location'
import { getUserInfo } from '../utils/KNBUtils'
import { KNBUserInfoRes } from '../utils/userInfo'
import { isMT } from '../utils/platform'

interface LocalProps {
  notifyPageListRefresh: (ids: string, index: number, name: string) => void
}
type Props = TabProps & TabStyleProps & LocalProps

export const FilterModule: React.FC<Props> = props => {
  const [hover, setHover] = useState(false)
  const [filterData, setFilterData] = useState<{
    data: FilterNode<NailStyleFilterDTO.t, string>[]
  }>({
    data: []
  })
  // let moduleRef = null
  const scrollToTop = () => {
    // moduleRef &&
    //   moduleRef.scrollToModule({
    //     animated: true,
    //     autoOffset: false,
    //     scrollPosition: 'top',
    //     inset: {
    //       top: getDeviceStatusBarHeight() + 44
    //     }
    //   })
  }

  useEffect(() => {
    Promise.all([getCityAndLocation(), getUserInfo()]).then(
      ([loc, user]: [KNBCityRes & KNBLocationRes, KNBUserInfoRes]) => {
        if (loc && user) {
          fetchFilterData(loc, user)
        }
      }
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // 加载筛选项
  const fetchFilterData = useCallback((loc: KNBCityRes & KNBLocationRes, user: KNBUserInfoRes) => {
    const params = {
      // bizcode: 'nib.general.medical_beauty',
      cityId: loc.cityId,
      platform: isMT ? 2 : 1,
      unionId: user.unionId
    }
    fetchFilter(params)
      .then((res: any) => {
        if (res?.code === 200) {
          const data = res?.data || []
          const filter = data.map(element => {
            const temp = new FilterNode<NailStyleFilterDTO.t, string>()
            temp.setData(element)
            // sort为空为全部
            if (!element.sort) {
              temp.setSelected(true)
            }
            return temp
          })
          setFilterData({
            data: filter
          })
          // filterData.data.map({

          // })
        }
      })
      .catch(() => {
        // alert(error)
      })
  }, [])

  return (
    <MCModule
      // ref={ref => (moduleRef = ref)}
      // backgroundColor="transparent"
      style={[styles.container, { backgroundColor: hover ? '#ffffff' : '#f6f6f6' }]}
      hoverOffset={getDeviceStatusBarHeight() + 44}
      hoverType="autoHover"
      onHoverStatusChanged={status => {
        setHover(status.hoverStatus === 'hovering')
      }}
      onExpose={() => {
        lxTrackMGEViewEvent('gc', 'b_gc_cgahfdvm_mv', 'c_gc_42r1tb05', {
          select_index: -1,
          select_name: '默认全部'
        })
      }}
    >
      {filterData.data.length > 0 && (
        <MixTabPopFilterComponent
          key={''}
          subTabs={filterData.data}
          hover={hover}
          // hasOneLevelTabs={this.props?.hasOneLevelTabs || false}
          subTabSelectFilterItems={(item, selectIndex) => {
            // notifyPageListRefresh(this.props.root, this.props.pflDataCenter)
            // this.props.refreshTab && this.props.refreshTab()
            if (item.isSelected()) {
              return
            }
            const name = item.getData()?.name || ''
            lxTrackMGEClickEvent('gc', 'b_gc_cgahfdvm_mc', 'c_gc_42r1tb05', {
              select_index: selectIndex,
              select_name: name
            })
            let nodeTagIds = '0'
            item.setSelected(!item.isSelected())
            let trackIndex = -1
            let trackName = '默认全部'
            if (item.isSelected()) {
              nodeTagIds = item.getData().ids.join(',')
              trackName = item.getData()?.name || ''
              trackIndex = selectIndex
            } else {
              nodeTagIds = '0'
            }
            props.notifyPageListRefresh(nodeTagIds, trackIndex, trackName)
            filterData.data.forEach((e, index) => {
              if (index !== selectIndex) {
                e.setSelected(false)
              }
              return e
            })
            setFilterData(filterData)
          }}
          scrollToTop={scrollToTop}
          autoHover={true}
          {...props}
        />
      )}
    </MCModule>
  )
}

const styles = StyleSheet.create({
  column: {
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'flex-start'
  },
  container: {
    width: '100%',
    paddingHorizontal: 0
  },
  titleContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    height: 22,
    marginBottom: 6
  },
  title: {
    paddingTop: 9,
    paddingHorizontal: 12,
    width: '100%',
    textAlign: 'left',
    color: '#222222',
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 22
  },
  card: {
    width: '100%',
    marginTop: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 12
  },
  contentColumn: {
    flexDirection: 'column',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 12,
    paddingBottom: 12
  },
  contentTitleContainer: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: 'transparent',
    height: 20
  },
  contentTitle: {
    width: '100%',
    textAlign: 'left',
    color: '#222222',
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 20
  },
  contentDetail: {
    width: '100%'
  },
  imageContainer: {
    width: '100%',
    height: 193,
    paddingTop: 9,
    paddingBottom: 12
  },
  imageBorder: {
    width: '100%',
    height: 184,
    borderRadius: 6,
    overflow: 'hidden'
  },
  image: {
    width: '100%',
    height: 184,
    resizeMode: 'cover'
  },
  imageBottomSpacer: {
    height: 9
  }
})
