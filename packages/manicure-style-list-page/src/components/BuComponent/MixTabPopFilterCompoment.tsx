/* eslint-disable react-native/no-inline-styles */
import React from 'react'
import { View, Text, StyleSheet } from '@mrn/react-native'
import { Theme, WithTheme } from '@nibfe/gc-ui/components/common/styles/theme'
import { getDeviceStatusBarHeight, isWeb } from '@mrn/mrn-gc-utils'
import { CommonBaseScrollTab } from '../BaseTab/CommonBaseScrollTab'
import { TabProps, TabStyleProps } from '../BaseTab/Interface/TabStruct'
import { FilterNode } from '../BaseTab/FilterTree/FilterNode'
import { NailStyleFilterDTO } from '../../Models/NailStyleFilterDTO'

interface LocalProps {
  // hovering: boolean
}
type Props = TabProps & TabStyleProps & LocalProps

interface State {
  tabPopSelectMap: {
    [index: number]: any
  }
  hovering: boolean
}

const subTabHover = 44 + getDeviceStatusBarHeight()

class MixTabPopFilter extends React.PureComponent<Props, State> {
  //弹窗ref
  tabPopRefs: {
    [index: number]: any
  }
  pickerHover: number
  secoundLevelTabMayHidden: boolean
  popRef = null
  tabRef = null
  _hoverBottomPos: number = 0
  constructor(props) {
    super(props)
    this.tabPopRefs = {}
    this.pickerHover = subTabHover
    this.state = {
      tabPopSelectMap: {},
      hovering: this.props.hover
    }
  }

  render() {
    return this._renderNormal()
  }

  _renderNormal() {
    const { hover } = this.props
    return (
      <WithTheme compName="Tab" themeStyles={defaultStyles} styles={this.props.styles}>
        {styles => {
          return (
            <CommonBaseScrollTab
              ref={e => {
                this.tabRef = e
              }}
              style={[
                {
                  height: isWeb() ? 30.5 : 30,
                  marginTop: 12,
                  marginBottom: 6,
                  backgroundColor: hover ? 'white' : '#f6f6f6'
                },
                styles.wrapperViewStyle
              ]}
              tabs={this.props.subTabs}
              appear={this.props?.appear}
              contentContainerStyle={styles.contentContainerStyle}
              renderTab={({ item, index }) => this.renderSubTabItem(item, index, styles)}
              onSelected={({ item, index }) => {
                this.props.scrollToTop && this.props.scrollToTop()
                this.props.subTabSelectFilterItems &&
                  this.props.subTabSelectFilterItems(item, index)
              }}
            />
          )
        }}
      </WithTheme>
    )
  }

  renderSubTabItem(item: FilterNode<NailStyleFilterDTO.t, string>, index, styles) {
    if (!item) {
      return null
    }
    return this.renderCommonTab(item, index, styles)
  }

  renderCommonTab(node: FilterNode<NailStyleFilterDTO.t, string>, index, styles) {
    const { hover } = this.props
    let item = node?.getData()
    if (!item) {
      return
    }
    return (
      <View
        // ref={ref => {
        //   this.popRef = ref
        // }}
        style={[
          {
            borderRadius: 6,
            height: 30,
            marginLeft: index === 0 ? 6 : 0,
            marginRight: 6,
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
            borderWidth: 0.5,
            borderColor: '#ffffff',
            boxSizing: 'border-box'
          },
          node.isSelected()
            ? styles.selectedView
            : [
                {
                  backgroundColor: hover ? '#f6f6f6' : 'white',
                  borderColor: hover ? '#f6f6f6' : 'white'
                },
                styles.unselectedView
              ]
        ]}
      >
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            height: 12
          }}
        >
          <Text
            style={[
              {
                fontSize: 12,
                marginHorizontal: 9,
                fontWeight: 'normal'
              },
              node.isSelected() ? styles.selectedText : styles.unselectedText
            ]}
          >
            {item.name}
          </Text>
        </View>
      </View>
    )
  }
}

const MixTabPopFilterComponent = MixTabPopFilter
export default MixTabPopFilterComponent

const defaultStyles = (_theme: Theme) =>
  StyleSheet.create({
    wrapperViewStyle: {},
    contentContainerStyle: {},
    selectedView: {
      backgroundColor: '#FFF1EC',
      borderColor: '#FF4B10'
    },
    unselectedView: {},
    selectedText: {
      fontSize: 12,
      fontWeight: 'normal',
      color: '#FF4B10'
    },
    unselectedText: {
      fontSize: 12,
      fontWeight: 'normal',
      color: '#666666'
    },
    pickerStyle: {}
  })
