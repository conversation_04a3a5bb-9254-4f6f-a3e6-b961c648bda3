import React, { useState, useEffect, useCallback } from 'react'
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ImageBackground,
  Dimensions
} from '@mrn/react-native'
import { MCWaterfallListModule, MCLoadingStatusString } from '@nibfe/doraemon-practice'
import { openUrl } from '@mrn/mrn-utils'
import { IS_IOS, isWeb } from '@mrn/mrn-gc-utils'
import { getCityAndLocation, KNBCityRes, KNBLocationRes } from '../utils/location'
import { getUserInfo, KNBUserInfoRes } from '../utils/userInfo'
import { fetchList } from '../TSApis'
import { NailStyleListContentDTO } from '../Models/NailStyleListContentDTO'
import { isMT } from '../utils/platform'
// import MRNUtils from '@mrn/mrn-utils'

interface Props {
  // dataSource: number[]
  nodeTagIds: string
  filterIndex: number
  filterName: string
}

interface ListState {
  listData: NailStyleListContentDTO.t[]
  loadingStatus: MCLoadingStatusString
  loadingMoreStatus: MCLoadingStatusString
}
const cardWidth = (Dimensions.get('window').width - 36) / 2
export const WaterListModule: React.FC<Props> = props => {
  const [listState, setListState] = useState<ListState>({
    listData: [],
    loadingStatus: 'loading',
    loadingMoreStatus: 'done'
  })
  const listParams = React.useRef({
    lng: null,
    lat: null,
    offset: 0,
    limit: 20,
    platform: isMT ? 2 : 1,
    nodeTagIds: props.nodeTagIds,
    clientType: isWeb() ? 2 : 1
  })
  const setListParams = useCallback(
    newParams => {
      listParams.current = {
        ...listParams.current,
        ...newParams
      }
    },
    [listParams]
  )
  const renderFeedsItem = (item: NailStyleListContentDTO.t, index) => {
    const imageModel = item?.picInfos[0]
    let oriW = imageModel?.width || 1
    const oriH = imageModel?.height || 0
    if (oriW === 0) {
      oriW = 1
    }
    let scrollW = oriH / oriW > 4 / 3 ? 4 / 3 : oriH / oriW
    if (oriW === 0 || oriH === 0) {
      scrollW = 4 / 3
    }
    const imgH = cardWidth * scrollW
    return (
      <TouchableOpacity
        key={`FeedsItem-${index}`}
        style={itemStyles.container}
        activeOpacity={1}
        onPress={() => {
          const jumpUrl = item.detailurl || ''
          !!jumpUrl && openUrl(jumpUrl)
        }}
      >
        <View style={{ flexDirection: 'column' }}>
          {!!imageModel?.url && (
            <ImageBackground
              source={{
                uri: imageModel.url
              }}
              style={[styles.imageBackground, { height: imgH }]}
            />
          )}
          {!!item.message && (
            <Text style={styles.redText} numberOfLines={1}>
              {item.message}
            </Text>
          )}
          {!!item.content && (
            <Text style={styles.titleText} numberOfLines={2}>
              {item.content}
            </Text>
          )}
          <View style={styles.line} />
          <TouchableOpacity
            activeOpacity={0.6}
            style={styles.shopContainer}
            onPress={() => {
              const jumpUrl = item.shop?.detailUrl
              !!jumpUrl && openUrl(jumpUrl)
            }}
          >
            <View style={styles.shopLeftContainer}>
              <Text style={styles.shopText} numberOfLines={1}>
                {item.shop?.shopName || ''}
              </Text>
              {!!item.shop?.star && (
                <View style={styles.starContainer}>
                  <Image
                    source={{
                      uri: 'https://p0.meituan.net/travelcube/ac4cb73e33e10e844206ef4cc67e23db966.png'
                    }}
                    style={styles.starIcon}
                  />
                  <Text style={styles.starText} numberOfLines={1}>
                    {item.shop?.star}
                  </Text>
                </View>
              )}
            </View>
            {!!item.shop?.distance && (
              <View style={styles.distaceContainer}>
                <Text style={styles.distaceText} numberOfLines={1}>
                  {item.shop?.distance}
                </Text>
                <Image
                  source={{
                    uri: 'https://p0.meituan.net/travelcube/208d46be818f3fa87e73955bc2ede35d393.png'
                  }}
                  style={styles.distaceIcon}
                />
              </View>
            )}
          </TouchableOpacity>
          <View style={{ height: 3 }} />
        </View>
      </TouchableOpacity>
    )
  }
  // , [])
  useEffect(() => {
    Promise.all([getCityAndLocation(), getUserInfo()]).then(
      ([loc, user]: [KNBCityRes & KNBLocationRes, KNBUserInfoRes]) => {
        if (loc && user) {
          initListParams(loc)
          fetchListData()
        }
      }
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.nodeTagIds])

  // 初始化列表参数
  const initListParams = useCallback(
    (loc: KNBCityRes & KNBLocationRes) => {
      // 获取经纬度、城市参数
      const { cityId, lat, lng } = loc || {}
      setListParams({
        ...listParams.current,
        cityId: cityId,
        lat,
        lng,
        offset: 0,
        nodeTagIds: props.nodeTagIds,
        cursor: ''
      })
    },
    [props.nodeTagIds, setListParams]
  )
  // 加载列表数据
  const fetchListData = useCallback(() => {
    setListState(preState => ({
      ...preState,
      loadingStatus: 'loading'
    }))
    const params = {
      ...listParams.current
    }
    fetchList(params)
      .then((res: any) => {
        if (res?.code === 200) {
          const { contentList, hasNextPage, nextPageCursor } = res?.data || {}
          setListState(preState => ({
            ...preState,
            listData: [...contentList],
            loadingStatus: 'done',
            loadingMoreStatus: hasNextPage ? 'loading' : 'done'
          }))
          if (hasNextPage) {
            setListParams({
              offset: listParams.current.offset + 1,
              cursor: nextPageCursor || ''
            })
          }
        }
      })
      .catch(() => {
        setListState(preState => ({
          ...preState,
          loadingStatus: 'fail',
          loadingMoreStatus: 'done'
        }))
      })
  }, [setListParams])
  // 加载更多list数据
  const fetchMoreListData = useCallback(() => {
    const params = {
      ...listParams.current
    }
    fetchList(params)
      .then((res: any) => {
        if (res?.code === 200) {
          const { contentList, hasNextPage, nextPageCursor } = res?.data || {}
          setListState(preState => ({
            ...preState,
            listData: [...preState.listData, ...contentList],
            loadingStatus: 'done',
            loadingMoreStatus: hasNextPage ? 'loading' : 'done'
          }))
          if (hasNextPage) {
            setListParams({
              offset: listParams.current.offset + 1,
              cursor: nextPageCursor || ''
            })
          }
        }
      })
      .catch(() => {
        setListState(preState => ({
          ...preState,
          loadingStatus: 'done',
          loadingMoreStatus: 'fail'
        }))
      })
  }, [setListParams])
  return (
    <MCWaterfallListModule
      loadingStatus={listState.loadingStatus}
      loadingMoreStatus={listState.loadingMoreStatus}
      isLoadingMoreCellHideBackground={true}
      isLoadingMoreFailCellHideBackground={true}
      data={listState.listData} // 数据源
      renderItem={renderFeedsItem} // 列表项渲染方法
      // keyExtractor={(item, index) => `${item?.contentCard?.contentId}-${index}`} // 列表项唯一标识
      reuseIdentifierExtractor={'contentFeeds'} // 列表项复用标识
      colCount={2}
      colGap={6}
      rowGap={6}
      footerView={
        listState.loadingMoreStatus === 'done' ? (
          <View style={listFooterStyles.container}>
            <Text style={listFooterStyles.footerText}>- 没有更多了 -</Text>
          </View>
        ) : null
      }
      footerViewStyle={{
        backgroundColor: 'transparent'
      }}
      onNeedLoadMore={() => {
        fetchMoreListData()
      }}
      onRetryForLoadingMoreFail={() => {
        fetchMoreListData()
      }}
      backgroundColor="transparent"
      paddingHorizontal={6}
      paddingVertical={6}
      // onItemExpose={(item, index) => {
      // }}
      viewMgeInfoExtractor={(item, index) => {
        return {
          category: 'gc',
          bid: 'b_gc_0xbmynbo_mv',
          cid: 'c_gc_42r1tb05',
          labs: {
            content_id: item.contentId || '',
            distance: item.shop?.distance || '',
            index: index,
            poi_id: item.shop?.shopId || 0,
            select_index: props.filterIndex,
            select_name: props.filterName,
            title: item.content || '',
            type: item.contentType || 0,
            type_title: item.message || ''
          }
        }
      }}
      clickMgeInfoExtractor={(item, index) => {
        return {
          category: 'gc',
          bid: 'b_gc_0xbmynbo_mc',
          cid: 'c_gc_42r1tb05',
          labs: {
            content_id: item.contentId || '',
            distance: item.shop?.distance || '',
            index: index,
            poi_id: item.shop?.shopId || 0,
            select_index: props.filterIndex,
            select_name: props.filterName,
            title: item.content || '',
            type: item.contentType || 0,
            type_title: item.message || ''
          }
        }
      }}
    />
  )
}
const itemStyles = StyleSheet.create({
  container: {
    borderRadius: 12,
    flexDirection: 'column',
    backgroundColor: '#fff',
    overflow: 'hidden'
  }
})

const styles = StyleSheet.create({
  imageBackground: {
    // width: 110,
    height: 185,
    // borderRadius: 6,
    position: 'relative',
    overflow: 'hidden'
  },
  horizontalLayOut: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  container: {
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 9,
    height: IS_IOS ? 82 : 62,
    backgroundColor: '#ffffff'
  },
  leftContainer: {
    flex: 1,
    height: '100%',
    justifyContent: 'center'
  },
  disAmountContainer: {
    width: '100%',
    justifyContent: 'flex-start'
  },
  disAmountText: {
    fontSize: 12.5,
    color: '#393939',
    fontWeight: '400'
  },
  priceContainer: {
    fontFamily: 'DINAlternate-Bold',
    fontSize: 23,
    color: '#FF3800',
    fontWeight: '700'
  },
  priceIcon: {
    fontFamily: 'PingFangSC-Semibold',
    fontSize: 13,
    color: '#FF3800',
    fontWeight: '600'
  },
  allDisAmountContainer: {
    fontSize: 12.5,
    color: '#7C7C7C',
    fontWeight: '400'
  },
  rightContainer: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 10
  },
  redText: {
    fontFamily: 'PingFang SC',
    fontSize: 11,
    color: '#FF641C',
    fontWeight: '400',
    letterSpacing: 0,
    marginTop: 6,
    height: 27 / 2,
    // backgroundColor: '#123456',
    marginHorizontal: 9
  },
  titleText: {
    fontFamily: 'PingFangSC-Medium',
    fontSize: 13,
    color: '#222222',
    fontWeight: '500',
    marginTop: 6,
    // backgroundColor: '#FF641C',
    marginHorizontal: 9
  },
  itemBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: '100%',
    paddingBottom: 10
  },
  itemButton: {
    flexDirection: 'column',
    alignItems: 'center',
    marginRight: 24
  },
  itemButtonIcon: {
    width: 22,
    height: 22,
    paddingBottom: 3
  },
  buttonContainerBg: {
    width: '100%',
    borderRadius: 22,
    marginVertical: 9
  },
  buttonContainer: {
    width: 150,
    paddingVertical: 6,
    flexDirection: 'column',
    alignItems: 'center'
  },
  buttonTitle: {
    color: '#FFFFFF',
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 14,
    textAlign: 'center'
  },
  buttonDetailContainer: {
    flexDirection: 'row',
    marginTop: -3
  },
  buttonGapLine: {
    backgroundColor: '#FFFFFF',
    height: 6,
    width: 0.5,
    marginHorizontal: 3,
    marginTop: 4.5
  },
  buttonPrice: {
    color: '#FFFFFF',
    fontFamily: 'PingFang SC',
    fontSize: 10,
    textAlign: 'left'
  },
  buttonCupon: {
    color: '#FFFFFF',
    fontFamily: 'PingFang SC',
    fontSize: 10,
    textAlign: 'left'
  },
  userContainer: {
    marginTop: 6,
    width: '100%'
    // height: 29
  },
  userIcon: {
    width: 29,
    height: 29,
    borderRadius: 29 / 2
  },
  shopContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 31,
    marginHorizontal: 9,
    justifyContent: 'space-between'
  },
  shopText: {
    fontFamily: 'PingFang SC',
    fontSize: 13,
    color: '#000000',
    fontWeight: '500',
    // width: cardWidth / 2 - 20
    // backgroundColor: '#123456'
    flex: 1
  },
  shopLeftContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },
  starContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 3
  },
  starIcon: {
    width: 10,
    height: 10
  },
  starText: {
    fontFamily: 'MTfin2.0',
    fontSize: 13,
    color: '#D40000',
    fontWeight: '400',
    letterSpacing: 0,
    marginLeft: 2
  },
  distaceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 9
  },
  distaceIcon: {
    width: 12,
    height: 12
  },
  distaceText: {
    fontFamily: 'PingFang SC',
    fontSize: 13,
    color: 'rgba(0, 0, 0, 0.5)',
    fontWeight: '400',
    letterSpacing: 0
  },
  line: {
    height: 0.5,
    marginTop: 9,
    marginHorizontal: 9,
    backgroundColor: '#E5E5E5'
  }
})

const listFooterStyles = StyleSheet.create({
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: 45,
    backgroundColor: 'transparent'
  },
  footerText: {
    fontWeight: '400',
    color: '#777777',
    fontFamily: 'PingFang SC',
    fontSize: 12,
    letterSpacing: 0,
    textAlign: 'center'
  }
})
