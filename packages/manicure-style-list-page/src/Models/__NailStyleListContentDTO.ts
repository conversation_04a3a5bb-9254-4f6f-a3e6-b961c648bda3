import { NailStyleShopDTO } from './NailStyleShopDTO'
import { NailShowPicDTO } from './NailShowPicDTO'
import { ContentUserDTO } from './ContentUserDTO'
export interface nonnull_t {
  dealIds: number[]
  updateTime: number
  content: string
  shop: NailStyleShopDTO.nonnull_t
  message: string
  detailurl: string
  picInfos: NailShowPicDTO.nonnull_t[]
  user: ContentUserDTO.nonnull_t
  contentType: number
  contentUrl: string
  contentId: number
}
export interface t {
  dealIds?: number[]
  updateTime?: number
  content?: string
  shop?: NailStyleShopDTO.t
  message?: string
  detailurl?: string
  picInfos?: NailShowPicDTO.t[]
  user?: ContentUserDTO.t
  contentType?: number
  contentUrl?: string
  contentId?: number
}
export interface safe_t {
  readonly dealIds: number[]
  readonly updateTime: number
  readonly content: string
  readonly shop: NailStyleShopDTO.safe_t
  readonly message: string
  readonly detailurl: string
  readonly picInfos: NailShowPicDTO.safe_t[]
  readonly user: ContentUserDTO.safe_t
  readonly contentType: number
  readonly contentUrl: string
  readonly contentId: number
}

