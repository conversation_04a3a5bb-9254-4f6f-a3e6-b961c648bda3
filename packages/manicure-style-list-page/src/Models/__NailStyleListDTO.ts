import { NailStyleShareDTO } from './NailStyleShareDTO'
import { NailStyleListContentDTO } from './NailStyleListContentDTO'
export interface nonnull_t {
  limit: number
  offset: number
  hasNextPage: boolean
  nextPageCursor: string
  backGroundUrl: string
  share: NailStyleShareDTO.nonnull_t
  subTitle: string
  title: string
  listTitleUrl: string
  contentList: NailStyleListContentDTO.nonnull_t[]
}
export interface t {
  limit?: number
  offset?: number
  hasNextPage?: boolean
  nextPageCursor?: string
  backGroundUrl?: string
  share?: NailStyleShareDTO.t
  subTitle?: string
  title?: string
  listTitleUrl?: string
  contentList?: NailStyleListContentDTO.t[]
}
export interface safe_t {
  readonly limit: number
  readonly offset: number
  readonly hasNextPage: boolean
  readonly nextPageCursor: string
  readonly backGroundUrl: string
  readonly share: NailStyleShareDTO.safe_t
  readonly subTitle: string
  readonly title: string
  readonly listTitleUrl: string
  readonly contentList: NailStyleListContentDTO.safe_t[]
}

