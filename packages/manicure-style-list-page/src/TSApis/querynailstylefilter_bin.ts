/**
 * @apiName V1_dzcontentfilter_bin
 * @apiType GET
 * @apiUrl https://mapi.dianping.com/api/beautyapigw/v1/dzcontentfilter.bin
 * @bridgeType mrn
 */
import { mapi } from '@mrn/mrn-utils'
import { NailStyleFilterDTO } from '../Models/NailStyleFilterDTO'

interface RequestParams {
  listrecallparamjson?: string // json格式的字符串，用于列表召回，前端回传
  bizcode?: string // 业务代码
}

interface MapiOptions {}

// 获取筛选项
export function querynailstylefilter_bin(
  params: RequestParams,
  mapiOptions?: MapiOptions
): Promise<NailStyleFilterDTO.t> {
  return mapi({
    url: 'https://mapi.dianping.com/api/beautyapigw/querynailstylefilter.bin',
    method: 'GET',
    params,
    ...mapiOptions
  }).catch((e: any) => {
    e.userInfo = {
      ...e.userInfo,
      requestInfo: {
        url: 'https://mapi.dianping.com/api/beautyapigw/querynailstylefilter.bin'
      }
    }
    throw e
  })
}
