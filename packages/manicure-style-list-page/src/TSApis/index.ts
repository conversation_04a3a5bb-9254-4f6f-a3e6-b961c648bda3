import { querynailstylelist_bin } from './querynailstylelist_bin'
import { NailStyleListDTO } from '../Models/NailStyleListDTO'
import { querynailstylefilter_bin } from './querynailstylefilter_bin'
import { request } from '@mrn/mrn-utils'
import { NailStyleFilterDTO } from '../Models/NailStyleFilterDTO'
import { isWeb } from '@mrn/mrn-gc-utils'
import { isMT } from '../utils/platform'

export async function fetchFilter(params, mapiOptions?) {
  if (isWeb()) {
    const a = await request({
      params: params,
      url: '/gw/medical/content/query_content_style_filter',
      // baseURL: 'https://m.51ping.com',
      baseURL: isMT ? 'https://g.meituan.com' : 'https://m.dianping.com',
      method: 'GET'
    }).catch(e => {
      return {
        code: e.code > 0 ? e.code : -1,
        msg: e.message || '请求失败'
      }
    })
    return a?.data as NailStyleFilterDTO.t
  } else {
    return querynailstylefilter_bin(params, mapiOptions)
  }
}

export async function fetchList(params, mapiOptions?) {
  if (isWeb()) {
    const a = await request({
      params: params,
      url: '/gw/medical/content/query_content_style_list',
      // baseURL: 'https://m.51ping.com',
      baseURL: isMT ? 'https://g.meituan.com' : 'https://m.dianping.com',
      method: 'GET'
    }).catch(e => {
      return {
        code: e.code > 0 ? e.code : -1,
        msg: e.message || '请求失败'
      }
    })
    return a?.data as NailStyleListDTO.t
  } else {
    return querynailstylelist_bin(params, mapiOptions)
  }
}
