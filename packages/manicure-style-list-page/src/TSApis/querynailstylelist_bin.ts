/**
 * @apiName barproductlist.joy
 * @apiType GET
 * @apiUrl https://mapi.dianping.com/mapi/joy/category/web/barproductlist.joy
 * @bridgeType mrn
 */
import { mapi } from '@mrn/mrn-utils'
import { NailStyleListDTO } from '../Models/NailStyleListDTO'

interface RequestParams {
  platform?: string // 平台参数，mt-美团，dp=点评，native不用穿该参数
  type: number // 1-商家招牌；2-网友推荐
  pagesize?: number // 每页最大记录条数
  pageindex: number // 页码，从1开始
  shopid: number // 门店ID
}

interface MapiOptions {}

export function querynailstylelist_bin(
  params: RequestParams,
  mapiOptions?: MapiOptions
): Promise<NailStyleListDTO.t> {
  return mapi({
    url: 'https://mapi.dianping.com/api/beautyapigw/querynailstylelist.bin',
    method: 'GET',
    params,
    ...mapiOptions
  }).catch((e: any) => {
    e.userInfo = {
      ...e.userInfo,
      requestInfo: {
        url: 'https://mapi.dianping.com/api/beautyapigw/querynailstylelist.bin'
      }
    }
    throw e
  })
}
