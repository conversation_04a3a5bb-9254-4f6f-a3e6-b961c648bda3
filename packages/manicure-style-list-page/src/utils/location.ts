import { isWeb } from '@mrn/mrn-gc-utils'
import KNB from '@mrn/mrn-knb'
import { isMT } from './platform'

export interface KNBCityRes {
  cityId: string
  locCityId: string
  type: 'mt' | 'dp'
  cityName: string
  locCityName: string
  areaId: string
  areaName: string
  noCache: boolean
}
export interface KNBLocationRes {
  lat: number
  lng: number
}

export interface KNBLocationRes {
  lat: number
  lng: number
}

export function getCity() {
  return new Promise(res => {
    KNB.getCity({
      success: (data: KNBCityRes) => {
        res(data)
      }
    })
  })
}

export function getLocation() {
  const type = isWeb() ? 'wgs84' : isMT ? 'GCJ02' : 'WGS84'
  return new Promise((res, rej) => {
    KNB.getLocation({
      sceneToken: 'dd-473af310dbd99039', // 因隐私合规中长期方案影响，增加sceneToken，根据mode不同需要不同权限，mode为instant和accurate为需要Locate.continuous，其他需要Locate.once
      type: type, // 可选值wgs84(标准坐标系)， gcj02（国测局 火星坐标）,1.2.0版本支持，建议微信小写，美团写大写
      timeout: 5000, //定位超时时间，1.2.0版本默认为5000，1.1.0版本默认为6000
      success: function (location: KNBLocationRes) {
        res(location)
      },
      fail: function (ret) {
        rej(ret)
      }
    })
  })
}

export function getCityAndLocation() {
  return Promise.all([getCity(), getLocation()])
    .then(([city, location]: [KNBCityRes, KNBLocationRes]) => {
      return {
        ...city,
        ...location
      }
    })
    .catch(() => {
      return {}
    })
}
