import KNB from '@mrn/mrn-knb'

export interface KNBUserInfoRes {
  unionId: string
  type: string
  userId: string
  uuid: string
  token: string
  isNewUser: boolean
  unionIdV2: string
  userName: string
  avatarURL: string
  phoneNumber: string
}

export function getUserInfo() {
  return new Promise((res, rej) => {
    KNB.getUserInfo({
      success: function (user: KNBUserInfoRes) {
        res(user)
      },
      fail: function (ret) {
        rej(ret)
      }
    })
  })
}
