import KNB from '@mrn/mrn-knb'
import { GetUserInfoResult, GetCityResult, GetLocationResult } from '@mtfe/knb-next'

// needVarify表示会验证是否已经登陆了，当已经登陆的时候，无需再登陆
function login(needVarify: boolean = true) {
  return new Promise(resolve => {
    const pureLogin = () => {
      KNB.login({
        success: loginUser => {
          resolve(loginUser || {})
        },
        fail: () => {
          resolve({})
        }
      })
    }
    if (needVarify) {
      getUserInfo()
        .then((user: any) => {
          var userId = user && user.userId //String 类型，用户ID, -1时表示未登录
          if (userId && userId.length > 0 && userId !== '-1') {
            // 登陆状态
            resolve(user)
          } else {
            // 非登陆
            pureLogin()
          }
        })
        .catch(pureLogin)
    } else {
      pureLogin()
    }
  })
}

// 获取fingerprint参数
function getFingerprint() {
  return new Promise((resolve, _reject) => {
    KNB.getFingerprint({
      success: ret => {
        const fingerprint = ret ? ret.fingerprint : ''
        resolve(fingerprint)
      },
      fail: () => {
        resolve('')
      }
    })
  })
}

// 获取用户信息
export function getUserInfo(): Promise<GetUserInfoResult> {
  return new Promise(resolve => {
    KNB.getUserInfo({
      success(user) {
        resolve(user || {})
      },
      fail: () => {
        resolve({})
      }
    })
  })
}

interface LoctionOptions {
  type?: '' | 'WGS84' | 'GCJ02' | 'wgs84' | 'gcj02'
  timeout?: number
  cache?: boolean
}

export default {
  login,
  getFingerprint,
  getUserInfo
}

export { GetUserInfoResult, GetCityResult, GetLocationResult }
