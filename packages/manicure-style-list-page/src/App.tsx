/*
 * @Author: wuhao92 <EMAIL>
 * @Date: 2023-11-28 11:01:23
 * @LastEditors: wuhao92 <EMAIL>
 * @LastEditTime: 2024-01-04 17:37:53
 * @FilePath: /mrn-component-joy-beauty/Users/<USER>/代码/Code代码/mrn-beauty/packages/card-detail-page/src/App.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useState, useRef, useCallback, useEffect } from 'react'
import { AnimatedMCPage } from '@nibfe/doraemon-practice'
import { Animated } from '@mrn/react-native'
import TopViewProvider from '@max/leez-top-view-provider'
import { HeaderInfoModule } from './components/HeaderInfoModule'
import { WaterListModule } from './components/WaterListModule'
import { Navigation } from './components/Navigation'
import { FilterModule } from './components/FilterModule'
import { KNBCityRes, KNBLocationRes, getCityAndLocation } from './utils/location'
import { KNBUserInfoRes, getUserInfo } from './utils/userInfo'
import { fetchList } from './TSApis'
import { isWeb } from '@mrn/mrn-gc-utils'
import { NailStyleListDTO } from './Models/NailStyleListDTO'
import KNB from '@mrn/mrn-knb'
import TextCompatUtils from './utils/TextCompatUtils'
import { isMT } from './utils/platform'
// 模块入口处执行一次全局配置兼容方法
TextCompatUtils.fixTextCompat() // 修复部分Android机型文本截断问题、iOS默认字体
interface Props {
  uuid: string
  lng: string
  limit: string
  offset: number
}

const App: React.FC<Props> = () => {
  const scrollY = useRef(new Animated.Value(0)).current

  const [data, setData] = useState<NailStyleListDTO.t>(null)
  const [filterParams, setFilterParams] = useState({
    nodeTagIds: '0',
    filterIndex: -1,
    filetrName: '默认全部'
  })

  const listParams = React.useRef({
    // bizcode: 'nib.general.medical_beauty',
    lng: null,
    lat: null,
    offset: 0,
    limit: 1,
    platform: isMT ? 2 : 1,
    nodeTagIds: '0',
    clientType: isWeb() ? 2 : 1
  })
  const setListParams = useCallback(
    newParams => {
      listParams.current = {
        ...listParams.current,
        ...newParams
      }
    },
    [listParams]
  )

  useEffect(() => {
    Promise.all([getCityAndLocation(), getUserInfo()]).then(
      ([loc, user]: [KNBCityRes & KNBLocationRes, KNBUserInfoRes]) => {
        if (loc && user) {
          initListParams(loc)
          fetchListData()
        }
      }
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  // 初始化列表参数
  const initListParams = useCallback(
    (loc: KNBCityRes & KNBLocationRes) => {
      // const params = {} as any
      // 获取经纬度、城市参数
      const { cityId, lat, lng } = loc || {}
      setListParams({
        ...listParams.current,
        cityId: cityId,
        lat,
        lng
      })
    },
    [setListParams]
  )
  // 加载列表数据
  const fetchListData = useCallback(() => {
    const params = {
      ...listParams.current
    }
    fetchList(params)
      .then((res: any) => {
        if (res?.code === 200) {
          setData(res.data)
        }
      })
      .catch(() => {})
  }, [])

  return (
    <TopViewProvider>
      <Navigation
        scrollY={scrollY}
        titleUrl={data?.listTitleUrl || ''}
        shareAction={() => {
          const share = data?.share
          share &&
            KNB.share({
              title: share?.title || '',
              url: share?.url || '',
              desc: share?.desc || '',
              image: share?.image || '',
              channel: []
            })
        }}
      />
      <AnimatedMCPage
        mptInfo={{
          category: 'gc',
          cid: 'c_gc_42r1tb05',
          labs: {
            cat_id: '39'
          }
        }}
        enableBounce={false}
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
          useNativeDriver: true
        })}
        scrollEventThrottle={1}
        paddingHorizontal={0}
        contentBackgroundColor="#F4F4F4"
        // eslint-disable-next-line react-native/no-inline-styles
        separatorLineStyle={{
          display: 'hidden-all'
        }}
        showScrollIndicator={false}
        pageTopGap={0}
        pageGap={0}
        key={'mcPage'}
        modules={[
          {
            moduleKey: 'HeaderInfoModule',
            // data?.backGroundUrl
            module: <HeaderInfoModule pictures={[data?.backGroundUrl || '']} />
          },
          {
            moduleKey: 'FilterModule',
            module: (
              <FilterModule
                value={''}
                options={[]}
                notifyPageListRefresh={(ids, index, name) => {
                  setFilterParams({
                    nodeTagIds: ids,
                    filterIndex: index,
                    filetrName: name
                  })
                }}
              />
            )
          },
          {
            moduleKey: 'WaterListModule',
            module: (
              <WaterListModule
                nodeTagIds={filterParams.nodeTagIds}
                filterIndex={filterParams.filterIndex}
                filterName={filterParams.filetrName}
              />
            )
          }
        ]}
      />
    </TopViewProvider>
  )
}

export default App
