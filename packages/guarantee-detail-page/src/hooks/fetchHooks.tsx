import { useEffect, useCallback } from 'react'
import { useImmer } from 'use-immer'
import { GuaranteeDetailResponse, GuaranteeDetailRequest } from '../types'
import { fetchGuaranteeDetailService } from '../api'
import MRNKNB from '@mrn/mrn-knb'
import { pageRouterClose } from '@mrn/mrn-utils'
import { getLocation, getCity, KNBLocationRes, KNBCityRes } from '../utils/location'
import { getUserInfo, KNBUserInfoRes } from '../utils/userInfo'

export const useFetchHooks = params => {
  const [guaranteeDetail, setGuaranteeDetail] = useImmer<GuaranteeDetailResponse>({
    shopName: '',
    brandInfoList: [],
    techModuleResult: {
      categoryList: [
        {
          categoryListUrl: '',
          categoryName: '',
          technicians: [
            {
              avatarUrl: '',
              certifiedNo: '',
              detailUrl: '',
              seniority: '',
              title: ''
            }
          ]
        }
      ],
      count: 0,
      listUrl: ''
    }
  })

  const getGuaranteeDetail = useCallback(
    (_params: GuaranteeDetailRequest) => {
      fetchGuaranteeDetailService(_params).then(res => {
        setGuaranteeDetail(res)
      })
    },
    [setGuaranteeDetail]
  )

  let { guaranteeServiceParams } = params

  const init = useCallback(async () => {
    try {
      const [location, userInfo, city] = await Promise.all([
        getLocation(),
        getUserInfo(),
        getCity()
      ])
      const { lat, lng } = (location as KNBLocationRes) || {}
      const { unionId } = (userInfo as KNBUserInfoRes) || {}
      const { cityId } = (city as KNBCityRes) || {}

      getGuaranteeDetail({
        ...guaranteeServiceParams,
        lat,
        lng,
        cityId,
        unionId
      })
    } catch (e) {
      console.log(e)
    }
  }, [getGuaranteeDetail])

  useEffect(() => {
    MRNKNB.getUserInfo({
      success: (user: any) => {
        if (user.userId === '-1') {
          MRNKNB.login({
            success: () => {
              init()
            },
            fail: e => {
              console.log(e)
              setTimeout(() => {
                pageRouterClose()
              }, 500)
            }
          })
        }
      }
    })
    init()
  }, [init])

  return {
    guaranteeDetail,
    getGuaranteeDetail
  }
}
