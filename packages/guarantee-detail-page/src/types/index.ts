export interface GuaranteeDetailRequest {
  shopId?: string
  bizId: number
  clientType?: number
  moduleKey: string
  bizParam: string
}

export interface GuaranteeDetailResponse {
  shopName: string
  brandInfoList?: brandInfoDTO[]
  techModuleResult?: techListModuleBO
}

export interface brandInfoDTO {
  brandName: string
  brandUrl: string
  brandId: number
}

export interface techListModuleBO {
  categoryList: categoryListDTO[]
  count: number
  listUrl: string
}

export interface categoryListDTO {
  categoryListUrl: string
  categoryName: string
  technicians: technician[]
}
export interface technician {
  avatarUrl: string
  certifiedNo: string
  detailUrl: string
  seniority: string
  title: string
}

export interface attrValueDTO {
  name: string
  photoUrl: string
  clinicalTitle: string
  certifiedNo: string
  workYears: number
}
