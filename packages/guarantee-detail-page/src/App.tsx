import React, { useRef } from 'react'
import { AnimatedMCPage } from '@nibfe/doraemon-practice'
import TopViewProvider from '@max/leez-top-view-provider'
import { Navigation } from './components/Navigation'
import { useFetchHooks } from './hooks/fetchHooks'
import { isDP } from '@mrn/mrn-gc-utils'
import { PageModule } from './components/pageModule'
import { Background } from './components/Background'
import { Provider } from '@nibfe/gc-ui'

interface Props {
  shopId: string
  moduleKey: string
  bizParam?: string
}

const App: React.FC<Props> = props => {
  const { shopId, moduleKey, bizParam } = props
  const pageRef = useRef(null)
  const { guaranteeDetail } = useFetchHooks({
    guaranteeServiceParams: {
      shopId,
      moduleKey,
      bizParam,
      bizId: 99,
      clientType: isDP() ? 1 : 2
    }
  })
  const cid = moduleKey === 'shop_glasses_assured' ? 'c_gc_i7hzm7c6' : 'c_gc_u3lfzkpf'

  return (
    <Provider>
      <TopViewProvider>
        <Navigation />
        <AnimatedMCPage
          mptInfo={{
            category: 'gc',
            cid: cid,
            labs: {
              cat_id: 128
            }
          }}
          ref={pageRef}
          enableBounce={false}
          scrollEventThrottle={1}
          paddingHorizontal={0}
          pageBottomGap={30}
          contentBackgroundColor={moduleKey === 'shop_glasses_assured' ? '#030617' : '#D8F0FF'}
          contentBackgroundView={<Background moduleKey={moduleKey} />}
          // eslint-disable-next-line react-native/no-inline-styles
          separatorLineStyle={{
            display: 'hidden-all'
          }}
          pageTopGap={0}
          pageGap={0}
          modules={[
            {
              moduleKey: 'PageModule',
              module: (
                <PageModule
                  moduleKey={moduleKey}
                  guaranteeDetail={guaranteeDetail}
                  pageRef={pageRef}
                />
              )
            }
          ]}
        />
      </TopViewProvider>
    </Provider>
  )
}

export default App
