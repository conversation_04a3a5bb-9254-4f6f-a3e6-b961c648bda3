import React from 'react'
import { View } from '@mrn/react-native'

import { MCModule } from '@nibfe/doraemon-practice'
import { CraftsmanList } from './CraftsmanList'
import { AfterSalesService } from './AfterSalesService'
import { BrandLicenceList } from './BrandLicenceList'
import { VerifyService } from './VerifyService'
import { Footer } from './Footer'
export const PageModule = ({ moduleKey, guaranteeDetail, pageRef }) => {
  return (
    <MCModule backgroundColor="transparent" paddingHorizontal={12}>
      {moduleKey === 'shop_glasses_assured' ? (
        <View>
          <CraftsmanList
            technicians={
              guaranteeDetail &&
              guaranteeDetail.techModuleResult &&
              guaranteeDetail.techModuleResult.categoryList &&
              guaranteeDetail.techModuleResult.categoryList[0] &&
              guaranteeDetail.techModuleResult.categoryList[0].technicians
            }
            total={
              guaranteeDetail &&
              guaranteeDetail.techModuleResult &&
              guaranteeDetail.techModuleResult.count
            }
            jumpLink={
              guaranteeDetail &&
              guaranteeDetail.techModuleResult &&
              guaranteeDetail.techModuleResult.listUrl
            }
          />
          <AfterSalesService />
        </View>
      ) : (
        <View>
          <BrandLicenceList
            brandInfoList={guaranteeDetail && guaranteeDetail.brandInfoList}
            pageRef={pageRef}
          />
          <VerifyService />
        </View>
      )}
      <Footer moduleKey={moduleKey} shopName={guaranteeDetail && guaranteeDetail.shopName} />
    </MCModule>
  )
}
