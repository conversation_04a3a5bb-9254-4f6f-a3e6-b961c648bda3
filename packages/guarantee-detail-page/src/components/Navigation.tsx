import React from 'react'
import { View, StyleSheet, TouchableOpacity, Image, StatusBar } from '@mrn/react-native'
import { getWidth } from '@mrn/mrn-gc-utils'
import { pageRouterClose } from '@mrn/mrn-utils'
import { getStatusBarHeight } from '@nibfe/dm-navigation'
export const Navigation = () => {
  return (
    <View style={styles.extraView}>
      <StatusBar backgroundColor="transparent" barStyle="dark-content" translucent />
      <TouchableOpacity onPress={() => pageRouterClose()} style={styles.topRow} activeOpacity={1}>
        <Image
          source={{ uri: 'https://p0.meituan.net/ingee/3209e72d43ab3747437cf0a46e45053b2515.png' }}
          style={{
            width: 32,
            height: 32
          }}
        />
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  extraView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 20
  },
  topRow: {
    width: getWidth(),
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 7,
    marginTop: getStatusBarHeight()
  }
})
