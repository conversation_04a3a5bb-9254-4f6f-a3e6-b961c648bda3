import React from 'react'
import { View, Image, StyleSheet } from '@mrn/react-native'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
export const MainTitle = ({ title, width }) => {
  return (
    <View style={styles.mainTitle}>
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        colors={['#ffffff', '#1360be80']}
        style={styles.gradientBtn}
      />

      <Image
        source={{
          uri: title
        }}
        style={[styles.meituanIcon, { width: width }]}
      />
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        colors={['#1360be80', '#ffffff']}
        style={styles.gradientBtn}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  mainTitle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 20.5
  },
  meituanIcon: {
    height: 20.5,
    width: 180,
    resizeMode: 'contain',
    marginHorizontal: 3
  },
  gradientBtn: {
    width: 35,
    height: 2,
    borderRadius: 1
  },
  block: {
    width: 4,
    height: 4,
    backgroundColor: '#003984',
    marginHorizontal: 3
  }
})
