import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableWithoutFeedback,
  Dimensions
} from '@mrn/react-native'
import { MainTitle } from './MainTitle'
import { lxTrackMGEViewEvent, lxTrackMGEClickEvent } from '@mrn/mrn-utils'
import KNB from '@mrn/mrn-knb'
const DimensionWidth = Dimensions.get('window').width
export const BrandLicenceList = ({ brandInfoList, pageRef }) => {
  const [isFold, setIsFold] = useState(true)
  const [list, setList] = useState([])
  const [collapseTracked, setCollapseTracked] = useState(false)
  const urls = []
  brandInfoList &&
    brandInfoList.forEach(item => {
      urls.push(item.brandUrl)
    })

  useEffect(() => {
    if (!isFold) {
      setList(brandInfoList)
    } else {
      brandInfoList && setList(brandInfoList.slice(0, 3))
    }
  }, [brandInfoList, isFold, pageRef])

  useEffect(() => {
    if (!collapseTracked && brandInfoList && brandInfoList.length > 3) {
      setCollapseTracked(true)
      lxTrackMGEViewEvent('gc', 'b_gc_9eepp3vd_mv', 'c_gc_u3lfzkpf', {})
    }
  }, [brandInfoList, collapseTracked])

  const preiewImage = currentUrl => {
    return new Promise((resolve, reject) => {
      KNB.previewImage({
        current: currentUrl,
        urls: urls,
        success: () => {
          resolve('success')
        },
        fail: e => {
          reject(e)
        }
      })
    })
  }
  const renderLicenceList = () => {
    return (
      <View
        style={[
          styles.listContainer,
          { justifyContent: brandInfoList && brandInfoList.length > 3 ? 'flex-start' : 'center' }
        ]}
      >
        {list.map((item, index) => {
          return (
            <TouchableWithoutFeedback
              onPress={() => {
                lxTrackMGEClickEvent('gc', 'b_gc_nik8qdb6_mc', 'c_gc_u3lfzkpf', {})

                preiewImage(item.brandUrl)
              }}
            >
              <View
                onLayout={() => {
                  lxTrackMGEViewEvent('gc', 'b_gc_nik8qdb6_mv', 'c_gc_u3lfzkpf', {})
                }}
                style={{
                  marginLeft: index % 3 === 0 ? 0 : (DimensionWidth - 50 - 90 * 3) / 2,
                  marginBottom: 9
                }}
              >
                <View style={styles.cardContainer}>
                  <View style={[styles.itemImg, { overflow: 'hidden' }]}>
                    <Image
                      style={{ width: 90, height: 120, resizeMode: 'cover', borderRadius: 6 }}
                      source={{
                        uri: item.brandUrl
                      }}
                    />
                  </View>
                  <Text style={styles.brandName}>{item.brandName}</Text>
                </View>
              </View>
            </TouchableWithoutFeedback>
          )
        })}
      </View>
    )
  }

  const renderCollapse = () => {
    return (
      <TouchableWithoutFeedback
        onPress={() => {
          lxTrackMGEClickEvent('gc', 'b_gc_9eepp3vd_mc', 'c_gc_u3lfzkpf', {})
          setIsFold(!isFold)
          if (!isFold) {
            pageRef.current?.scrollToPosition({ position: 0, animated: true })
          }
        }}
      >
        <View style={styles.checkMore}>
          <Text style={styles.moreText}>{isFold ? '查看全部' : '收起'}</Text>
          <Image
            style={{
              width: 12,
              height: 12,
              resizeMode: 'contain',
              transform: isFold ? [] : [{ rotate: '180deg' }]
            }}
            source={{
              uri: 'https://p0.meituan.net/travelcube/13fbd65ddbb00bf45706912ab74f3f17359.png'
            }}
          />
        </View>
      </TouchableWithoutFeedback>
    )
  }
  return (
    <View style={styles.container}>
      <MainTitle
        width={72}
        title={'https://p0.meituan.net/travelcube/ef3ec5ead13dfc4e970bab4cdbab256e3189.png'}
      />
      <Text style={styles.grayText}>
        美团平台审核门店的授权书，并与上游品牌合作双重校验授权书真实性，保障门店所售镜片都是正品。
      </Text>
      {brandInfoList ? renderLicenceList() : null}
      {brandInfoList && brandInfoList.length > 3 ? renderCollapse() : null}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: 'white',
    position: 'relative',
    borderRadius: 8,
    padding: 12,
    paddingBottom: 6,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#ffffff',
    marginTop: (187.5 * DimensionWidth) / 375
  },
  gradient: {
    position: 'absolute',
    width: DimensionWidth - 26,
    height: 6,
    top: 0,
    left: 0
  },
  bottomGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: DimensionWidth - 26,
    height: 6
  },
  grayText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    fontWeight: '400',
    color: '#666666',
    marginTop: 10,
    marginBottom: 6
  },
  listContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    flexWrap: 'wrap',
    width: '100%'
  },
  itemImg: {
    width: 90,
    height: 120,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#f0f0f0'
  },
  cardContainer: {
    alignItems: 'center'
  },
  checkMore: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 9,
    marginBottom: 12
  },
  moreText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    fontWeight: '400',
    color: '#222222'
  },
  brandName: {
    fontFamily: 'PingFang SC',
    fontSize: 10,
    fontWeight: '400',
    color: '#000000',
    marginTop: 3
  }
})
