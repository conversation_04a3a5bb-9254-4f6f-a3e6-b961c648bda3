import React from 'react'
import { View, Text, StyleSheet } from '@mrn/react-native'
import { MainTitle } from './MainTitle'
export const AfterSalesService = () => {
  return (
    <View style={styles.container}>
      <MainTitle
        width={108}
        title={'https://p0.meituan.net/travelcube/4e189886716907f1f856900c7e7b94844018.png'}
      />

      <View>
        <View>
          <View style={[styles.certifiedRow, { marginTop: 10 }]}>
            <Text style={styles.title}>30天免费换</Text>
          </View>
          <Text style={styles.grayText}>
            取镜日30天内，若验配用户佩戴7天后，仍对度数感到不适，门店复查后，需支持免费更换镜片一次（自带验光数据不享受该权益）
          </Text>
        </View>
        <View style={{ marginTop: 6 }}>
          <View style={styles.certifiedRow}>
            <Text style={styles.title}>180天镜框免费换</Text>
          </View>
          <Text style={styles.grayText}>
            取镜日180天内，若所购镜框有质量问题（非人为损坏），商户需支持用户免费更换镜框一次
          </Text>
        </View>
        <View style={{ marginTop: 6 }}>
          <View style={styles.certifiedRow}>
            <Text style={styles.title}>终身免费清洗维修</Text>
          </View>
          <Text style={styles.grayText}>配镜后，商户需免费提供后续清洗、镜片镜框维修服务</Text>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginTop: 12
  },
  title: {
    fontFamily: 'PingFang SC',
    fontSize: 14,
    fontWeight: '500'
  },
  icon: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
    marginRight: 3
  },
  certifiedRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12
  },
  grayText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    fontWeight: '400',
    color: '#666666',
    marginVertical: 6
  }
})
