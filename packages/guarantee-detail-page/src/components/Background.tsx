import React from 'react'
import { View, ImageBackground, StyleSheet, Dimensions } from '@mrn/react-native'
const DimensionWidth = Dimensions.get('window').width
export const Background = ({ moduleKey }) => {
  const backImgUrl =
    moduleKey === 'shop_glasses_assured'
      ? 'https://p0.meituan.net/travelcube/ca11db6c35968d637889d780e1e510d1526688.png'
      : 'https://p0.meituan.net/travelcube/45277c64c17bb1dc6bb5a9f033a1efb7267867.png'
  return (
    <View>
      <ImageBackground
        style={styles.backgroundImage}
        source={{
          uri: backImgUrl
        }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  backgroundImage: {
    width: '100%',
    resizeMode: 'contain',
    height: (508 * DimensionWidth) / 375
  }
})
