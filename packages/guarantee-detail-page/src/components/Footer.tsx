import React from 'react'
import { View, Text, StyleSheet, Image, TouchableWithoutFeedback } from '@mrn/react-native'
import { openUrl } from '@mrn/mrn-utils'
import { lxTrackMGEViewEvent, lxTrackMGEClickEvent } from '@mrn/mrn-utils'

export const Footer = ({ moduleKey, shopName }) => {
  let arrowIcon = ''
  let shopIcon = ''
  let ruleColor = ''
  let shopColor = ''
  let ruleLink = ''
  let mvBid = ''
  let mcBid = ''
  let cid = ''
  if (moduleKey === 'shop_glasses_assured') {
    arrowIcon = 'https://p1.meituan.net/travelcube/dcbf5d9348b2f707fe46f93d223add3e327.png'
    ruleColor = '#ffffffcc'
    shopIcon = 'https://p1.meituan.net/travelcube/5dc759a4190504b19b8d42899a477a37911.png'
    shopColor = '#ffffff99'
    mvBid = 'b_gc_l4dqy6ab_mv'
    mcBid = 'b_gc_l4dqy6ab_mc'
    cid = 'c_gc_i7hzm7c6'
    ruleLink = 'https://cube.meituan.com/cube/block/999f77d604fa/292654/index.html'
  }
  if (moduleKey === 'shop_glasses_guarantee') {
    arrowIcon = 'https://p1.meituan.net/travelcube/b71b708d0c250b303be557df6527a0c1374.png'
    ruleColor = '#1D569BCC'
    shopIcon = 'https://p1.meituan.net/travelcube/0b60ca26e4ff344fa92b7524126a0de61268.png'
    shopColor = '#1D569B99'
    mvBid = 'b_gc_ujlxcqr6_mv'
    mcBid = 'b_gc_ujlxcqr6_mc'
    cid = 'c_gc_u3lfzkpf'
    ruleLink = 'https://cube.meituan.com/cube/block/ab84625c9195/292644/index.html'
  }

  return (
    <View style={styles.container}>
      <TouchableWithoutFeedback
        onPress={() => {
          lxTrackMGEClickEvent('gc', mcBid, cid, {})

          ruleLink && openUrl(ruleLink)
        }}
      >
        <View
          onLayout={() => {
            lxTrackMGEViewEvent('gc', mvBid, cid, {})
          }}
          style={styles.row}
        >
          <Text style={[{ color: ruleColor }, styles.ruleText]}>规则详情</Text>
          <Image style={styles.arrowIcon} source={{ uri: arrowIcon }}></Image>
        </View>
      </TouchableWithoutFeedback>
      {shopName ? (
        <View style={[styles.row, { marginTop: 18 }]}>
          <Image style={styles.shopIcon} source={{ uri: shopIcon }}></Image>
          <Text style={[{ color: shopColor }, styles.shopText]}>{shopName}</Text>
        </View>
      ) : null}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center'
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12
  },
  arrowIcon: {
    width: 12,
    height: 12,
    resizeMode: 'contain'
  },
  shopIcon: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
    marginRight: 3
  },
  ruleText: {
    fontSize: 12,
    fontWeight: '400',
    fontFamily: 'PingFang SC'
  },
  shopText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    fontWeight: '400'
  }
})
