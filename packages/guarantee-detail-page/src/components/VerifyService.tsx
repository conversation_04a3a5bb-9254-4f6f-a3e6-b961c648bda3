import React from 'react'
import { View, Text, StyleSheet, Image } from '@mrn/react-native'
import { MainTitle } from './MainTitle'
export const VerifyService = () => {
  const processIcon = 'https://p0.meituan.net/travelcube/16eaecea7f5a5e247b978fdad5c5a4a542833.png'
  return (
    <View style={styles.container}>
      <MainTitle
        width={148.5}
        title={'https://p0.meituan.net/travelcube/ce3989c399365ce3b6ec6dc77c013a565251.png'}
      />
      <Text style={styles.grayText}>
        正品保障门店售卖的镜片（仅限官方授权品牌/系列）均为正品，支持品牌官方防伪验证，假一赔三。
      </Text>
      <View style={styles.processContainer}>
        <View style={styles.line} />
        <View style={styles.titleContainer}>
          <Text style={styles.subTitle}>验真流程</Text>
          <View style={styles.bgBox} />
        </View>
        <View style={styles.line} />
      </View>
      <View style={{ alignItems: 'center' }}>
        <Image style={styles.processImage} source={{ uri: processIcon }} />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    paddingBottom: 18,
    marginTop: 12
  },
  grayText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    fontWeight: '400',
    color: '#666666',
    marginTop: 10
  },
  processContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 12
  },
  titleContainer: {
    position: 'relative',
    alignItems: 'center'
  },
  subTitle: {
    fontFamily: 'PingFang SC',
    fontSize: 14,
    fontWeight: '500',
    color: '#222222',
    marginHorizontal: 12
  },
  line: {
    width: 121,
    height: 0.5,
    backgroundColor: '#D8F0FF'
  },
  bgBox: {
    position: 'absolute',
    bottom: 0,
    borderRadius: 2,
    backgroundColor: '#D8F0ff',
    width: 64,
    height: 5,
    zIndex: -1
  },

  processImage: {
    width: 313,
    height: 116,
    resizeMode: 'contain'
  }
})
