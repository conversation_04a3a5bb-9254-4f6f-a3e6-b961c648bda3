import React, { useState } from 'react'
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TouchableOpacity,
  Dimensions
} from '@mrn/react-native'
import { MainTitle } from './MainTitle'
import { Popover } from '@nibfe/gc-ui'
import { openUrl } from '@mrn/mrn-utils'
import { lxTrackMGEViewEvent, lxTrackMGEClickEvent } from '@mrn/mrn-utils'
const DimensionWidth = Dimensions.get('window').width
export const CraftsmanList = ({ technicians, total, jumpLink }) => {
  const [popVisible, setPopVisible] = useState(false)

  const successIcon = 'https://p0.meituan.net/travelcube/687727e009a4f98cd0de39230879f95a1104.png'
  const questionIcon = 'https://p1.meituan.net/travelcube/2b3a3d6a443593dee37f190cf01f3209925.png'
  const arrowIcon = 'https://p0.meituan.net/travelcube/208d46be818f3fa87e73955bc2ede35d393.png'

  const renderDoctorList = () => {
    const CardItem: React.FC = ({ item, index }) => {
      return (
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            lxTrackMGEClickEvent('gc', 'b_gc_tkw3tpl7_mc', 'c_gc_i7hzm7c6', {
              index,
              tech_id: item.technicianId
            })
            openUrl(item.detailUrl)
          }}
        >
          <View
            onLayout={() => {
              lxTrackMGEViewEvent('gc', 'b_gc_tkw3tpl7_mv', 'c_gc_i7hzm7c6', {
                index,
                tech_id: item.technicianId
              })
            }}
            style={styles.itemCard}
          >
            <View style={[styles.avatar, { overflow: 'hidden' }]}>
              <Image
                style={styles.avatar}
                source={{
                  uri: item.avatarUrl
                }}
              />
            </View>
            <View style={styles.rightPart}>
              <Text style={styles.name}>{item.name}</Text>
              <Text style={styles.year}>{`${item.title}｜${item.seniority}`}</Text>
              <Text style={styles.num}>{`资质编号： ${item.certifiedNo}`}</Text>
            </View>
          </View>
        </TouchableOpacity>
      )
    }

    return (
      <ScrollView
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        style={styles.listContainer}
        contentContainerStyle={{
          paddingRight: 18
        }}
        onLayout={() => {
          lxTrackMGEViewEvent('gc', 'b_gc_z3pjgrh5_mv', 'c_gc_i7hzm7c6', {})
        }}
        onScrollBeginDrag={() => {
          lxTrackMGEClickEvent('gc', 'b_gc_z3pjgrh5_mc', 'c_gc_i7hzm7c6', {})
        }}
      >
        {technicians[0].avatarUrl
          ? technicians
              .slice(0, 3)
              .map((item, index) => <CardItem key={item} item={item} index={index} />)
          : null}
      </ScrollView>
    )
  }
  const Tips: React.FC = () => {
    return (
      <View style={styles.tip}>
        <Text style={{ color: 'white', fontSize: 10 }}>
          门店具备合格的综合验光仪、 电脑验光仪、瞳距测量仪设备
        </Text>
      </View>
    )
  }
  const renderPopover = () => {
    return (
      <Popover
        visible={popVisible}
        placement="up"
        arrowPosition="right"
        theme="dark"
        onDismiss={() => {
          setPopVisible(false)
        }}
        selections={[{ title: '' }]}
        renderItem={() => <Tips />}
        onPressItem={() => {
          setPopVisible(false)
        }}
        childrenWrapperStyle={{ flexDirection: 'row' }}
      >
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            setPopVisible(true)
          }}
        >
          <Image
            style={{
              width: 13,
              height: 13,
              resizeMode: 'cover'
            }}
            source={{
              uri: questionIcon
            }}
          />
        </TouchableOpacity>
      </Popover>
    )
  }
  return (
    <View style={styles.container}>
      <MainTitle
        title={'https://p1.meituan.net/travelcube/ea3d98a47451fe4a47effdd0b5408eb85974.png'}
        width={108}
      />

      <View style={[styles.titleRow, { marginTop: 10 }]}>
        <Text style={styles.title}>技师资质核实</Text>
      </View>
      <View style={styles.certifiedRow}>
        <Image
          style={styles.icon}
          source={{
            uri: successIcon
          }}
        />
        <Text style={[{ marginRight: 15 }, styles.grayText]}>身份证真实</Text>
        <Image
          style={styles.icon}
          source={{
            uri: successIcon
          }}
        />
        <Text style={styles.grayText}>人社颁发的职业资格证书真实</Text>
      </View>
      {technicians && technicians.length > 0 ? renderDoctorList() : null}

      {total > 3 ? (
        <TouchableOpacity
          activeOpacity={1}
          onPress={() => {
            lxTrackMGEClickEvent('gc', 'b_gc_dti2z7v7_mc', 'c_gc_i7hzm7c6', {})
            openUrl(jumpLink)
          }}
        >
          <View
            onLayout={() => {
              lxTrackMGEViewEvent('gc', 'b_gc_dti2z7v7_mv', 'c_gc_i7hzm7c6', {})
            }}
            style={styles.checkMore}
          >
            <Text style={styles.grayText}>{`查看门店全部${total}名资质验光师`}</Text>
            <Image
              style={styles.icon}
              source={{
                uri: arrowIcon
              }}
            />
          </View>
        </TouchableOpacity>
      ) : null}

      <View style={styles.titleRow}>
        <Text style={styles.title}>设备资质核实</Text>
      </View>
      <View style={styles.certifiedRow}>
        <Image
          style={styles.icon}
          source={{
            uri: successIcon
          }}
        />
        <Text style={[{ marginRight: 15 }, styles.grayText]}>备案信息可查</Text>
        <Image
          style={styles.icon}
          source={{
            uri: successIcon
          }}
        />
        <Text style={styles.grayText}>验光设备齐全</Text>
        {renderPopover()}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 12,
    marginTop: (224.5 * DimensionWidth) / 375
  },
  title: {
    fontFamily: 'PingFang SC',
    fontSize: 15,
    fontWeight: '500',
    color: '#222222'
  },
  grayText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    fontWeight: '400',
    color: '#666666'
  },
  bigIcon: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
    marginRight: 3
  },
  icon: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
    marginRight: 3
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12
  },
  certifiedRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    marginTop: 3
  },
  listContainer: {
    flexDirection: 'row',
    marginHorizontal: -12,
    paddingHorizontal: 12
  },
  itemCard: {
    flexDirection: 'row',
    padding: 9,
    borderRadius: 8,
    backgroundColor: '#f6f9ff',
    marginRight: 6
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25
  },
  name: {
    color: '#222426',
    fontFamily: 'PingFang SC',
    fontSize: 12,
    fontWeight: '500'
  },
  year: {
    color: '#222426',
    fontFamily: 'PingFang SC',
    fontSize: 10,
    fontWeight: '400'
  },
  num: {
    color: '#999999',
    fontFamily: 'PingFang SC',
    fontSize: 10,
    fontWeight: '400'
  },
  rightPart: {
    marginLeft: 6,
    justifyContent: 'center'
  },
  checkMore: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 3
  },
  tip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 3,
    maxWidth: 154
  }
})
