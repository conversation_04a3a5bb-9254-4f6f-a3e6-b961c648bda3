import { mapi } from '@mrn/mrn-utils'
import { GuaranteeDetailRequest, GuaranteeDetailResponse } from '../../src/types'

const URL = 'https://mapi.dianping.com/mapi/nibmp/mcp/gateway/content/querydetailpage.mp'

export const fetchGuaranteeDetailService: (
  params: GuaranteeDetailRequest
) => Promise<GuaranteeDetailResponse> = (params: GuaranteeDetailRequest) => {
  return mapi({
    url: URL,
    method: 'GET',
    params
  })
    .then(res => {
      if (res.commonResp.code === 200) {
        let resData = null

        try {
          resData = JSON.parse(res.data)
        } catch (e) {
          console.warn('接口JSON.parse失败 _', e)
        }

        return resData
      }
    })
    .catch((e: any) => {
      throw e
    })
}
