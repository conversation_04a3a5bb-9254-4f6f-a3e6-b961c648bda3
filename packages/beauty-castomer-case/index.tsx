import { GlobalComponentReporter } from '@mrn/mcc-component-report'
import { AppRegistry } from '@mrn/react-native'
import BeautyCastomerCase from './src/BeautyCastomerCase'
import MyCastomerCase from './src/MyCastomerCase'

/**
 * 这里注册的 mrnproject 可以是子包内不冲突的任意名字
 * 命名规范建议
 * ${repoName}-${packageName}
 */ GlobalComponentReporter.start({ appKey: 'rn_gcbu_mrn-beauty-castomer-case' })
AppRegistry.registerComponent('beautyCastomerCase', () => BeautyCastomerCase)
AppRegistry.registerComponent('myCastomerCase', () => MyCastomerCase)
