# 体验报告上传页面

---
## 启动项目

### 安装

```bash
yarn install
```

### 运行

```bash
yarn run start
```

---
## 项目结构

### 数据结构化
#### src/struct
用来保存页面的组件对象，包含数据状态与组件功能的行为逻辑等方法。使用面向对象(OOB)与面向切面(AOP)的方式，与视图分离开发。便于在保留组件与业务功能的逻辑下方便切换不同的视图渲染框架，并能在组件未渲染的情况下运行组件功能与保持组件状态。

### 视图组件
#### src/components
为当前页面框架使用的前端组件渲染模块。

### 配置文件
#### src/struct/config/index.ts
组件的功能逻辑写在 【数据结构化】目录下，项目的一些自定义业务逻辑写在此配置文件中。

titleParam与footerParam结构配置较为简单，不作介绍。

pannelParams接受一个array集合对象，其中配置属性分别对应

```
  title: string, // 组件标题
  notice?: string,// 展示在标题下方的提示文案
  content?: { // 组件内容 标题下方的内容空间
  moduleName: string, // 调用组件名称 input-标准输入框 image-upload-图片上传模块  text-area-文本域
  moduleParam: { // 填充内容的参数
  key: string, // 提交用的字段主键， 若当前值提交时字段为内容字段，可在key前添加父级字段名，用.隔开如 reference.certPic
    value?: any, // 页面初始化时默认参数
    visible?: boolean, // 当前模块是否渲染
    unvisibleKeepValue?: boolean, // 在模块未渲染时，提交是否带上此模块的值
    validator?: (values, submit) => boolean | Validations , // 验证器，可以选择已经写好的常规验证器Validations，也可以自定义验证函数。自定义验证函数会回传当前模块的值和验证的状态。需要根据自定义方法来判断验证是否通过，返回boolean值。
    ------------------------图片上传模块参数------------------------
    imageKey?: string, /，控制提交的字段，将提交的图片url保存至imageKey指定的字段上
    mainTitle:string, // 用于展示图片上传按钮的文案
    header?: string[], // 用于展示在上传模块上方的文案
    footer?: string[],// 用于展示在上传模块下方的文案
    limitSize?: number,// 限制上传图片的数量
    defaultArray?: boolean, // 默认一张图片不创建集合对象。设置为true后默认创建集合对象
    style?: ImageStyle , // 图片模块展示的样式，默认为普通模式上传，mageStyle.tag为打标模式
    deleteAble?: boolean, // 控制图片是否可以被删除
    editAble>: boolean, // 控制图片是否可以被替换修改
    ------------------------input和text-area模块参数------------------------
    placeholder?: string, //展示placeholder
    customized?: {}, //可以控制元素最外层的属性，例如样式
  }
}
  fieldValue?: { // 展示在标题右边的内容空间
    type: FieldType,// 组件类型 FieldType.picker为选择器类型
    params: { // 组件参数
    key: string, // 提交用的字段主键， 若当前值提交时字段为内容字段，可在key前添加父级字段名，用.隔开如 reference.certPic
    value?: any, // 页面初始化时默认参数
    validator?: (values, submit) => boolean | Validations , // 验证器，可以选择已经写好的常规验证器Validations，也可以自定义验证函数。自定义验证函数会回传当前模块的值和验证的状态。需要根据自定义方法来判断验证是否通过，返回boolean值。
    ------------------------组件为picker参数------------------------
    title: string,// 作为picker标题展示
    viewer: React.element,// picker弹出框内展示的自定义组件，因此处加载是一次性的，会导致在开发环境下热部署功能不能触发内部组件刷新，需要根据环境来判断，如果为开发环境时，使用懒加载方式调用如devEnv? import('components/page/pannel/field-value/modail-content/skus'): SkusViewer,
    paramPaser: (Picker,callback) => void,// 用来准备viewer中的参数，会接收到Picker对象和callback函数，将计算好的结果传入callback方法，会立即打开弹窗
    transport? boolean: , // 用于将值传送至content位置展示
    disabled?: boolean,// 禁用当前picker
    setValueCallBack?: (picker, value) => void // 用来执行值设置之后的回调，方法会传入当前picker对象与当前的值
    }
  }
```

---
## API
### 根据key获取value
```
SubmitData.getKeyValue(string | string[]) : SubmitKey[]
```

### 重新验证
```
SubmitData.getInstance().validate()
```
或在class对象的成员方法名上加上
```
@validate
```

### 重渲染

```
Page.getInstance().flushPage()
```
或在class对象的成员方法名上加上
```
@rerender
```
注意 如果一个方法既需要重新验证又需要重渲染，应该遵循先验证再渲染的逻辑顺序，不然验证后的结果不会被渲染。注解需要反向顺序写
```
@rerender
@validate
```

建议注解加在入口方法中，不推荐写在被调用的方法中。这样会导致有时候不需要渲染但是被渲染了，有时候会被多次渲染。

### 提交数据管理
src/struct/common/SubmitData.ts

### Loading 状态管理
src/struct/common/LoadingManager.ts

### 上传页面对象
src/struct/page/index.ts

### 上传页面API
src/struct/request/BeautyCastomerCaseRequest.ts

