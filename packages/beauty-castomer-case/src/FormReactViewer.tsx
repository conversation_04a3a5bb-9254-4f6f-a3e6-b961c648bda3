import { Page } from './struct/page'
import React, { useReducer } from 'react'
import { View } from '@mrn/react-native'
import { PageView } from 'components/page'
import TopViewProvider from '@max/leez-top-view-provider'
import LoadingManager from '@max/leez-loading-manager'
import DialogManager from '@max/leez-dialog-manager'
import ToastManager from '@max/leez-toast-manager'

const reducer = state => !state
const useSmartUpdate = () => useReducer(reducer, false)[1]
export default (props: { data: Page }) => {
  const smartUpdate = useSmartUpdate()
  props.data.bindFlusher(smartUpdate)
  return (
    <View style={{ flex: 1 }}>
      <TopViewProvider>
        <PageView data={props.data} />
      </TopViewProvider>
      <DialogManager />
      <LoadingManager />
      <ToastManager />
    </View>
  )
}
