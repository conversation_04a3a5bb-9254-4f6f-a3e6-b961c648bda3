import React from 'react'
import { View } from '@mrn/react-native'
import { getDeviceStatusBarHeight } from '@mrn/mrn-gc-base'
import FormReactViewer from './FormReactViewer'
import { Page } from './struct/page'
import { getConfig } from './struct/config'
import { pageRouterClose } from '@mrn/mrn-utils'

export default (props: any) => {
  let { id, devEnv } = props
  const config = getConfig(pageRouterClose, devEnv)
  //id = '137400719'
  let instance = Page.createInstance(config, id)
  return (
    <View style={{ flex: 1 }}>
      <View style={{ height: getDeviceStatusBarHeight() }} />
      <FormReactViewer data={instance} />
    </View>
  )
}
