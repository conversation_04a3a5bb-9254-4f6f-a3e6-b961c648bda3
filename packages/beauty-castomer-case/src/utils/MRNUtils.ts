import { isDP } from '@mrn/mrn-gc-base'
import KNB from '@mrn/mrn-knb'

export function getLocationInfo() {
  return new Promise(resolve => {
    KNB.getLocation({
      // sceneToken: 'dd-d2bdf2200b64c4ea', //
      type: isDP() ? 'WGS84' : 'GCJ02', // 可选值wgs84(标准坐标系)， gcj02（国测局 火星坐标）,1.2.0版本支持，建议微信小写，美团写大写
      timeout: 2000, //定位超时时间，1.2.0版本默认为5000，1.1.0版本默认为6000
      cache: true, //默认为true
      success: location => {
        resolve(location)
      },
      fail: () => {
        resolve(undefined)
      }
    })
    setTimeout(() => {
      resolve(undefined)
    }, 2000)
  })
}

export function getCityInfo() {
  return new Promise(resolve => {
    KNB.getCity({
      success: city => {
        resolve(city)
      },
      fail: _ => {
        resolve(undefined)
      }
    })
    setTimeout(() => {
      resolve(undefined)
    }, 2000)
  })
}
