import React from 'react'
import { View, StyleSheet } from '@mrn/react-native'
import Icon from '@max/leez-icon'
export interface CheckBoxProps {
  checked: boolean
}
const styles = StyleSheet.create({
  CheckBoxMain: {
    width: 17,
    height: 17,
    borderRadius: 3,
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center'
  },
  Checked: {
    backgroundColor: '#FFD100'
  },
  UnChecked: {
    backgroundColor: 'white',
    borderColor: '#979797',
    borderStyle: 'solid',
    borderWidth: 1
  }
})
export const CheckBox = (props: CheckBoxProps) => {
  const { checked } = props
  return (
    <>
      {checked ? (
        <View style={[styles.CheckBoxMain, styles.Checked]}>
          <Icon name="gouxuanxian" type="title1" className="demo-icon-icon" />
        </View>
      ) : (
        <View style={[styles.CheckBoxMain, styles.UnChecked]} />
      )}
    </>
  )
}
