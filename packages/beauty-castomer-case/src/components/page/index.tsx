import React from 'react'
import { StyleSheet, View, ScrollView } from '@mrn/react-native'
import { Page } from '../../struct/page'
import { TitleView } from 'components/page/title'
import { FooterView } from 'components/page/footer'
import { PannelView } from 'components/page/pannel'
import { SelectModailView } from 'components/page/pannel/field-value/SelectModail'
const styles = StyleSheet.create({
  Contents: {
    paddingLeft: 20,
    paddingRight: 20,
    paddingTop: 15
  },
  Spliter: {
    width: '100%',
    height: 1,
    backgroundColor: '#EEEEEE'
  }
})
export const PageView = (props: { data: Page }) => {
  const { title, pannels, footer } = props.data
  return (
    <View style={{ flex: 1 }}>
      <TitleView data={title} />
      <ScrollView scrollEventThrottle={64} style={styles.Contents}>
        {pannels?.map((x, y) => {
          if (
            !(
              (x.fieldValue && x.fieldValue.visible === false) ||
              (x.content && x.content?.visible === false)
            )
          ) {
            return (
              <View key={y}>
                <PannelView data={x} />
                {y !== pannels.length - 1 && <View style={styles.Spliter} />}
              </View>
            )
          }
        })}
        <View style={{ height: 200 }} />
      </ScrollView>
      <FooterView data={footer} />
      <SelectModailView />
    </View>
  )
}
