import React from 'react'
import { View, StyleSheet, TouchableWithoutFeedback, Text } from '@mrn/react-native'
import { Title } from '../../../struct/page/title'
const styles = StyleSheet.create({
  TopTitle: {
    height: 44,
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: 30,
    paddingRight: 30
  },
  LeftTitle: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 15,
    color: '#111111',
    fontWeight: '500'
  },
  MainTitle: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 17,
    color: '#111111'
  },
  RightTitle: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 15,
    color: '#111111',
    fontWeight: '500'
  }
})
export const TitleView = (props: { data: Title }) => {
  const { leftTitle, rightTitle, bigTitle } = props.data.param
  return (
    <View style={styles.TopTitle}>
      {leftTitle && (
        <TouchableWithoutFeedback
          onPress={() => {
            leftTitle.behavior && leftTitle.behavior()
          }}
        >
          <Text style={styles.LeftTitle}>{leftTitle.title}</Text>
        </TouchableWithoutFeedback>
      )}

      <Text style={styles.MainTitle}>{bigTitle}</Text>
      {rightTitle && (
        <TouchableWithoutFeedback
          onPress={() => {
            rightTitle.behavior && rightTitle.behavior()
          }}
        >
          <Text style={styles.RightTitle}>{rightTitle.title}</Text>
        </TouchableWithoutFeedback>
      )}
    </View>
  )
}
