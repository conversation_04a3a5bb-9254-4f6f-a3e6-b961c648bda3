import React from 'react'
import TopView from '@max/leez-top-view'
import ModalBaseContainer from '@max/leez-modal-base-container'
import AgreementModail from '../../../struct/common/AgreementModail'
import {
  Image,
  Text,
  TouchableWithoutFeedback,
  View,
  StyleSheet,
  Platform,
  ScrollView
} from '@mrn/react-native'
const shadow =
  Platform.OS === 'ios'
    ? {
        shadowColor: 'rgba(0, 0, 0, 0.05)',
        shadowOffset: { width: 0, height: -3 }, // 注意这里的height是负值
        shadowOpacity: 1,
        shadowRadius: 3.84
      }
    : {
        elevation: 5
      }
const styles = StyleSheet.create({
  modailPannel: {
    backgroundColor: '#FFFFFF',
    width: '100%',
    height: 650,
    borderTopRightRadius: 15,
    borderTopLeftRadius: 15
  },
  footer: {
    height: 62,
    width: '100%',
    ...shadow, // 应用阴影效果
    zIndex: 10, // 确保阴影显示在其他元素之上
    position: 'absolute',
    bottom: 0,
    paddingTop: 9,
    backgroundColor: 'white'
  },
  dismiss: {
    marginLeft: 15,
    marginRight: 15,
    height: 44,
    backgroundColor: '#FF6633',
    borderRadius: 22,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center'
  },
  dismissText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  title: {
    height: 51,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  titleText: {
    fontSize: 16,
    fontFamily: 'PingFangSC-Regular',
    color: '#222222',
    fontWeight: '500'
  },
  close: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: '#F4F4F4',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    right: 15
  },
  closeIcon: {
    width: 9,
    height: 9
  },
  contents: {
    marginLeft: 17,
    marginRight: 17,
    fontFamily: 'PingFangSC-Regular',
    lineHeight: 21.5,
    color: '#222222',
    fontSize: 14,
    marginBottom: 62
  }
})
export const AgreementModailView = (props: { data: AgreementModail }) => {
  return (
    <TopView>
      <ModalBaseContainer
        visible={props.data.visible}
        useRNModal={false}
        type="slide"
        slideFrom="bottom"
        onMaskPress={() => props.data.toggle()}
      >
        <View style={styles.modailPannel}>
          <View style={styles.title}>
            <Text style={styles.titleText}>{props.data.param.title}</Text>
            <TouchableWithoutFeedback onPress={() => props.data.toggle()}>
              <View style={styles.close}>
                <Image
                  style={styles.closeIcon}
                  source={{
                    uri: 'https://p0.meituan.net/travelcube/c363a0a2a2268ba2f179289b50bd7006291.png'
                  }}
                />
              </View>
            </TouchableWithoutFeedback>
          </View>
          <ScrollView scrollEventThrottle={64}>
            <Text style={styles.contents}>{props.data.param.content}</Text>
          </ScrollView>

          <View style={styles.footer}>
            <TouchableWithoutFeedback onPress={() => props.data.toggle()}>
              <View style={styles.dismiss}>
                <Text style={styles.dismissText}>我知道了</Text>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </View>
        <View style={{ height: 24, backgroundColor: '#FFFFFF' }} />
      </ModalBaseContainer>
    </TopView>
  )
}
