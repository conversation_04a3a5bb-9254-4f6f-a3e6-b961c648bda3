import React from 'react'
import { Platform } from '@mrn/react-native'
import {
  StyleSheet,
  View,
  Text,
  TouchableWithoutFeedback,
  TouchableOpacity
} from '@mrn/react-native'
import { Footer } from '../../../struct/page/footer'
import { SafeAreaView } from '@mrn/react-navigation'
import { CheckBox } from '../../ui/CheckBox'
import { AgreementModailView } from 'components/page/footer/Modail'

const shadow =
  Platform.OS === 'ios'
    ? {
        shadowColor: 'rgba(0, 0, 0, 0.05)',
        shadowOffset: { width: 0, height: -3 }, // 注意这里的height是负值
        shadowOpacity: 1,
        shadowRadius: 3.84
      }
    : {
        elevation: 5
      }
const styles = StyleSheet.create({
  FooterPannel: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    paddingLeft: 15,
    paddingRight: 15,
    ...shadow, // 应用阴影效果
    zIndex: 10, // 确保阴影显示在其他元素之上
    backgroundColor: 'white',
    paddingTop: 15
  },
  AgreementsField: {
    display: 'flex',
    flexDirection: 'row',
    height: 47
  },
  AgreementsContent: {
    display: 'flex',
    flexDirection: 'row'
  },
  SubmitField: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 40
  },
  SubmitBtn: {
    width: 345,
    height: '100%',
    backgroundColor: '#F0F0F0',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20
  },
  Agreements: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    color: '#166FF7'
  },
  AgreementTitle: {
    marginLeft: 6,
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    color: '#666666'
  },
  SubmitText: {
    color: '#CCCCCC',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 16
  },
  CheckboxStyle: {
    width: 17,
    height: 17,
    borderRadius: 0
  }
})
export const FooterView = (props: { data: Footer }) => {
  const { param, agreementStatus, agreementModal } = props.data
  const { agreements, submitBtn } = param
  return (
    <View style={styles.FooterPannel}>
      {agreementModal && <AgreementModailView data={agreementModal} />}
      <View style={styles.AgreementsField}>
        <TouchableOpacity
          onPress={() => {
            props.data.tapCheckBox && props.data.tapCheckBox()
          }}
        >
          <CheckBox checked={agreementStatus} />
        </TouchableOpacity>
        <View style={styles.AgreementsContent}>
          <Text style={styles.AgreementTitle}>阅读并同意</Text>
          {agreements?.map((x, y) => (
            <TouchableWithoutFeedback
              key={y}
              onPress={() => {
                props.data.openAgreementModail(x)
              }}
            >
              <Text style={styles.Agreements}>《{x.title}》</Text>
            </TouchableWithoutFeedback>
          ))}
        </View>
      </View>
      <View style={styles.SubmitField}>
        <TouchableWithoutFeedback
          onPress={() => {
            props.data.tapSubmitBtn && props.data.tapSubmitBtn()
          }}
        >
          <View
            style={[styles.SubmitBtn, props.data.validateCheck && { backgroundColor: '#FF6633' }]}
          >
            <Text style={[styles.SubmitText, props.data.validateCheck && { color: '#FFFFFF' }]}>
              {submitBtn.title}
            </Text>
          </View>
        </TouchableWithoutFeedback>
      </View>
      <SafeAreaView />
    </View>
  )
}
