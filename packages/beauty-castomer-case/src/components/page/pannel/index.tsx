import React from 'react'
import { Text, View, StyleSheet, TouchableWithoutFeedback } from '@mrn/react-native'
import { Pannel } from '../../../struct/page/pannel'
import { Page } from '../../../struct/page'
import { FieldView } from 'components/page/pannel/field-value'

const styles = StyleSheet.create({
  BigContent: {
    paddingTop: 9,
    paddingBottom: 9
  },
  TitleField: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexShrink: 1
  },
  Title: {
    marginRight: 60
  },
  TitleText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 16,
    fontWeight: '500',
    color: '#111111'
  },
  Notice: {
    marginTop: 9
  },
  NoticeText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 13,
    color: '#FF4B10'
  },
  fieldViewStyle: {},
  fieldValue: {
    fontFamily: 'PingFangSC-Regular'
  },
  disabledTitle: {
    color: '#AAAAAA'
  }
})
export const PannelView = (props: { data: Pannel }) => {
  const { title, notice, content } = props.data.param
  const CotentView = content?.moduleName && Page.getInstance().pannelViews[content.moduleName]
  return (
    <TouchableWithoutFeedback
      onPress={() =>
        props.data.fieldValue &&
        !props.data.fieldValue.disabled &&
        props.data.fieldValue.pickValue()
      }
    >
      <View style={styles.BigContent}>
        <View style={styles.TitleField}>
          <View style={styles.Title}>
            <Text
              style={
                props.data.fieldValue && props.data.fieldValue.disabled
                  ? [styles.TitleText, styles.disabledTitle]
                  : styles.TitleText
              }
            >
              {title}
            </Text>
          </View>
          {props.data.fieldValue && <FieldView data={props.data} style={styles.fieldViewStyle} />}
        </View>
        {notice && (
          <View style={styles.Notice}>
            <Text style={styles.NoticeText}>*{notice}</Text>
          </View>
        )}
        {CotentView && <CotentView data={props.data.content} />}
        {props.data.fieldValue &&
          props.data.fieldValue.param.transport &&
          props.data.fieldValue.label && (
            <View>
              <Text style={styles.fieldValue}>{props.data.fieldValue.label}</Text>
            </View>
          )}
      </View>
    </TouchableWithoutFeedback>
  )
}
