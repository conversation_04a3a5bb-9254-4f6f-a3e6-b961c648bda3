import React, { useState } from 'react'
import { View, TextInput, StyleSheet, Text } from '@mrn/react-native'
import Input from '../../../../struct/page/pannel/content/Input'
const styles = StyleSheet.create({
  InputContent: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    width: '100%'
  },
  Input: {
    fontSize: 16,
    color: '#333333',
    flexShrink: 1,
    minWidth: '80%',
    fontFamily: 'PingFangSC-Regular'
  },
  Count: {
    color: '#999999',
    fontSize: 13,
    fontFamily: 'PingFangSC-Regular'
  }
})
const InputView = (props: { data: Input }) => {
  const { param, value } = props.data
  const { placeholder } = param
  const [count, setCount] = useState(0)
  const textInput = (inValue: string) => {
    setCount(inValue.length)
    props.data.value = inValue
  }
  return (
    <View style={styles.InputContent}>
      <TextInput
        onBlur={() => props.data.blur()}
        multiline={true}
        maxLength={30}
        style={styles.Input}
        placeholder={placeholder}
        defaultValue={value}
        onChangeText={newText => textInput(newText)}
      />
      <View>
        <Text style={styles.Count}>{count}/30</Text>
      </View>
    </View>
  )
}

export default InputView
