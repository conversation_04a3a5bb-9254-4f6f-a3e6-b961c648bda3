import React from 'react'
import { View, Text, StyleSheet, TouchableWithoutFeedback, ScrollView } from '@mrn/react-native'
import ImageUploaderView from 'components/page/pannel/content/image-upload/ImageUploaderView'
import ImageViewerView from 'components/page/pannel/content/image-upload/ImageViewerView'
import ImageUpload, { isLink } from '../../../../../struct/page/pannel/content/image-upload'
import { TagPickerViewer } from 'components/page/pannel/content/image-upload/TagPicker'
import { BigImagePreviewView } from 'components/page/pannel/content/image-upload/BigImagePreview'

const styles = StyleSheet.create({
  uploadPannel: {},
  linkText: {
    color: '#166FF7'
  },
  headerText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 13,
    lineHeight: 18,
    color: '#999999',
    marginTop: 9
  },
  footerText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 11,
    lineHeight: 16,
    color: '#777777'
  },
  imageCenter: {
    marginTop: 10,
    marginBottom: 10,
    display: 'flex',
    flexDirection: 'row'
  },
  gap: {
    width: 6
  }
})
const ImageUploadView = (props: { data: ImageUpload }) => {
  const { param, imageUploader, imageViewers } = props.data
  const { header, footer, rightContent } = param
  return (
    <View style={styles.uploadPannel}>
      {header &&
        header.map((x, y) => (
          <TouchableWithoutFeedback key={y} onPress={() => isLink(x) && x.habdle()}>
            <Text style={isLink(x) ? [styles.headerText, styles.linkText] : styles.headerText}>
              {x}
            </Text>
          </TouchableWithoutFeedback>
        ))}
      {rightContent && imageViewers.length > 0 && rightContent({ width: '100%' })}
      <ScrollView
        scrollEventThrottle={64}
        showsHorizontalScrollIndicator={false}
        horizontal={true}
        alwaysBounceHorizontal={false}
        style={{ width: '100%' }}
      >
        <View style={styles.imageCenter}>
          {param.limitSize - imageViewers.length > 0 && <ImageUploaderView data={imageUploader} />}

          <View style={styles.gap} />
          {rightContent && imageViewers.length === 0 && rightContent({ width: 230 })}
          {imageViewers &&
            imageViewers.map((x, y) => (
              <View key={y} style={{ display: 'flex', flexDirection: 'row' }}>
                <ImageViewerView data={x} index={y} />
                <View style={styles.gap} />
              </View>
            ))}
        </View>
      </ScrollView>

      {footer &&
        footer.map((x, y) => (
          <TouchableWithoutFeedback key={y} onPress={() => isLink(x) && x.habdle()}>
            <Text style={isLink(x) ? [styles.footerText, styles.linkText] : styles.footerText}>
              {x}
            </Text>
          </TouchableWithoutFeedback>
        ))}
      <TagPickerViewer />
      <BigImagePreviewView />
    </View>
  )
}

export default ImageUploadView
