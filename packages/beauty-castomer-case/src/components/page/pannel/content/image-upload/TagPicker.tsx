import React from 'react'
import TopView from '@max/leez-top-view'
import ModalBaseContainer from '@max/leez-modal-base-container'
import TagPicker, {
  TagType
} from '../../../../../struct/page/pannel/content/image-upload/TagPicker'
import { View, Text, TouchableWithoutFeedback, StyleSheet, TextInput } from '@mrn/react-native'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
const styles = StyleSheet.create({
  pannel: {
    width: 380,
    padding: 20,
    borderRadius: 5,
    backgroundColor: '#FFFFFF'
  },
  tags: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-start'
  },
  tagBorder: {
    padding: 8,
    paddingLeft: 11,
    marginRight: 4.5,
    marginLeft: 4.5,
    borderStyle: 'solid',
    borderColor: 'rgb(240,240,240)',
    borderWidth: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 27
  },
  selectedBorder: {
    borderColor: '#FF6633'
  },
  tagText: {
    color: '#333333',
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '400',
    fontSize: 13
  },
  selectedText: {
    color: '#FF6633'
  },
  tagGroup: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start'
  },
  textInput: {
    width: 41,
    height: 28,
    borderRadius: 3,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: '#FF6633',
    color: '#FF6633',
    marginRight: 4.5,
    textAlign: 'center',
    paddingTop: 5,
    paddingBottom: 5
  },
  dateInputGroup: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center'
  },
  confirmContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 25
  },
  confirmBtn: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 20,
    paddingRight: 20,
    borderRadius: 25
  },
  btnText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  titleText: {
    fontSize: 16,
    fontFamily: 'PingFangSC-Regular',
    color: '#222222',
    fontWeight: '500'
  },
  title: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10
  }
})
export const TagPickerViewer = () => {
  const instance = TagPicker.getInstance()
  return (
    <TopView>
      {instance && (
        <ModalBaseContainer
          visible={instance.visible}
          useRNModal={false}
          type="fade"
          onMaskPress={() => instance.toggle()}
        >
          <View style={styles.pannel}>
            <View style={styles.title}>
              <Text style={styles.titleText}>请选择标签</Text>
            </View>
            <View style={styles.tags}>
              {instance.tags.map((x, y) => (
                <View style={styles.tagGroup} key={y}>
                  <TouchableWithoutFeedback onPress={() => instance.selectTag(x)}>
                    <View
                      style={
                        x.selected ? [styles.tagBorder, styles.selectedBorder] : styles.tagBorder
                      }
                    >
                      <Text
                        style={x.selected ? [styles.tagText, styles.selectedText] : styles.tagText}
                      >
                        # {x.tagName}
                      </Text>
                    </View>
                  </TouchableWithoutFeedback>
                  {x.selected && x.tagType === TagType.after && (
                    <View style={styles.dateInputGroup}>
                      <TextInput
                        style={styles.textInput}
                        defaultValue={x.tagDays.toString()}
                        maxLength={4}
                        keyboardType={'numeric'}
                        returnKeyType="done"
                        onChangeText={newText => (x.tagDays = Number(newText))}
                      />
                      <Text style={[styles.tagText, styles.selectedText]}>天</Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
            <View style={styles.confirmContainer}>
              <TouchableWithoutFeedback onPress={() => instance.tag()}>
                <LinearGradient
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  colors={['#FF7700', '#FF4B10']}
                  style={styles.confirmBtn}
                >
                  <Text style={styles.btnText}>确认</Text>
                </LinearGradient>
              </TouchableWithoutFeedback>
            </View>
          </View>
        </ModalBaseContainer>
      )}
    </TopView>
  )
}
