import React, { useCallback } from 'react'
import TopView from '@max/leez-top-view'
import ModalBaseContainer from '@max/leez-modal-base-container'
import BigImagePreview from '../../../../../struct/page/pannel/content/image-upload/BigImagePreview'
import {
  View,
  FlatList,
  Image,
  StyleSheet,
  Text,
  TouchableWithoutFeedback
} from '@mrn/react-native'
import Icon from '@max/leez-icon'
import { getWidth, getHeight } from '@mrn/mrn-gc-utils'
const styles = StyleSheet.create({
  bigImg: {
    width: getWidth(),
    height: getHeight()
  },
  topBar: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 44
  },
  back: {
    color: '#FFFFFF',
    position: 'absolute',
    left: 10
  },
  pageIndex: {
    color: '#FFFFFF',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 18,
    lineHeight: 22,
    fontWeight: '500'
  },
  previewTag: {
    position: 'absolute',
    backgroundColor: '#F6F6F6',
    bottom: 120,
    left: 12,
    paddingRight: 11,
    paddingLeft: 11,
    paddingTop: 8,
    paddingBottom: 8,
    borderRadius: 27
  },
  imgs: {
    backgroundColor: '#000000'
  },
  imgPannel: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  }
})
export const BigImagePreviewView = () => {
  let instance = BigImagePreview.getInstance()
  const width = getWidth()

  const getItemLayout = (data, index) => ({
    length: width,
    offset: width * index,
    index
  })
  const onViewableItemsChanged = useCallback(({ viewableItems }) => {
    instance = BigImagePreview.getInstance()
    instance && instance.onViewableItemsChanged(viewableItems[0].index)
  }, [])
  return (
    <TopView>
      {instance && (
        <ModalBaseContainer
          visible={instance.visible}
          useRNModal={false}
          type="fade"
          onMaskPress={() => instance.toggle()}
        >
          <TouchableWithoutFeedback onPress={() => instance.toggle()}>
            <View style={styles.topBar}>
              <Icon style={styles.back} name="fanhui" type="title1" className="demo-icon-icon" />

              <Text style={styles.pageIndex}>
                {`${instance.currentIndex + 1}/${instance.imageViewers.length}`}
              </Text>
            </View>
          </TouchableWithoutFeedback>

          <FlatList
            style={styles.imgs}
            data={instance.imageViewers}
            renderItem={({ item }) => (
              <View style={styles.imgPannel}>
                <Image
                  style={styles.bigImg}
                  source={{ uri: item.value.url || item.value }}
                  resizeMode={'contain'}
                />
                {item.value.tagName && (
                  <View style={styles.previewTag}>
                    <Text># {item.value.tagName}</Text>
                  </View>
                )}
              </View>
            )}
            keyExtractor={(_, index) => (index && index.toString()) || ''}
            horizontal={true}
            pagingEnabled={true}
            showsHorizontalScrollIndicator={false}
            onViewableItemsChanged={onViewableItemsChanged}
            viewabilityConfig={{ viewAreaCoveragePercentThreshold: 50 }}
            initialScrollIndex={instance.currentIndex}
            getItemLayout={getItemLayout}
          />
        </ModalBaseContainer>
      )}
    </TopView>
  )
}
