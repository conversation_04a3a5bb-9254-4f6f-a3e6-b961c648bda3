import React from 'react'
import { View, StyleSheet, Text, ViewStyle } from '@mrn/react-native'
const styles = StyleSheet.create({
  title: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 13,
    fontWeight: '500',
    lineHeight: 24,
    color: '#111111'
  },
  normalContent: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    lineHeight: 18,
    color: '#777777'
  },
  highLightContent: {}
})
export default (props: ViewStyle) => {
  return (
    <View style={props}>
      <Text style={styles.title}>为保证消费真实性，请上传消费凭证</Text>
      <Text style={styles.normalContent}>
        需体现消费门店、体验项目、金额等信息，可以是
        <Text style={styles.highLightContent}>门店支付凭证、缴费单、发票</Text>
        等照片（时间有效期为半年内)；此类
        <Text style={styles.highLightContent}>隐私信息不公开展示</Text>
      </Text>
    </View>
  )
}
