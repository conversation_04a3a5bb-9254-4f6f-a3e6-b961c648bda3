import React from 'react'
import { View, Image, Text, StyleSheet, TouchableWithoutFeedback } from '@mrn/react-native'
import ImageUploader from '../../../../../struct/page/pannel/content/image-upload/ImageUploader'
const styles = StyleSheet.create({
  uploadBox: {
    width: 96,
    height: 96,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    borderStyle: 'dashed',
    borderWidth: 1,
    borderColor: '#CCCCCC'
  },
  image: {
    width: 31,
    height: 22
  },
  texts: {
    marginTop: 8,
    fontFamily: 'PingFangSC-Regular',
    fontSize: 11,
    lineHeight: 13,
    color: '#777777'
  }
})
const ImageUploaderView = (props: { data: ImageUploader }) => {
  const { title } = props.data.param
  return (
    <TouchableWithoutFeedback
      onPress={() =>
        props.data.upload(
          props.data.parent.param.limitSize - props.data.parent.imageViewers.length,
          props.data.parent.param.limitSize
        )
      }
    >
      <View style={styles.uploadBox}>
        <Image
          style={styles.image}
          source={{
            uri: 'https://p0.meituan.net/travelcube/cd9241614438259404d8e173c76f9a771813.png'
          }}
        />
        <Text style={styles.texts}>{title}</Text>
      </View>
    </TouchableWithoutFeedback>
  )
}

export default ImageUploaderView
