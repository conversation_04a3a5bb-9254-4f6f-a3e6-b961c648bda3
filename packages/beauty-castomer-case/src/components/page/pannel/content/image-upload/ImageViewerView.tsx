import React from 'react'
import { View, Image, StyleSheet, TouchableWithoutFeedback, Text } from '@mrn/react-native'
import ImageViewer from '../../../../../struct/page/pannel/content/image-upload/ImageViewer'
import { ImageStyle } from '../../../../../struct/page/pannel/content/image-upload'
import { LinearGradient } from '@mrn/react-native-linear-gradient'

const styles = StyleSheet.create({
  viewBox: {
    width: 96,
    height: 96
  },
  editIcon: {
    width: '100%',
    height: 45,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
    alignItems: 'flex-start',
    position: 'absolute',
    bottom: 0,
    paddingRight: 5,
    paddingLeft: 5,
    paddingBottom: 6
  },
  tagName: {
    color: '#FFFFFF',
    fontWeight: '400',
    fontSize: 10,
    fontFamily: 'PingFangSC-Regular'
  },
  tagBtn: {
    color: '#FFFFFF',
    fontWeight: '400',
    fontSize: 11,
    fontFamily: 'PingFangSC-Regular'
  },
  tagBtns: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 2
  },
  tagBtnFrame: {
    paddingBottom: 3,
    paddingLeft: 9,
    paddingRight: 9,
    paddingTop: 3,
    borderColor: '#FFFFFF',
    borderRadius: 12,
    borderStyle: 'solid',
    borderWidth: 0.5
  },
  backGroundImage: {
    width: 96,
    height: 96,
    position: 'absolute'
  },
  closeIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: 'rgba(0, 0, 0, 0.65)',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  }
})
const ImageViewerView = (props: { data: ImageViewer; index: number }) => {
  const { param, value } = props.data
  const { editAble, deleteAble, imageUrl, style } = param
  return (
    <View style={styles.viewBox}>
      <TouchableWithoutFeedback onPress={() => props.data.preview(props.index)}>
        <Image style={styles.backGroundImage} source={{ uri: imageUrl }} />
      </TouchableWithoutFeedback>
      {deleteAble && (
        <TouchableWithoutFeedback onPress={() => props.data.delete(props.index)}>
          <View style={styles.closeIcon}>
            <Image
              style={{ width: 7, height: 7 }}
              source={{
                uri: 'https://p0.meituan.net/travelcube/d0316e57e57f4aa0ecb05843cc221fa6184.png'
              }}
            />
          </View>
        </TouchableWithoutFeedback>
      )}
      {editAble && style === ImageStyle.tag && (
        <LinearGradient
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          colors={['#00000000', '#000000B2']}
          style={styles.editIcon}
        >
          <Text style={styles.tagName}>{value.tagName || '待打标'}</Text>
          <View style={styles.tagBtns}>
            <TouchableWithoutFeedback onPress={() => props.data.edit(props.index)}>
              <View style={styles.tagBtnFrame}>
                <Text style={styles.tagBtn}>替换</Text>
              </View>
            </TouchableWithoutFeedback>
            <TouchableWithoutFeedback onPress={() => props.data.openTagModail()}>
              <View style={styles.tagBtnFrame}>
                <Text style={styles.tagBtn}>标签</Text>
              </View>
            </TouchableWithoutFeedback>
          </View>
        </LinearGradient>
      )}
    </View>
  )
}
export default ImageViewerView
