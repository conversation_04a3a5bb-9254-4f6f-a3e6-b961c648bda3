import React from 'react'
import { TextInput, View, StyleSheet } from '@mrn/react-native'
import TextArea from '../../../../struct/page/pannel/content/TextArea'
const styles = StyleSheet.create({
  TextInput: {
    fontSize: 15,
    fontFamily: 'PingFangSC-Regular',
    color: '#111111'
  }
})
const TextAreaView = (props: { data: TextArea }) => {
  const { param } = props.data
  const { placeholder, customized } = param
  return (
    <View>
      <TextInput
        onBlur={() => props.data.blur()}
        style={[styles.TextInput, customized?.style]}
        placeholder={placeholder}
        defaultValue={props.data.value}
        multiline={true}
        onChangeText={newValue => (props.data.value = newValue)}
      />
    </View>
  )
}

export default TextAreaView
