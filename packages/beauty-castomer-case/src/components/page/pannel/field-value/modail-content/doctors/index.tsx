import View from '@hfe/max-view'
import Text from '@hfe/max-text'
import RecyclerView from '@hfe/max-recyclerview'
import React, { useRef } from 'react'
import { getSafeAreaBottom } from '@mrn/mrn-gc-base'
import LButton from '@max/leez-button'
import { Image, StyleSheet } from 'react-native'
import {
  Doctor,
  DoctorData,
  loadmore
} from 'src/struct/page/pannel/field-value/modail-content/doctors/dotctor'
import { Picker } from 'src/struct/page/pannel/field-value/Picker'

const style = StyleSheet.create({
  rightContent: {
    marginLeft: 9,
    justifyContent: 'space-between',
    flexGrow: 1,
    marginTop: 6,
    marginBottom: 6
  },
  mainText: {
    color: '#222222',
    fontWeight: '600',
    fontSize: 16,
    height: 17
  },
  subtext: {
    color: '#666666',
    fontWeight: '500',
    fontSize: 12,
    letterSpacing: 0,
    textAlign: 'left',
    height: 17
  },
  recyclerView: {
    backgroundColor: '#F4F4F4',
    flexGrow: 1
  },
  image: { width: 60, height: 60, borderRadius: 6 },

  recyclerViewHeader: { height: 0, width: 150 },
  recyclerViewCell: {
    height: 84,
    backgroundColor: '#FFFFFF',
    padding: 12,
    paddingStart: 16,
    paddingEnd: 16,
    flexDirection: 'row'
  },
  imageBg: { borderRadius: 6, overflow: 'hidden' },
  choose: { alignSelf: 'center' }
})
/**
 * 选择医师列表
 */

export default function DoctorList(props: {
  data: { doctorData: DoctorData; picker: Picker }
  confirm: Function
}) {
  const doctorsList = props.data?.doctorData?.resultList
  const totalHit = props.data?.doctorData?.totalHit || 0
  const hasMore = totalHit > doctorsList?.length
  const cacheRef = useRef({ pageNum: 1, hasMore, shopId: '' })
  return (
    <RecyclerView
      style={style.recyclerView}
      stickyHeaderIndices={[0]}
      showsVerticalScrollIndicator={false}
      onEndReached={() => loadmore(hasMore, cacheRef)}
    >
      <RecyclerView.Header>
        <View style={style.recyclerViewHeader} />
      </RecyclerView.Header>
      {doctorsList &&
        doctorsList.map((item, index) => {
          return DoctorCell(index, doctorsList, props.confirm)
        })}
    </RecyclerView>
  )
}
function DoctorCell(index: number, doctors: Doctor[], confirm: Function): React.JSX.Element {
  function onPress(_e: any): void {
    confirm(doctors[index].technicianId.toString(), doctors[index].technicianName)
  }

  return (
    <RecyclerView.Cell key={index}>
      <View style={[style.recyclerViewCell]}>
        <View style={style.imageBg}>
          <Image style={style.image} source={{ uri: doctors[index].photoUrl }} />
        </View>

        <View style={style.rightContent}>
          <Text style={style.mainText}>{doctors[index].technicianName}</Text>
          <Text style={style.subtext}>{doctors[index].title}</Text>
        </View>
        <LButton style={style.choose} level="small" onPress={onPress} text={'选择'} />
      </View>
      {index === doctors.length - 1 && <View style={{ height: getSafeAreaBottom() }} />}
    </RecyclerView.Cell>
  )
}
