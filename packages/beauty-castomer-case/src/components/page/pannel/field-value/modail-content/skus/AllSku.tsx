import React from 'react'
import { AllSku } from '../../../../../../struct/page/pannel/field-value/modail-content/skus/AllSku'
import {
  Image,
  Text,
  View,
  ViewStyle,
  StyleSheet,
  TouchableWithoutFeedback
} from '@mrn/react-native'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { Picker } from '../../../../../../struct/page/pannel/field-value/Picker'
const styles = StyleSheet.create({
  pannel: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'flex-start'
  },
  mainImg: {
    width: 84,
    height: 84
  },
  content: {
    width: '100%',
    height: 84,
    marginLeft: 9,
    display: 'flex',
    flexDirection: 'column',
    flexShrink: 1,
    justifyContent: 'space-between',
    alignItems: 'flex-start'
  },
  title: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 14,
    fontWeight: '500',
    color: '#222222'
  },
  subTitle: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    color: '#666666'
  },
  buttomStyle: {
    width: '100%'
  },
  btnContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
  price: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center'
  },
  btn: {
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 12,
    paddingRight: 12,
    borderRadius: 20,
    position: 'absolute',
    top: -28
  },
  btnText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  truePriceFrame: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-end'
  },
  truePriceLabel: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    fontWeight: '500',
    color: '#FF4B10'
  },
  truePrice: {
    fontFamily: 'MTfin-bold2.0',
    fontSize: 18,
    color: '#FF4B10'
  },
  discount: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 10,
    color: '#FF4B10'
  },
  disCountFrame: {
    borderStyle: 'solid',
    borderRadius: 3,
    borderColor: '#FF4B10',
    borderWidth: 1,
    padding: 3,
    marginLeft: 3,
    marginRight: 3
  },
  originalPrice: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    textDecorationLine: 'line-through',
    color: '#999999'
  }
})

export const AllSkuView = (props: {
  data: AllSku
  style: ViewStyle
  choose: Function
  picker: Picker
}) => {
  const { param, imgurl, title, btnText, price, id } = props.data
  const { subTitle, discount, originalPrice } = param
  return (
    <View style={[props.style, styles.pannel]}>
      <Image style={styles.mainImg} source={{ uri: imgurl }} />
      <View style={styles.content}>
        <View>
          <Text style={styles.title} numberOfLines={1} ellipsizeMode="tail">
            {title}
          </Text>
          <Text style={styles.subTitle}>{subTitle}</Text>
        </View>
        <View style={styles.buttomStyle}>
          <View style={styles.btnContainer}>
            <TouchableWithoutFeedback
              onPress={() => {
                props.choose({ productId: id }, `商品：${title}`)
              }}
            >
              <LinearGradient
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                colors={['#FF7700', '#FF4B10']}
                style={styles.btn}
              >
                <Text style={styles.btnText}>{btnText}</Text>
              </LinearGradient>
            </TouchableWithoutFeedback>
          </View>
          <View style={styles.price}>
            {!!price && (
              <View style={styles.truePriceFrame}>
                <Text style={styles.truePriceLabel}>¥</Text>
                <Text style={styles.truePrice}>{price}</Text>
              </View>
            )}
            {!!discount && (
              <View style={styles.disCountFrame}>
                <Text style={styles.discount}>{discount}</Text>
              </View>
            )}
            {!!originalPrice && <Text style={styles.originalPrice}>¥{originalPrice}</Text>}
          </View>
        </View>
      </View>
    </View>
  )
}
