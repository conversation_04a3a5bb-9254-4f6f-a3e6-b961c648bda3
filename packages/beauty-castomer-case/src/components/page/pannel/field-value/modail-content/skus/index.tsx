import React from 'react'
import {
  Text,
  TextInput,
  TouchableWithoutFeedback,
  View,
  StyleSheet,
  ScrollView
} from '@mrn/react-native'
import { Skus } from '../../../../../../struct/page/pannel/field-value/modail-content/skus'
import { ConsumedSkuView } from 'components/page/pannel/field-value/modail-content/skus/ConsumedSku'
import Icon from '@max/leez-icon'
import { AllSkuView } from 'components/page/pannel/field-value/modail-content/skus/AllSku'
import { contentHeight } from '../../SelectModail'
import { Picker } from '../../../../../../struct/page/pannel/field-value/Picker'
const tabHeight = 36
const containerHeight = contentHeight - tabHeight
const styles = StyleSheet.create({
  topTabs: {
    display: 'flex',
    flexDirection: 'row',
    height: tabHeight,
    paddingLeft: 12,
    paddingRight: 12,
    justifyContent: 'space-around',
    borderBottomColor: 'rgb(246,246,246)',
    borderBottomWidth: 1,
    borderStyle: 'solid'
  },
  topTab: {
    width: '50%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomWidth: 3.5,
    borderBottomColor: 'white',
    borderStyle: 'solid'
  },
  topSelectedTab: {
    borderBottomColor: '#FFD101'
  },
  topTabText: {
    color: '#222222',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 14
  },
  topTabTextSelected: {
    fontWeight: '500'
  },
  consumedPannel: {
    backgroundColor: 'rgb(246,246,246)',
    height: containerHeight,
    paddingLeft: 12,
    paddingRight: 12,
    display: 'flex',
    flexDirection: 'column',
    flexShrink: 1
  },
  consumedSubtitle: {
    height: 58,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  },
  consumedSubtitleText: {
    fontFamily: 'PingFangSC-Regular',
    color: '#9E9E9E'
  },
  consumedCardMargins: {
    marginBottom: 12
  },
  allPannel: {
    paddingLeft: 12,
    paddingRight: 12,
    height: containerHeight,
    flexShrink: 1,
    display: 'flex',
    flexDirection: 'column'
  },
  search: {
    width: '100%',
    backgroundColor: '#F9F9FC',
    marginTop: 12,
    marginBottom: 12,
    height: 30,
    borderRadius: 9,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    flexShrink: 1
  },
  searchIcon: {
    marginRight: 8,
    marginLeft: 13
  },
  searchText: {
    width: '100%',
    paddingTop: 5,
    paddingBottom: 5
  },
  allskuTabs: {
    display: 'flex',
    flexDirection: 'row',
    borderBottomColor: 'rgb(246,246,246)',
    borderBottomWidth: 1,
    borderStyle: 'solid',
    height: 44
  },
  allskuTab: {
    marginRight: 30,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center'
  },
  allskuTabYellowbar: {
    width: '88%',
    height: 3.5,
    backgroundColor: 'white',
    marginTop: 4.5,
    marginBottom: 4.5,
    borderRadius: 1
  },
  allskuTabYellowbarSelected: {
    backgroundColor: '#FFD101'
  },
  allCardMargins: {
    marginTop: 24
  }
})
const SkusViewer = (props: { data: { skus: Skus; picker: Picker }; confirm: Function }) => {
  const skutypes = props.data.skus.skutypes
  return (
    <View>
      <View style={styles.topTabs}>
        {/*头部Tab*/}
        {skutypes &&
          skutypes.map((x, y) => (
            <TouchableWithoutFeedback key={y} onPress={() => props.data.skus.selectSkuTypes(y)}>
              <View
                style={x.type.selected ? [styles.topTab, styles.topSelectedTab] : styles.topTab}
              >
                <Text
                  style={
                    x.type.selected
                      ? [styles.topTabText, styles.topTabTextSelected]
                      : styles.topTabText
                  }
                >
                  {x.type.name}
                </Text>
              </View>
            </TouchableWithoutFeedback>
          ))}
      </View>
      {skutypes &&
        skutypes.map((x, k) => {
          if (x.type.selected) {
            switch (x.type.name) {
              case '全部商品':
                return (
                  <View key={k} style={styles.allPannel}>
                    <View style={styles.search}>
                      {/*全部商品搜索*/}

                      <Icon
                        style={styles.searchIcon}
                        name="sousuo"
                        nopaddingMode={false}
                        color="rgba(0,0,0,0.2)"
                        type="body2"
                      />
                      <TextInput
                        style={styles.searchText}
                        defaultValue={x.searchKeyWord}
                        onChangeText={text => x.setSearchKeyWord(text)}
                        placeholder={'搜索商品名称/品牌/功效'}
                        returnKeyType="search"
                        onSubmitEditing={() => {
                          props.data.skus.searchSkuByKeyWord()
                        }}
                      />
                    </View>
                    {x.skuTypes && x.skuTypes.length > 1 && (
                      <View style={styles.allskuTabs}>
                        <ScrollView
                          showsHorizontalScrollIndicator={false}
                          horizontal={true}
                          style={{ flex: 1 }}
                          alwaysBounceHorizontal={false}
                        >
                          {/*全部商品Tab*/}
                          {x.skuTypes &&
                            x.skuTypes.length > 0 &&
                            x.skuTypes.map((z, a) => (
                              <TouchableWithoutFeedback key={a} onPress={() => x.selectSkuTypes(a)}>
                                <View style={styles.allskuTab}>
                                  <Text
                                    style={
                                      z.type.selected
                                        ? [styles.topTabText, styles.topTabTextSelected]
                                        : styles.topTabText
                                    }
                                  >
                                    {z.type.name}
                                  </Text>
                                  <View
                                    style={
                                      z.type.selected
                                        ? [
                                            styles.allskuTabYellowbar,
                                            styles.allskuTabYellowbarSelected
                                          ]
                                        : styles.allskuTabYellowbar
                                    }
                                  />
                                </View>
                              </TouchableWithoutFeedback>
                            ))}
                        </ScrollView>
                      </View>
                    )}
                    <ScrollView
                      scrollEventThrottle={64}
                      showsVerticalScrollIndicator={false}
                      alwaysBounceVertical={false}
                      style={{ flex: 1 }}
                    >
                      {/*全部商品卡片*/}
                      {x.skuTypes &&
                        x.skuTypes.length > 0 &&
                        x.skuTypes.map(o => {
                          if (o.type.selected) {
                            return o.skus.map((v, w) => (
                              <AllSkuView
                                style={styles.allCardMargins}
                                data={v}
                                key={w}
                                choose={props.confirm}
                                picker={props.data.picker}
                              />
                            ))
                          }
                        })}
                      <View style={{ height: 50 }} />
                    </ScrollView>
                  </View>
                )

              case '购买过':
                return (
                  <View key={k} style={styles.consumedPannel}>
                    <View style={styles.consumedSubtitle}>
                      <Text style={styles.consumedSubtitleText}>
                        仅展示近半年医美预付商品已核销订单
                      </Text>
                    </View>
                    <ScrollView
                      scrollEventThrottle={64}
                      showsVerticalScrollIndicator={false}
                      alwaysBounceVertical={false}
                      style={{ flex: 1 }}
                    >
                      {x.skus.map((i, j) => (
                        <ConsumedSkuView
                          style={styles.consumedCardMargins}
                          data={i}
                          key={j}
                          choose={props.confirm}
                          picker={props.data.picker}
                        />
                      ))}
                      <View style={{ height: 50 }} />
                    </ScrollView>
                  </View>
                )
            }
          }
        })}
    </View>
  )
}

export default SkusViewer
