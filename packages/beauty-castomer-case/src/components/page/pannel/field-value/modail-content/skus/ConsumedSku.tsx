import React from 'react'
import { ConsumedSku } from '../../../../../../struct/page/pannel/field-value/modail-content/skus/ConsumedSku'
import {
  Image,
  Text,
  View,
  StyleSheet,
  ViewStyle,
  TouchableWithoutFeedback
} from '@mrn/react-native'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { Picker } from '../../../../../../struct/page/pannel/field-value/Picker'
const styles = StyleSheet.create({
  pannel: {
    backgroundColor: '#FFFFFF',
    paddingTop: 12,
    paddingBottom: 15,
    paddingLeft: 12,
    paddingRight: 12,
    borderRadius: 12
  },
  topTitle: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12
  },
  store: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    flexShrink: 1
  },
  storeLogo: {
    width: 12,
    height: 12
  },
  storeName: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 15,
    fontWeight: '500',
    color: '#222222',
    lineHeight: 20,
    marginLeft: 3,
    marginRight: 10
  },
  consumStatusText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 13,
    color: '#777777'
  },
  content: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexShrink: 1
  },
  mainImg: {
    width: 70,
    height: 70
  },
  messagePannel: {
    marginLeft: 10,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    flexShrink: 1,
    height: 70
  },
  title: {
    color: '#222222',
    fontWeight: '500',
    fontSize: 14,
    fontFamily: 'PingFangSC-Regular'
  },
  subTitle: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 11,
    lineHeight: 13,
    color: '#666666',
    marginTop: 6
  },
  buttomStyle: {
    width: '100%',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  price: {
    color: '#222222',
    fontSize: 15,
    lineHeight: 18,
    fontFamily: 'MTfin-Regular3.0'
  },
  btn: {
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 12,
    paddingRight: 12,
    borderRadius: 20
  },
  btnText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF'
  }
})
export const ConsumedSkuView = (props: {
  data: ConsumedSku
  style?: ViewStyle
  choose: Function
  picker: Picker
}) => {
  const { imgurl, param, title, btnText, price, id, buttonStatus } = props.data
  const { consumStatus, storeName, consumTime, productId } = param
  return (
    <View style={[styles.pannel, props.style]}>
      <View style={styles.topTitle}>
        <View style={styles.store}>
          <Image
            style={styles.storeLogo}
            source={{
              uri: 'https://p0.meituan.net/travelcube/83bf16a135896e4cb896e561bead8b61502.png'
            }}
          />
          <Text numberOfLines={1} ellipsizeMode="tail" style={styles.storeName}>
            {storeName}
          </Text>
        </View>
        <View>
          <Text style={styles.consumStatusText}>{consumStatus}</Text>
        </View>
      </View>
      <View style={styles.content}>
        <Image style={styles.mainImg} source={{ uri: imgurl }} />
        <View style={styles.messagePannel}>
          <View>
            <Text style={styles.title} numberOfLines={1} ellipsizeMode="tail">
              {title}
            </Text>
            <Text style={styles.subTitle}>{consumTime}</Text>
          </View>
          <View style={styles.buttomStyle}>
            {price && (
              <View>
                <Text style={styles.price}>¥{price}</Text>
              </View>
            )}
            <TouchableWithoutFeedback
              onPress={() => {
                if (buttonStatus === 2) return
                props.choose({ productId: productId, orderId: id }, `订单：总价¥${price}${title}`)
              }}
            >
              <LinearGradient
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                colors={buttonStatus === 2 ? ['#CCCCCC', '#CCCCCC'] : ['#FF7700', '#FF4B10']}
                style={styles.btn}
              >
                <Text style={styles.btnText}>{btnText}</Text>
              </LinearGradient>
            </TouchableWithoutFeedback>
          </View>
        </View>
      </View>
    </View>
  )
}
