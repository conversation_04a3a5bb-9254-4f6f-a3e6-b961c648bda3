import RecyclerView from '@hfe/max-recyclerview'
import React, { JSX } from 'react'
import { getSafeAreaBottom } from '@mrn/mrn-gc-base'
import { Text, TextInput, View, StyleSheet, TouchableOpacity } from '@mrn/react-native'
import Icon from '@max/leez-icon'
import { Shop, ShopObj } from 'src/struct/page/pannel/field-value/modail-content/shops/shop'
import { Picker } from 'src/struct/page/pannel/field-value/Picker'

const style = StyleSheet.create({
  recyclerViewCell: {
    height: 60,
    backgroundColor: '#FFFFFF',
    justifyContent: 'space-between',
    flexGrow: 1,
    marginTop: 6,
    marginBottom: 6,
    paddingTop: 12,
    paddingBottom: 12
  },
  mainText: {
    height: 17,
    color: '#333333',
    fontSize: 14
  },
  subtext: {
    color: '#999999',
    fontWeight: '500',
    fontSize: 12,
    letterSpacing: 0,
    textAlign: 'left',
    height: 14,
    marginTop: 3
  },
  recyclerView: {
    backgroundColor: '#FFFFFF',
    flexGrow: 1
  },

  recyclerViewHeader: { height: 0, width: 150 },

  allPannel: {
    paddingLeft: 12,
    paddingRight: 12,
    flexShrink: 1,
    display: 'flex',
    flexDirection: 'column'
  },
  search: {
    width: '100%',
    backgroundColor: '#F9F9FC',
    height: 30,
    borderRadius: 9,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    flexShrink: 1
  },
  searchIcon: {
    marginRight: 8,
    marginLeft: 13
  },
  searchText: {
    width: '100%',
    paddingTop: 5,
    paddingBottom: 5
  },
  line: { backgroundColor: '#F0F0F0', height: 0.5 }
})

/**
 * 关联门店列表
 */

export default function ShopList(_props: {
  data: { shops: ShopObj; picker: Picker }
  confirm: Function
}) {
  const shops = _props.data.shops.shops
  return (
    <View style={{ flex: 1, padding: 20 }}>
      <View style={{ flexDirection: 'row' }}>
        <Text style={{ alignSelf: 'center' }}>上海</Text>
        <View style={style.allPannel}>
          <View style={style.search}>
            {/*全部商品搜索*/}
            <Icon
              style={style.searchIcon}
              name="sousuo"
              nopaddingMode={false}
              color="rgba(0,0,0,0.2)"
              type="body2"
            />
            <TextInput
              style={style.searchText}
              placeholder={'搜索地点'}
              returnKeyType="search"
              defaultValue={_props.data.shops.keyWord}
              onChangeText={txt => _props.data.shops.setKeyWord(txt)}
              onSubmitEditing={() => _props.data.shops.requestShops()}
            />
          </View>
        </View>
      </View>

      <Text style={style.subtext}>可能想关联的商户</Text>
      <RecyclerView
        style={style.recyclerView}
        stickyHeaderIndices={[0]}
        showsVerticalScrollIndicator={false}
      >
        <RecyclerView.Header>
          <View style={style.recyclerViewHeader} />
        </RecyclerView.Header>
        {shops &&
          shops.map((item, index) => {
            return ShopCell(index, shops, _props.confirm)
          })}
      </RecyclerView>
    </View>
  )
}
function ShopCell(index: number, list: Shop[], confirm: Function): JSX.Element {
  function onPress(_e: any): void {
    confirm(list[index].shopId.toString(), list[index].shopName)
  }

  return (
    <RecyclerView.Cell key={index}>
      <TouchableOpacity onPress={onPress}>
        <View style={[style.recyclerViewCell]}>
          <Text style={style.mainText}>{list[index].shopName}</Text>
          <Text style={style.subtext}>{list[index].cityName}</Text>
        </View>
      </TouchableOpacity>
      {index !== list.length - 1 && <View style={style.line} />}
      {index === list.length - 1 && <View style={{ height: getSafeAreaBottom() }} />}
    </RecyclerView.Cell>
  )
}
