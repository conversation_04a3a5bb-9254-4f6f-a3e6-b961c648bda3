import { FieldType, Pannel } from '../../../../struct/page/pannel'
import { PickerView } from 'components/page/pannel/field-value/Picker'
import { ViewStyle } from '@mrn/react-native'
export const FieldView = (props: { data: Pannel; style?: ViewStyle }) => {
  let fieldViewer
  switch (props.data.param.fieldValue.type) {
    case FieldType.picker:
      fieldViewer = PickerView
      break
  }
  const param = { data: props.data.fieldValue, style: props.style }
  return fieldViewer && fieldViewer(param)
}
