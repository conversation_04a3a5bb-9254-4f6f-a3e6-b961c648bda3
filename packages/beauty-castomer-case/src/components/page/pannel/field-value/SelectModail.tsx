import React, { Suspense, lazy } from 'react'
import { Platform } from '@mrn/react-native'
import TopView from '@max/leez-top-view'
import ModalBaseContainer from '@max/leez-modal-base-container'
import {
  Image,
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
  KeyboardAvoidingView
} from '@mrn/react-native'
import SelectModail from '../../../../struct/page/pannel/field-value/SelectModail'
import { Page } from '../../../../struct/page'
const pannel = 700
const title = 51
const contentHeight = pannel - title
export { contentHeight }
const styles = StyleSheet.create({
  modailPannel: {
    backgroundColor: '#FFFFFF',
    width: '100%',
    height: pannel,
    borderTopRightRadius: 15,
    borderTopLeftRadius: 15
  },
  title: {
    height: title,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center'
  },
  titleText: {
    fontSize: 18,
    fontFamily: 'PingFangSC-Regular',
    color: '#222222',
    fontWeight: '500'
  },
  close: {
    width: 22,
    height: 22,
    borderRadius: 11,
    backgroundColor: '#F4F4F4',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    right: 15
  },
  closeIcon: {
    width: 9,
    height: 9
  },
  content: {
    height: contentHeight
  }
})
export const SelectModailView = () => {
  const data = SelectModail.getInstance()
  const devEnv = Page.getInstance().devEnv
  let LazyComponent =
    devEnv && data?.param?.content?.viewer && lazy(() => data.param.content.viewer)

  return (
    <TopView>
      {data && (
        <ModalBaseContainer
          visible={data.visible}
          useRNModal={false}
          type="slide"
          slideFrom="bottom"
          onMaskPress={() => data.toggle()}
        >
          <KeyboardAvoidingView
            behavior={(Platform.OS === 'android' && 'height') || ''}
            keyboardVerticalOffset={(Platform.OS === 'android' && 125) || 0}
          >
            <View style={styles.modailPannel}>
              <View style={styles.title}>
                <Text style={styles.titleText}>{data.param.title}</Text>
                <TouchableWithoutFeedback onPress={() => data.toggle()}>
                  <View style={styles.close}>
                    <Image
                      style={styles.closeIcon}
                      source={{
                        uri: 'https://p0.meituan.net/travelcube/c363a0a2a2268ba2f179289b50bd7006291.png'
                      }}
                    />
                  </View>
                </TouchableWithoutFeedback>
              </View>
              <View style={styles.content}>
                {devEnv
                  ? LazyComponent && (
                      <Suspense fallback={<></>}>
                        <LazyComponent
                          data={data.param.content.param}
                          confirm={data.handleCallback.bind(data)}
                        />
                      </Suspense>
                    )
                  : data.param.content.viewer &&
                    data.param.content.viewer({
                      data: data.param.content.param,
                      confirm: data.handleCallback.bind(data)
                    })}
              </View>
            </View>
          </KeyboardAvoidingView>
        </ModalBaseContainer>
      )}
    </TopView>
  )
}
