import { StyleSheet, Text, View, ViewStyle } from '@mrn/react-native'
import React from 'react'
import Icon from '@max/leez-icon'
import { Picker } from '../../../../struct/page/pannel/field-value/Picker'
const styles = StyleSheet.create({
  Selections: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flexShrink: 1
  },
  SelectionsText: {
    fontFamily: 'PingFangSC-Regular',
    fontSize: 13,
    color: '#999999'
  },
  SelectionsIcon: {
    fontSize: 15,
    color: '#CCCCCC'
  },
  Selected: {
    color: '#111111'
  }
})
//todo 这个地方是回掉函数，和上个页面交互
export const PickerView = (props: { data: Picker; style: ViewStyle }) => {
  return (
    <View style={[styles.Selections, props.style]}>
      <Text
        style={props.data.label ? [styles.SelectionsText, styles.Selected] : styles.SelectionsText}
      >
        {props.data.label ? !props.data.param.transport && props.data.label : '请选择'}
      </Text>
      <Icon
        style={props.data.label ? [styles.SelectionsIcon, styles.Selected] : styles.SelectionsIcon}
        name="jiantouxianyou"
        type="title1"
        className="demo-icon-icon"
      />
    </View>
  )
}
