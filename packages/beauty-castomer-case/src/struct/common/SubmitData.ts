import { isValidations, SubmitKey, Validations } from './SubmitKey'
import { Page } from '../page'

export class SubmitData {
  private static submitData: SubmitData | undefined
  submitkeys: SubmitKey[] = []

  private constructor(submitkeys?: SubmitKey[]) {
    submitkeys && (this.submitkeys = submitkeys)
  }
  public static getInstance(submitkeys?: SubmitKey[]) {
    if (this.submitData === void 0) {
      this.submitData = new SubmitData(submitkeys)
    }
    return this.submitData
  }
  public clear() {
    this.submitkeys = []
  }

  public addKey(submitKey: SubmitKey) {
    this.submitkeys.push(submitKey)
  }
  public addKeys(submitkeys: SubmitKey[]) {
    Array.prototype.push.apply(this.submitkeys, submitkeys)
  }
  public getJsonValueData() {
    const res = {}
    this.submitkeys?.forEach(x => {
      //过滤未渲染组件和未渲染但需要提交组件
      if (x.visible || x.unvisibleKeepValue) {
        if (res[x.key]) {
          //如果key已经存在
          if (Array.isArray(res[x.key])) {
            //如果已经是多个值直接追加
            res[x.key].push(x.value)
          } else {
            //如果是单个值转成多个值
            res[x.key] = [res[x.key], x.value]
          }
        } else {
          //key不存在
          if (x.defaultArray) {
            //如果提交对象默认为array
            res[x.key] = [x.value]
          } else {
            //非默认array直接转换为值
            res[x.key] = x.value
          }
        }
      }
    })
    return res
  }
  public parseValueTree() {
    const jsonValueData = this.getJsonValueData()
    for (const key in jsonValueData) {
      if (key.includes('.')) {
        const jsonValueDatum = jsonValueData[key]
        const strings = key.split('.')
        jsonValueData[`${strings[0]}`][`${strings[1]}`] = jsonValueDatum
        delete jsonValueData[`${key}`]
      }
    }
    return jsonValueData
  }
  public validate(submit?: boolean) {
    const jsonValueData = this.getJsonValueData()
    const validatedResTemp = {}
    const that = this
    let totalPass = true
    this.submitkeys.forEach(x => {
      if (validatedResTemp[x.key] === undefined && (x.visible || x.unvisibleKeepValue)) {
        //如果该submitkey的验证结果未计算则进入计算逻辑，已经计算过的不进行重复计算
        let resTemp = false
        if (typeof x.validator === 'function') {
          //如果验证方式为自定义方法，执行自定义方法
          resTemp = x.validator(jsonValueData[x.key], submit)
        } else if (x.validator === undefined) {
          //如果没有定义验证方法，直接判断验证通过
          resTemp = true
        } else if (isValidations(x.validator)) {
          //如果是通用验证方式，调用通用验证器验证
          resTemp = that.commonValidator(x.validator, jsonValueData[x.key])
        }
        validatedResTemp[x.key] = resTemp
        if (!resTemp) {
          totalPass = false
        }
      }
    })
    const footer = Page.getInstance().footer
    if (!footer.agreementStatus) {
      totalPass = false
    }
    footer.updateValidateState(totalPass)
    return totalPass
  }
  private commonValidator(validator: Validations, inParam: any): boolean {
    let result = false
    const notNull = (inParams: any) => !!inParams
    switch (validator) {
      case Validations.notNull:
      case Validations.require:
        result = notNull(inParam)
        break
      case Validations.none:
      case Validations.pass:
        result = true
        break
      default:
        const _never: never = validator
        break
    }
    return result
  }
  public deleteSubmitKeyById(id: string) {
    this.submitkeys = this.submitkeys.filter(x => x.id !== id)
  }

  public submitKeyEcho(echoData) {
    this.submitkeys.forEach(x => echoData[x.key] && (x.value = echoData[x.key]))
  }
  public getKeyValue(keyName: string | string[]): SubmitKey[] {
    return this.submitkeys.filter(x =>
      typeof keyName === 'string' ? x.key === keyName : keyName.includes(x.key)
    )
  }
}
