import { SubmitData } from './SubmitData'
import { generateUUID } from './UUID'
export interface SubmitKeyParam {
  key: string
  value: any
  visible?: boolean //是否渲染
  unvisibleKeepValue?: boolean //在未渲染的情况下，是否需要提交值
  validator?: Validations | ((value: any | undefined, submit: boolean) => boolean)
  disabled?: boolean
  defaultArray?: boolean
}
export enum Validations {
  'notNull',
  'require',
  'none',
  'pass'
}
export function isValidations(inParam: any): inParam is Validations {
  if (!Object.values) {
    Object.values = function (obj) {
      return Object.keys(obj).map(function (key) {
        return obj[key]
      })
    }
  }
  return Object.values(Validations).includes(inParam)
}
export abstract class SubmitKey {
  id: string
  key: string | undefined
  value: any | string | undefined
  visible: boolean
  unvisibleKeepValue: boolean
  validator?: Validations | ((value: any | undefined, submit?: boolean) => boolean)
  disabled: boolean
  defaultArray?: boolean
  constructor(inParam: SubmitKeyParam) {
    const { key, value, validator, visible, unvisibleKeepValue, disabled, defaultArray } = inParam
    this.key = key
    this.value = value
    validator && (this.validator = validator)
    this.visible = visible === void 0 ? true : visible //不填参数时默认渲染
    this.unvisibleKeepValue = unvisibleKeepValue === void 0 ? false : unvisibleKeepValue //不填参数时默认未渲染时不提交该值
    this.id = generateUUID()
    SubmitData.getInstance().addKey(this)
    this.disabled = disabled === undefined ? false : disabled
    this.defaultArray = defaultArray
  }
  deleteSubmitkey() {
    SubmitData.getInstance().deleteSubmitKeyById(this.id)
  }
  setVisible(visible: boolean) {
    this.visible = visible
  }
}
