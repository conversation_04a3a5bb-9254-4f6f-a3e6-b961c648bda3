import Loading from '@max/leez-loading'
export default class LoadingManager {
  private static instance: LoadingManager | undefined
  private loadState: any
  private constructor() {}

  public static getInstance() {
    if (!this.instance) {
      this.instance = new LoadingManager()
    }
    return this.instance
  }
  public show() {
    this.loadState = Loading.open()
    setTimeout(() => this.hide(), 5000)
  }
  public hide() {
    this.loadState.close()
  }
}
