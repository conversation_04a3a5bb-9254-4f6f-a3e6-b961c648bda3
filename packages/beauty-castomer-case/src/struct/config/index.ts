import { FieldType } from '../page/pannel'
import { Page, pageParam } from '../page'

import InputView from 'components/page/pannel/content/Input'
import TextAreaView from 'components/page/pannel/content/TextArea'
import ImageUploadView from 'components/page/pannel/content/image-upload'
import { openUrl } from '@mrn/mrn-utils'
import { Validations } from '../common/SubmitKey'
import ImageUpload, { ImageStyle } from '../page/pannel/content/image-upload'
import TextArea from '../page/pannel/content/TextArea'
import Input from '../page/pannel/content/Input'
import { Picker } from '../page/pannel/field-value/Picker'
import { Skus, transformInitData } from '../page/pannel/field-value/modail-content/skus'
import { SubmitData } from '../common/SubmitData'
import { SkuType } from '../page/pannel/field-value/modail-content/skus/SkuType'
import { ConsumedSku } from '../page/pannel/field-value/modail-content/skus/ConsumedSku'
import SkusViewer from 'components/page/pannel/field-value/modail-content/skus'
import ImageUploadCostumContent from 'components/page/pannel/content/image-upload/ImageUploadCostumContent'
import DoctorList from 'components/page/pannel/field-value/modail-content/doctors'
import Toast from '@max/leez-toast'
import { getDoctors } from '../page/pannel/field-value/modail-content/doctors/dotctor'
import ShopList from 'components/page/pannel/field-value/modail-content/shops'
import { getShopsByParams, ShopObj } from '../page/pannel/field-value/modail-content/shops/shop'
import PickerManager from '../page/pannel/field-value/PickerManager'
import LoadingManager from '../common/LoadingManager'
import BeautyCastomerCaseRequest from '../../api/request/BeautyCastomerCaseRequest'
import RequestObj from '../../api/request/RequestObj'
export function getConfig(pageRouterClose: Function, devEnv: boolean): pageParam {
  /*
  这里是用来实现弹窗组件懒加载的，
  当在开发环境下process.env.NODE_ENV 的值是 development
  在在测试和生产环境中可以取到 production 暂时不作取值判断
  devEnv为true时启用弹窗组件懒加载，在开发环境中有热部署功能
  可以在这里设置process.env.NODE_ENV为任意值如
  process.env.NODE_ENV = 'test'
  使devEnv为false时，启动弹窗内组件预加载
   */
  return {
    devEnv,
    titleParam: {
      bigTitle: '体验报告投稿',
      leftTitle: {
        title: '取消',
        behavior: () => {
          pageRouterClose()
        }
      },
      rightTitle: {
        title: '说明',
        behavior: () => {
          openUrl('https://cube.meituan.com/cube/block/31e393e4c74d/227147/index.html')
        }
      }
    },
    pannelParams: [
      {
        title: '主建',
        content: {
          moduleName: 'input',
          moduleParam: {
            key: 'itemId',
            value: void 0,
            deleteAble: false,
            editAble: false,
            visible: false,
            unvisibleKeepValue: true
          }
        }
      },
      {
        title: '标题',
        content: {
          moduleName: 'input',
          moduleParam: {
            key: 'title',
            value: void 0,
            validator: Validations.require,
            placeholder: '建议格式：项目名称-术后xx天-效果'
          }
        }
      },
      {
        title: '图片',
        content: {
          moduleName: 'image-upload',
          moduleParam: {
            key: 'pics',
            imageKey: 'url',
            value: void 0,
            deleteAble: true,
            editAble: true,
            mainTitle: '上传图片',
            validator: (values, submit) => {
              function tagValidate(value) {
                let res = true
                if (submit && value && !value.tagName) {
                  Toast.open({ title: '图片未打标，请添加正确标签后提交！' })
                  res = false
                }
                return res
              }
              return Array.isArray(values)
                ? values.every(tagValidate) && values.length > 0
                : tagValidate(values) && !!values
            },
            header: ['请上传能够表现医美效果的实拍图片，并添加对应标签，最多20张'],
            limitSize: 20,
            style: ImageStyle.tag,
            defaultArray: true
          }
        }
      },
      {
        title: '正文',
        notice: '请勿在描述中提及医生和机构名称，以免广告嫌疑',
        content: {
          moduleName: 'text-area',
          moduleParam: {
            key: 'content',
            value: void 0,
            validator: Validations.require,
            placeholder: '分享你的医美体验 \n（建议：术前诉求、年龄、医美过程、效果、个人感受等）',
            customized: {
              style: {
                height: 130
              }
            }
          }
        }
      },
      {
        title: '关联门店',
        fieldValue: {
          type: FieldType.picker,
          params: {
            key: 'shopId',
            value: void 0,
            title: '选择门店',
            validator: value => {
              Page.getInstance().lockDoctorAndStore(!value)
              return !!value
            },
            viewer: devEnv
              ? import('components/page/pannel/field-value/modail-content/shops')
              : ShopList,
            paramPaser: shopSelect,
            setValueCallBack: () => {
              PickerManager.getInstance().clearPlckerValue(['doctorId', 'reference'])
            }
          }
        }
      },
      {
        title: '合作医生',
        fieldValue: {
          type: FieldType.picker,
          params: {
            key: 'doctorId',
            value: void 0,
            title: '选择医师',
            // validator: Validations.require,
            viewer: devEnv
              ? import('components/page/pannel/field-value/modail-content/doctors')
              : DoctorList,
            disabled: true,
            paramPaser: doctorSelect
          }
        }
      },
      {
        title: '关联商品',
        fieldValue: {
          type: FieldType.picker,
          params: {
            key: 'reference',
            value: void 0,
            title: '选择商品',
            validator: Validations.require,
            viewer: devEnv
              ? import('components/page/pannel/field-value/modail-content/skus')
              : SkusViewer,
            paramPaser: skuDataResolver,
            transport: true,
            disabled: true,
            setValueCallBack: (picker, value) => {
              const keyValue = SubmitData.getInstance().getKeyValue('reference.certPic')
              let visible
              if (value.orderId) {
                visible = false
              } else {
                visible = true
              }
              keyValue &&
                Array.isArray(keyValue) &&
                keyValue.length > 0 &&
                keyValue[0].setVisible(visible)
            }
          }
        }
      },
      {
        title: '消费凭证',
        content: {
          moduleName: 'image-upload',
          moduleParam: {
            key: 'reference.certPic',
            value: void 0,
            validator: values => (Array.isArray(values) ? values.length > 0 : !!values),
            deleteAble: true,
            editAble: true,
            mainTitle: '上传消费凭证',
            limitSize: 5,
            visible: false,
            rightContent: ImageUploadCostumContent,
            defaultArray: true
          }
        }
      },
      {
        title: '请上传与关联门店签署的授权书',
        notice: '信息保密，仅用于授权确认',
        content: {
          moduleName: 'image-upload',
          moduleParam: {
            key: 'protocolPic',
            value: void 0,
            validator: values => (Array.isArray(values) ? values.length > 0 : !!values),
            deleteAble: true,
            editAble: true,
            mainTitle: '上传授权书照片',
            footer: ['图片格式JPG/JPEG/PNG，每张不超过2M；授权内容需完整'],
            limitSize: 5,
            defaultArray: true
          }
        }
      }
    ],
    footerParam: {
      agreements: [
        {
          title: '体验报告投稿功能协议',
          content: `
您好！

欢迎您签署《体验报告投稿功能协议》以使用体验报告投稿功能！

提示条款

各条款标题仅为帮助您理解该条款表达的主旨之用，不影响或限制本协议条款的含义或解释。为维护您自身权益，请您仔细阅读各条款具体表述。
【审慎阅读】您在签署本协议之前，请您务必审慎阅读、充分理解协议中的全部条款内容，特别是包括：免除或者限制责任的条款、违约责任、争议解决等以粗体标注的条款。
【签约使用】一旦您通过在线点击“同意”（或其他表示接受的按钮）协议，即表示您已充分阅读、理解并接受本协议的全部内容，并与我们达成一致，本协议即于您和我们之间产生法律约束力。如您不同意本协议或其中任何条款约定，请您立即停止提交相关材料或使用体验报告投稿功能。如您继续的，即视为您已阅读并同意本协议。

一、功能介绍
体验报告投稿功能系由我们提供的供您上传、展示及分享真实项目体验的功能。您可通过相应入口进入投稿页面，编辑体验报告内容、上传相关图文经我们审核通过后以“医美体验报告用户投稿”账号或其他由我们命名的账号（以下统称为“我们的账号”）在美团及/或大众点评平台上发布。

二、使用承诺
1.\t您承诺您所上传的内容及图片务必真实、合法、有效，不存在任何过期、失效、伪造的情形。
2.\t您承诺对您所上传的全部内容（包括但不限于文字、图片、视频及/或其他信息）拥有相应合法的权利，不含有违反国家法律、行政法规等内容，不侵犯第三方的合法权益，包括但不限于知识产权、肖像权、姓名权、隐私权及其他人身权及/或财产权等。否则，我们有权依法或依本协议要求您进行修改。如您未能及时修改的，我们有权不予发布或下线、删除您所上传的全部或部分内容，我们无需因此承担责任。您清楚并明确，您上传的内容及您使用体验报告投稿功能的行为属于您的个人行为，由此产生的任何侵权打了个纠纷或诉讼应由您独立承担，与我们无关，如因您上传的内容产生不利影响或给我们带来损失的，我们有权要求您赔偿因此给我们造成的损失。
3.\t您承诺，如您的所上传的内容涉及第三方的商业信息或个人信息的，您已向该等第三方获得了您使用该等第三方信息上传至体验报告的充分授权，该等授权应当包括授权您向我们提交其信息、授权我们有权保存其信息、授权我们通过我们的账号进行发布或通过其他方式进行使用的所有授权。
4.\t您承诺，您对您通过使用体验报告投稿功能所了解、获得的所有信息负有保密义务，该保密义务持续有效，不随您停止使用体验报告投稿功能或体验报告投稿功能的下线而失效。

三、违约责任
1.\t您理解并同意，我们可通过发布相关规则的形式对本协议进行修改或对本协议未尽事宜进行进一步补充以及约定违约认定程序、标准和责任。
2.\t您违反本协议及规则的，我们有权独立判断并视情况采取不予发布内容、警示、限制功能、暂停或者停止向您提供服务、追究违约责任等处置措施。如您严重违约的，我们有权视您的违约严重程度决定是否终止与您的所有合作。
对涉嫌违反法律法规、违法犯罪的行为，我们并将保存有关记录或证据，依法向有关主管部门报告，配合有关主管部门调查。
3.\t除本协议另行约定外，因您违反本协议，引起第三方投诉或诉讼索赔等的，您应当自行处理并承担全部可能由此引起的法律责任。因您的违法或违约行为导致美团及其关联公司向任何第三方赔偿或遭受国家机关处罚的，您还应足额赔偿我们及关联公司因此遭受的损失（包括自身的直接经济损失、商誉损失及对外支付的赔偿金、和解款、律师费、诉讼费等间接经济损失，下同）。

四、特别约定
1.\t您理解并同意，我们可能会基于服务风险、运营管理需求，或其他善意原因及事由（包括但不限于不可抗力、产品或服务优化、升级、维护、设备检修、疫情影响等）提前中止或终止体验报告投稿功能。此种情况下，我们将尽可能提前进行公示，我们的公示行为即视为有效通知，我们无需因此承担责任。
2.\t您不得通过任何方式干预或影响我们的对于体验报告内容的审核以及发布，例如向美团或其关联公司的员工及其利害关系人等直接、间接提供物质、精神上的不正当利益，包括但不限于现金、实物、现金等价物、劳务、旅游等，或与美团产生利益冲突但未提前申告，则可视为您存在商业贿赂行为。
3.\t对于您上传、提交的文字、图片、视频、音频、链接等信息或知识产权，在法律规定的保护期限内，您授予我们及关联公司可在全球范围内、免费、非独家、可转授权地使用，您授权我们及关联公司一项全球范围内、免费、非独家的侵权行为进行调查、技术防护、维权、索赔、诉讼，使得我们及其关联公司可以自己的名义对侵犯您的知识产权等权利的行为进行维权和救济。
4.\t您同意我们及其关联公司存储、使用、复制、修订、编辑、发布、展示、翻译、分发您的知识产权素材或制作其派生作品，并以已知或日后开发的形式、媒体或技术将上述信息纳入其它作品内。

五、隐私政策
您使用体验报告投稿功能，即表示同意我们按照在美团及/或大众点评上公示的《隐私政策》来处理您的个人信息。

六、责任限制
1.\t您理解并同意，我们可能会基于服务风险、运营管理需求，或其他善意原因及事由（包括但不限于不可抗力、产品或服务优化、升级、维护、设备检修、疫情影响等）提前中止或终止全部或部分功能。此种情况下，我们将尽可能提前进行公示，我们的公示行为即视为有效通知，我们无需因此承担责任。
2.\t如您通过不正当、非常规途径使用体验报告投稿功能，包含但不限于如下：非合法途径获得账号、非法侵入系统等行为，所造成的后果由用户自行承担，我们保有追责权利。
3.\t如您因对我们提供的体验报告投稿功能和本协议存在理解偏差而造成的后果，我们无需承担相应责任。

七、协议变更通知
1.\t为给您提供更好的服务或因国家法律法规、政策调整、技术条件、产品功能等变化需要，我们会适时以规则、规范、解读、公告、通知、实施细则、产品介绍说明、FAQ等方式对本协议进行修订，修订内容构成本协议的补充协议，您有权提出不同意见。补充协议更新后，我们会在美团平台公布更新版本或以其他适当的方式提醒您更新的内容，以便您及时了解本协议的最新补充，您也可以在相关页面查阅最新的补充协议。如您不同意前述补充协议，您可向我们提出请您在前述补充协议生效前停止使用体验报告投稿功能。如您继续使用体验报告投稿功能，即表示您已同意接受并自愿遵守补充协议。
2.\t我们会选择采用书面、邮件、短信、站内信、客户端等任一种方式向您发出本协议相关的通知，通知到达后即时生效。您知悉、理解并同意：我们发送的通知含有重要内容，您应及时查阅。您知悉并认可，您不得因未打开、未注意到该等信息等您自身原因，否定相关通知的效力。

八、法律适用、管辖与其他
1.\t本协议的成立、有效性、解释、履行、签署、修订和终止以及争议的解决均应适用中华人民共和国法律（仅为本协议之目的，不包括香港特别行政区、澳门特别行政区和台湾地区）。
2.\t因本协议引起的或与本协议有关的任何争议，双方应协商解决。协商不成的，任何一方均可将争议提交至北京市朝阳区人民法院。

`
        }
      ],
      submitBtn: {
        title: '提交投稿',
        linkUrl:
          'dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-beauty-castomer-case&mrn_component=myCastomerCase',
        navigation: pageRouterClose
      }
    },
    pannelModules: [
      {
        moduleName: 'input',
        moduleObj: Input,
        view: InputView
      },
      {
        moduleName: 'text-area',
        moduleObj: TextArea,
        view: TextAreaView
      },
      {
        moduleName: 'image-upload',
        moduleObj: ImageUpload,
        view: ImageUploadView
      }
    ]
  }
}

export function skuDataResolver(param: Picker, callback: Function) {
  const skutypes: SkuType[] = []
  const promises: Promise<any>[] = []
  promises.push(BeautyCastomerCaseRequest.requestAllSku())
  promises.push(BeautyCastomerCaseRequest.requestConsomeSku())
  const instance = LoadingManager.getInstance()
  instance.show()
  Promise.all(promises).then(result => {
    const [allData, consomedData] = result
    skutypes.push(
      new SkuType(
        {
          name: '全部商品',
          selected: true
        },
        undefined,
        allData.msg && allData.msg.products && transformInitData(allData.msg.products)
      )
    )
    skutypes.push(
      new SkuType(
        {
          name: '购买过',
          selected: false
        },
        consomedData.data &&
          consomedData.data.resultList &&
          consomedData.data.resultList.length > 0 &&
          consomedData.data.resultList.map(
            i =>
              new ConsumedSku({
                id: i.orderIdStr,
                title: i.productInfo.name,
                imgurl: i.productInfo.picUrl,
                price: i.productInfo.price,
                btnText: i.button || '选择',
                consumTime:
                  (i.tradeFinishTime &&
                    `核销时间：${new Date(Number(i.tradeFinishTime))
                      .toLocaleString()
                      .replace(/\//g, '-')}`) ||
                  '',
                storeName: i.shopInfo.shopName,
                consumStatus: '已核销',
                productId: i.productInfo.productId.toString(),
                buttonStatus: i.buttonStatus
              })
          )
      )
    )
    callback({ skus: new Skus(skutypes), picker: param })
    instance.hide()
  })
}

export function doctorSelect(param: Picker, callback: Function): void {
  const shopId = getShopId()
  getDoctors({ shopId, platform: 1, userId: RequestObj.userId }).then(data => {
    callback({ doctorData: data, picker: param })
  })
}
export function getShopId(): any {
  return SubmitData.getInstance().getKeyValue('shopId')[0].value
}

export function shopSelect(param: Picker, callback: Function): Promise<void> {
  getShopsByParams().then(resp => {
    callback({ shops: new ShopObj(resp), picker: param })
  })
}
