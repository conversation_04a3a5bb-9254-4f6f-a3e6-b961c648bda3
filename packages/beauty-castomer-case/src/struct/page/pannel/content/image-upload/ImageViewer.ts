import { Submit<PERSON><PERSON>, SubmitKeyParam } from '../../../../common/SubmitKey'
import ImageUpload, { ImageStyle } from './index'
import { rerender, validate } from '../../../../decorators'
import TagPicker from './TagPicker'
import BigImagePreview from './BigImagePreview'

export interface ImageViewerParam {
  editAble: boolean
  deleteAble: boolean
  label?: string
  imageUrl: string
  imageKey?: string
  style?: ImageStyle
}
export default class ImageViewer extends SubmitKey {
  param: ImageViewerParam
  parent: ImageUpload
  constructor(inParam: ImageViewerParam & SubmitKeyParam, inParent: ImageUpload) {
    const {
      key,
      value,
      validator,
      visible,
      unvisibleKeepValue,
      disabled,
      defaultArray,
      ...inputParam
    } = inParam

    let submitValue = (value.tagName && { ...value }) || {}
    inParam.imageKey
      ? (submitValue[`${inParam.imageKey}`] = inParam.imageUrl)
      : (submitValue = inParam.imageUrl)
    super({
      key,
      value: submitValue,
      validator,
      visible,
      unvisibleKeepValue,
      disabled,
      defaultArray
    })
    this.param = inputParam
    this.parent = inParent
  }

  public changeImageUrl (imageUrl: string) {
    this.param.imageKey
      ? (this.value[`${this.param.imageKey}`] = imageUrl)
      : (this.value = imageUrl)
    this.param.imageUrl = imageUrl
  }
  public setVisible(visible: boolean) {
    this.parent.setVisible(visible)
  }
  @rerender
  @validate
  public delete(index: number) {
    super.deleteSubmitkey()
    this.parent.changeImageViewers(index)
  }
  public edit(index: number) {
    this.parent.imageUploader.upload(1, 1, index)
  }

  public openTagModail() {
    TagPicker.startTag(this)
  }

  public preview(index: number) {
    BigImagePreview.preview(this.parent.imageViewers, index)
  }
}
