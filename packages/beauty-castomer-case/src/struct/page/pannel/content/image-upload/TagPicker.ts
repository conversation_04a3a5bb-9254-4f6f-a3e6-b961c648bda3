import ModailObj from '../../../../common/ModailObj'
import ImageViewer from './ImageViewer'
import { rerender, validate } from '../../../../decorators'

export enum TagType {
  before = 1,
  current,
  after
}
interface Tag {
  tagType: TagType
  tagDays?: number
  tagName: string
  selected: boolean
}
export default class TagPicker extends ModailObj {
  private static tagPicker: TagPicker | undefined
  imageViewer: ImageViewer
  tags: Tag[]
  private constructor() {
    super()
    this.tags = [
      {
        tagType: TagType.before,
        tagName: '体验前',
        selected: false
      },
      {
        tagType: TagType.current,
        tagName: '体验后当天',
        selected: false
      },
      {
        tagType: TagType.after,
        tagDays: 1,
        tagName: '体验后',
        selected: false
      }
    ]
  }
  public static getInstance(): TagPicker | undefined {
    return this.tagPicker
  }
  @rerender
  selectTag(tag: Tag) {
    this.tags.forEach(x => (x.selected = x.tagType === tag.tagType))
  }

  @rerender
  public static startTag(imageViewer: ImageViewer) {
    this.tagPicker = new TagPicker()
    this.tagPicker.imageViewer = imageViewer
    this.tagPicker.tags.forEach(x => (x.selected = x.tagType === imageViewer.value.tagType))
    return this.tagPicker
  }
  @rerender
  @validate
  public tag() {
    this.tags.forEach(x => {
      if (x.selected) {
        this.imageViewer.value.tagType = x.tagType
        this.imageViewer.value.tagName =
          x.tagType === TagType.after ? (x.tagName += `${x.tagDays}天`) : x.tagName
      }
    })
    this.visible = false
  }
}
