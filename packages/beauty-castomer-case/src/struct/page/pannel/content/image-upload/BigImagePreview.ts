import ModailObj from '../../../../common/ModailObj'
import ImageViewer from './ImageViewer'
import { rerender } from '../../../../decorators'

export default class BigImagePreview extends ModailObj {
  private static bigImagePreview: BigImagePreview
  public imageViewers: ImageViewer[]
  public currentIndex: number
  private constructor(imageViewers: ImageViewer[], index?: number) {
    super()
    this.imageViewers = imageViewers
    this.currentIndex = index
  }
  public static getInstance() {
    return this.bigImagePreview
  }
  @rerender
  public static preview(imageViewers: ImageViewer[], index?: number) {
    this.bigImagePreview = new BigImagePreview(imageViewers, index)
  }

  @rerender
  public onViewableItemsChanged(index: number) {
    this.currentIndex = index
  }
}
