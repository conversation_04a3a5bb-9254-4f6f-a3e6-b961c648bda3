import KNB from '@mrn/mrn-knb'
import ImageUpload from './index'
import LoadingManager from '../../../../common/LoadingManager'
import { SubmitKey, SubmitKeyParam } from '../../../../common/SubmitKey'
import Toast from '@max/leez-toast'
const SCENETOKEN = 'dd-93d5b448e808e662'
export interface ImageUploaderParam {
  title: string
}

export interface CurrentUploaded {}
export default class ImageUploader extends SubmitKey {
  param: ImageUploaderParam
  parent: ImageUpload
  constructor(inParam: ImageUploaderParam & SubmitKeyParam, inParent: ImageUpload) {
    const {
      key,
      value,
      validator,
      visible,
      unvisibleKeepValue,
      disabled,
      defaultArray,
      ...inputParam
    } = inParam
    super({ key, value: undefined, validator, visible, unvisibleKeepValue, disabled, defaultArray })
    this.param = inputParam
    this.parent = inParent
  }

  public setVisible(visible: boolean) {
    this.parent.setVisible(visible)
  }
  public upload(limitCount: number, total: number, index?: number) {
    if (limitCount === 0) {
      Toast.open({
        content: `图片仅能上传${total}张`
      })
    } else {
      this.pickImage(limitCount, index)
    }
  }

  private pickImage(limitCount: number, index?: number) {
    const that = this
    KNB.chooseImage({
      sceneToken: SCENETOKEN,
      source: '',
      count: limitCount,
      returnType: 'localId',
      success: function (result) {
        const photos = result.photoInfos // photoInfos是一个对象数组，每个对象包括以下内容
        if (photos.length > 0) {
          LoadingManager.getInstance().show()
          let count = 0
          photos.forEach(function (photo) {
            that.uploadFile(
              photo.localId,
              that.parent,
              () => {
                count++
                if (photos.length === count) {
                  LoadingManager.getInstance().hide()
                }
              },
              index
            )
          })
        }
      },
      fail: function (reason) {
        console.error(JSON.stringify(reason))
        LoadingManager.getInstance().hide()
      }
    })
  }

  private uploadFile(
    localId: string,
    parent: ImageUpload,
    loadingHandler: Function,
    index?: number
  ) {
    KNB.use('compressImage', {
      sceneToken: SCENETOKEN,
      image: localId,
      scale: 70,
      quality: 50,
      success: function (result) {
        const { outputPath } = result
        let correctedPath = outputPath
        // 适配代码
        if (!/^knb-media/.test(correctedPath)) {
          correctedPath = `knb-media://client?url=${encodeURIComponent(correctedPath)}`
        }
        KNB.uploadFile({
          sceneToken: SCENETOKEN,
          fileName: correctedPath,
          contentType: 'image/*',
          venusEnvironment: 'userCenter',
          success: function (results) {
            parent.changeImageViewV2(results.url.replace('http:','https:'), index)
            loadingHandler()
          },
          fail: function (reason) {
            console.error(JSON.stringify(reason))
            loadingHandler()
          }
        })
      },
      fail: function (reason) {
        console.error(JSON.stringify(reason))
        loadingHandler()
      }
    })
  }
}
