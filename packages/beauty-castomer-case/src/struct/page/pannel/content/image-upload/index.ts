import ImageUploader from './ImageUploader'
import ImageViewer from './ImageViewer'
import { rerender, validate } from '../../../../decorators'
import { Validations } from '../../../../common/SubmitKey'
export interface ImageUploadParam {
  deleteAble: boolean
  editAble: boolean
  mainTitle: string
  header?: [Link | string]
  footer?: [Link | string]
  limitSize: number
  key: string
  value: any
  validator: Validations | ((value: any | undefined) => boolean)
  visible: boolean
  imageKey?: string
  rightContent?: React.JSX.Element
  style?: ImageStyle
  defaultArray?: boolean
}
export interface Link {
  label: string
  habdle: Function
}

export function isLink(param: Link | string): param is Link {
  return !!param?.label
}
export enum ImageStyle {
  tag
}
export default class ImageUpload {
  param: ImageUploadParam
  imageUploader: ImageUploader
  imageViewers: ImageViewer[]
  visible: boolean
  constructor(inParam: ImageUploadParam) {
    const { mainTitle, key, value, validator, visible } = inParam
    this.param = inParam
    this.imageUploader = new ImageUploader({ title: mainTitle, key, value, validator }, this)
    this.imageViewers = []
    this.visible = visible
  }
  @rerender
  @validate
  public setVisible(visible: boolean) {
    this.visible = visible
    this.imageUploader.visible = visible
    this.imageViewers.forEach(x => (x.visible = visible))
  }

  public changeImageView(imageObj: string | {}, index?: number) {
    let { key, value, editAble, deleteAble, imageKey, style, defaultArray } = this.param
    const imageUrl = typeof imageObj === 'string' ? imageObj : imageObj[this.param.imageKey]
    value = imageObj
    if (index !== undefined) {
      this.imageViewers[index].changeImageUrl(imageUrl)
    } else {
      const imageViewer = new ImageViewer(
        { key, value, editAble, deleteAble, imageUrl, imageKey, style, defaultArray },
        this
      )
      this.addImageView(imageViewer)
    }
  }
  @rerender
  @validate
  public changeImageViewV2(imageUrl: string, index?: number) {
    this.changeImageView(imageUrl, index)
  }

  private addImageView(imageViewer: ImageViewer) {
    this.imageViewers.unshift(imageViewer)
  }

  public changeImageViewers(index: number) {
    this.imageViewers.splice(index, 1)
  }
}
