import { Submit<PERSON><PERSON>, SubmitKeyParam } from '../../../common/SubmitKey'
import { SubmitData } from '../../../common/SubmitData'
import { rerender } from '../../../decorators'

export interface InputParam {
  placeholder: string
}
export default class Input extends SubmitKey {
  param: InputParam | undefined
  constructor(inParam: InputParam & SubmitKeyParam) {
    const {
      key,
      value,
      validator,
      visible,
      unvisibleKeepValue,
      disabled,
      defaultArray,
      ...inputParam
    } = inParam
    super({ key, value, validator, visible, unvisibleKeepValue, disabled, defaultArray })
    this.param = inputParam
  }
  @rerender
  blur() {
    SubmitData.getInstance().validate()
  }
}
