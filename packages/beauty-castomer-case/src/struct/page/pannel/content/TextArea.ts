import { Submit<PERSON><PERSON>, SubmitKeyParam } from '../../../common/SubmitKey'
import { rerender } from '../../../decorators'
import { SubmitData } from '../../../common/SubmitData'
export interface TextAreaParam {
  placeholder: string
  customized: any
}
export default class TextArea extends SubmitKey {
  param: TextAreaParam | undefined

  constructor(inParam: TextAreaParam & SubmitKeyParam) {
    const {
      key,
      value,
      validator,
      visible,
      unvisibleKeepValue,
      disabled,
      defaultArray,
      ...textAreaParam
    } = inParam
    super({ key, value, validator, visible, unvisibleKeepValue, disabled, defaultArray })
    this.param = textAreaParam
  }
  @rerender
  blur() {
    SubmitData.getInstance().validate()
  }
}
