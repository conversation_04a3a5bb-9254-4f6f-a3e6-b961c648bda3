import { SubmitK<PERSON>, SubmitKeyParam } from '../../../common/SubmitKey'
import SelectModail from './SelectModail'
import { Pannel } from '../index'
import PickerManager from './PickerManager'
export interface PickerParam {
  title: string
  viewer: React.JSX.Element
  paramPaser?: Function
  transport: boolean
  setValueCallBack?: Function
}
export class Picker extends SubmitKey {
  param: PickerParam | undefined
  label: string
  pannel: Pannel
  setValueCallBack?: Function
  constructor(inParam: SubmitKeyParam & PickerParam, pannel: Pannel) {
    const {
      key,
      value,
      validator,
      visible,
      unvisibleKeepValue,
      disabled,
      setValueCallBack,
      defaultArray,
      ...inputParam
    } = inParam
    super({ key, value, validator, visible, unvisibleKeepValue, disabled, defaultArray })
    this.param = inputParam
    this.pannel = pannel
    this.setValueCallBack = setValueCallBack
    PickerManager.getInstance().register(this)
  }

  public pickValue() {
    this.param.paramPaser
      ? this.param.paramPaser(this, this.startPick.bind(this))
      : this.startPick()
  }
  private startPick(param?: any) {
    const modailParam = {
      title: this.param.title,
      content: {
        param,
        viewer: this.param.viewer
      }
    }
    SelectModail.show(modailParam, this.setValue.bind(this))
  }
  public setValue(value: any, label: string) {
    this.value = value
    this.label = label
    this.setValueCallBack && this.setValueCallBack(this, value)
  }
  public setVisible(visible: boolean) {
    this.visible = visible
  }
}
