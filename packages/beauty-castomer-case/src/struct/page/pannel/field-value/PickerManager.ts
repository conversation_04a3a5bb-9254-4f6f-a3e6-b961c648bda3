import { Picker } from './Picker'
import ExtendedArray from '../../../common/ExtendedArray'

export default class PickerManager {
  private static pickerManager: PickerManager | undefined
  picers: ExtendedArray<Picker>
  private constructor() {
    this.picers = new ExtendedArray<Picker>()
  }

  public static getInstance(): PickerManager {
    if (this.pickerManager === void 0) {
      this.pickerManager = new PickerManager()
    }
    return this.pickerManager
  }

  public register(picker: Picker) {
    this.picers.push(picker)
  }

  public getPicker(key: string[]) {
    return <ExtendedArray<Picker>> this.picers.trueFilter(x => key.includes(x.key))
  }

  public clearPlckerValue(key?: string[]) {
    let preClearPickers = this.picers
    if (key) {
      preClearPickers = this.getPicker(key)
    }
    preClearPickers.forEach(x => {
      x.value = void 0
      x.label = void 0
    })
  }

  public setPickerValueByKey(key: string, pickerValue: { label: string; value: any }) {
    this.picers.forEach(x => {
      if (x.key === key) {
        x.value = pickerValue.value
        x.label = pickerValue.label
      }
    })
  }
}
