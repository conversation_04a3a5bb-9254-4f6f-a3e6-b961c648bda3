import { rerender, validate } from '../../../decorators'
import ModailObj from '../../../common/ModailObj'
export interface SelectModailParam {
  title: string
  content: {
    param?: any
    viewer: React.JSX.Element
  }
}
export default class SelectModail extends ModailObj {
  private static selectModail: SelectModail | undefined
  param: SelectModailParam
  static callBack: Function
  private constructor(props: SelectModailParam) {
    super()
    this.param = props
  }

  public static getInstance(): SelectModail | undefined {
    return this.selectModail
  }
  @rerender
  public static show(props: SelectModailParam, callBack: Function): SelectModail {
    this.callBack = callBack
    this.selectModail = new SelectModail(props)
    return this.selectModail
  }

  private hide() {
    this.visible = false
  }
  @rerender
  @validate
  public handleCallback(value: any, label: string) {
    SelectModail.callBack && SelectModail.callBack(value, label)
    this.hide()
  }
}
