import { SkuType } from './SkuType'
import { rerender } from '../../../../../decorators'
import { AllSku } from './AllSku'
import BeautyCastomerCaseRequest from '../../../../../../api/request/BeautyCastomerCaseRequest'

export class Skus {
  skutypes: SkuType[]
  constructor(skutypes: SkuType[]) {
    this.skutypes = skutypes
  }
  @rerender
  public selectSkuTypes(index: number) {
    this.skutypes &&
      Array.isArray(this.skutypes) &&
      this.skutypes.length > 0 &&
      this.skutypes.forEach((x, y) =>
        y === index ? (x.type.selected = true) : (x.type.selected = false)
      )
  }

  public searchSkuByKeyWord() {
    this.skutypes.forEach(x => {
      if (x.type.name === '全部商品') {
        const keyWord = x.searchKeyWord
        const that = this
        if (keyWord) {
          BeautyCastomerCaseRequest.searchAllSku(keyWord).then(y => {
            // @ts-ignore
            that.flushAfterSetSku(() =>
              y.data.goodsList && y.data.goodsList.length > 0
                ? (x.skuTypes = transformSearchData(y.data.goodsList))
                : (x.skuTypes = [])
            )
          })
        } else {
          BeautyCastomerCaseRequest.requestAllSku().then(y => {
            // @ts-ignore
            that.flushAfterSetSku(
              () =>
                y.msg &&
                y.msg.products &&
                y.msg.products.length > 0 &&
                (x.skuTypes = transformInitData(y.msg.products))
            )
          })
        }
      }
    })
  }
  @rerender
  private flushAfterSetSku(analyze: Function) {
    analyze && analyze()
  }
}

export function transformInitData(products: any) {
  return [
    new SkuType(
      {
        name: '推荐',
        selected: true
      },
      products.map(
        x =>
          new AllSku({
            id: x.productId,
            title: x.title,
            imgurl: x.squarePic,
            price: x.salePrice,
            btnText: '选择',
            subTitle:
              (x.aidDecisionTags && x.aidDecisionTags.length > 0 && x.aidDecisionTags[0].name) ||
              '',
            discount: void 0,
            originalPrice: void 0
          })
      )
    )
  ]
}

export function transformSearchData(goodsList: any) {
  return [
    new SkuType(
      {
        name: '搜索',
        selected: true
      },
      goodsList.map(
        x =>
          new AllSku({
            id: x.productId,
            title: x.title,
            imgurl: x.squarePicUrl,
            price: x.price,
            btnText: '选择',
            subTitle: '',
            discount: void 0,
            originalPrice: void 0
          })
      )
    )
  ]
}
