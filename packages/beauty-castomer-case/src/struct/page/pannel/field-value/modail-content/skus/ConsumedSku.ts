import { Sku, SkuParam } from './Sku'
export interface ConsumedSkuParam {
  consumTime: string
  storeName: string
  consumStatus: string
  productId: string
}
export class ConsumedSku extends Sku {
  param: ConsumedSkuParam
  constructor(props: ConsumedSkuParam & SkuParam) {
    const { id, title, imgurl, price, btnText, buttonStatus, ...params } = props
    super(id, title, imgurl, price, btnText, buttonStatus)
    this.param = params
  }
}
