import { isDP } from '@mrn/mrn-gc-base'
import { request } from '@mrn/mrn-utils'
import { getShopId } from 'src/struct/config'

export interface Doctor {
  technicianId: string
  detailUrl: string
  technicianName: string
  photoUrl: string
  title: string
}

export interface DoctorResp {
  success: boolean
  msg: string
  data: DoctorD<PERSON>
}

export interface DoctorData {
  totalHit: number
  resultList: Doctor[]
}

interface QueryDoctorParms {
  shopId: any
  // useId: any
  platform: number
}

const requestConfig = {
  url: '',
  method: 'GET',
  // baseURL: isDP() ? 'https://m.51ping.com' : 'https://test.i.meituan.com',
  baseURL: isDP() ? 'https://m.dianping.com' : 'https://i.meituan.com',
  params: {},
  // 模拟登陆用
  // headers: {
  //   cookie: 'testUserId=9000000000103044819'
  // },
  options: {
    disableRisk: false,
    registerCandyHost: false
  }
}
// 获取医生列表
export function getDoctors(params: QueryDoctorParms): Promise<DoctorResp> {
  const requestParams = Object.assign({}, requestConfig, {
    url: '/gw/medical/content/userexperiencereport/technician/list',
    params
  })

  return request(requestParams)
    .then(response => {
      return (response.data as DoctorResp).success ? (response.data as DoctorResp).data : null
    })
    .catch(error => {
      console.error(`getDoctorsError:${error}`)
    })
}

export function loadmore(
  hasMore: boolean,
  cacheRef: React.MutableRefObject<{
    pageNum: number
    hasMore: boolean
  }>
) {
  if (hasMore) {
    cacheRef.current.pageNum++
    getDoctors({ shopId: getShopId(), platform: isDP() ? 1 : 2 }).then(data => {})
  }
}
