import { isDP } from '@mrn/mrn-gc-base'
import { request } from '@mrn/mrn-utils'
import { getLocationInfo } from '../../../../../../utils'
import { rerender } from '../../../../../decorators'
import RequestObj from '../../../../../../api/request/RequestObj'

export interface QueryShopsResut {
  totalHit: number
  resultList: Shop[]
}
interface ShopResp {
  code: number
  msg: string
  success: boolean
  data: ShopData
}
interface ShopData {
  totalHit: number
  resultList: Shop[]
}

export interface Shop {
  shopId: string
  shopName: string
  cityName: string
  distance: string
}

interface QueryShopParms {
  keyword?: string
  lng: number
  lat: number
  pageNo?: number
  size?: number
  // userId?: number
  platform?: number
}

const requestConfig = {
  url: '',
  method: 'GET',
  // baseURL: isDP() ? 'https://m.51ping.com' : 'https://test.i.meituan.com',
  baseURL: isDP() ? 'https://m.dianping.com' : 'https://i.meituan.com',
  params: {},
  // // 模拟登陆用
  // headers: {
  //   cookie: 'testUserId=**********;'
  // },
  options: {
    disableRisk: false,
    registerCandyHost: false
  }
}

// 获取店铺列表
export function getShops(params: QueryShopParms): Promise<Shop[]> {
  const requestParams = Object.assign({}, requestConfig, {
    url: '/gw/medical/content/userexperiencereport/shop/list',
    params
  })

  return request(requestParams)
    .then(response => {
      return (response.data as ShopResp).success
        ? (response.data as ShopResp).data.resultList
        : null
    })
    .catch(error => {
      console.error(`getShopsError:${error}`)
    })
}

export function getShopsByParams() {
  return getShops({
    userId: RequestObj.userId,
    // pageNo: 1,
    // size: pageSize,
    lng: RequestObj.lng,
    lat: RequestObj.lat,
    platform: isDP() ? 1 : 2
  })
}

export async function fetchShops(searchWord: string): Promise<Shop[] | void> {
  const location: any = await getLocationInfo()
  const lat = location?.lat
  const lng = location?.lng

  return getShops({
    userId: RequestObj.userId,
    keyword: searchWord,
    // pageNo: 1,
    // size: pageSize,
    lng: lng,
    lat: lat,
    platform: isDP() ? 1 : 2
  })
    .then(resp => {
      return resp
    })
    .catch(err => {
      console.error('error caught', err)
    })
}

export class ShopObj {
  shops: Shop[]
  keyWord: string = ''
  constructor(shops: Shop[]) {
    this.shops = shops
  }
  setKeyWord(keyWord: string) {
    this.keyWord = keyWord
  }
  @rerender
  setShops(shops: Shop[]) {
    this.shops = shops
  }
  requestShops() {
    const that = this
    if (this.keyWord) {
      fetchShops(this.keyWord).then(rep => {
        that.setShops(rep ? rep : [])
      })
    } else {
      getShopsByParams().then(rep => {
        that.setShops(rep ? rep : [])
      })
    }
  }
}
