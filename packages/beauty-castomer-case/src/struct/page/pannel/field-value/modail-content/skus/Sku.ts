import { Picker } from '../../Picker'

export interface SkuParam {
  id: string
  title: string
  imgurl: string
  price: number
  btnText: string
  buttonStatus: 1 | 2 // 1 正常 2 禁用
}
export abstract class Sku {
  id: string
  title: string
  imgurl: string
  price: number
  btnText: string
  buttonStatus: 1 | 2 // 1 正常 2 禁用
  constructor(
    id: string,
    title: string,
    imgurl: string,
    price: number,
    btnText: string,
    buttonStatus: 1 | 2
  ) {
    this.id = id
    this.title = title
    this.imgurl = imgurl
    this.price = price
    this.btnText = btnText
    this.buttonStatus = buttonStatus
  }

  abstract choose(picker: Picker)
}
