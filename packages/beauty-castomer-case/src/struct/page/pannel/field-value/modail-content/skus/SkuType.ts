import { rerender } from '../../../../../decorators'
import { AllSku } from './AllSku'
import { ConsumedSku } from './ConsumedSku'
interface SkuTypeProps {
  name: string
  selected: boolean
}
export function skuIsAllSku(sku: AllSku | ConsumedSku): sku is AllSku {
  return sku && !sku.hasOwnProperty('storeName')
}
export function skuIsConsumedSku(sku: AllSku | ConsumedSku): sku is ConsumedSku {
  return sku && sku.hasOwnProperty('storeName')
}
export class SkuType {
  type: SkuTypeProps
  skus: AllSku[] | ConsumedSku[]
  skuTypes?: SkuType[]
  searchKeyWord: string
  constructor(type: SkuTypeProps, skus: AllSku[] | ConsumedSku[], skuTypes?: SkuType[]) {
    this.type = type
    this.skus = skus || []
    this.skuTypes = skuTypes
  }
  public addSkus(skus: AllSku[] | ConsumedSku[]) {
    //@ts-ignore
    this.skus.push(...skus)
  }
  @rerender
  public selectSkuTypes(index: number) {
    this.skuTypes &&
      Array.isArray(this.skuTypes) &&
      this.skuTypes.length > 0 &&
      this.skuTypes.forEach((x, y) =>
        y === index ? (x.type.selected = true) : (x.type.selected = false)
      )
  }
  public setSearchKeyWord(keyWord: string) {
    this.searchKeyWord = keyWord
  }

  public search() {}
}
