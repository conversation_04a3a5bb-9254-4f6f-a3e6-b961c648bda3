import { SubmitKeyParam } from '../../common/SubmitKey'
import { Picker } from './field-value/Picker'

export interface PannelParam {
  title: string
  notice?: string
  fieldValue?: FieldValue
  content?: PannelContent
}
interface FieldValue {
  type: FieldType
  params: SubmitKeyParam & { [key: string]: any }
}
interface PannelContent {
  moduleName: string
  moduleParam: SubmitKeyParam & { [key: string]: any }
}
export enum FieldType {
  'picker' = 1,
  'input',
  'selection'
}

export type FieldValueType = Picker | undefined
export class Pannel {
  param: PannelParam | undefined
  content: Object | undefined
  fieldValue: FieldValueType

  constructor(inParam: PannelParam, pannelModules: {}) {
    this.param = inParam
    const contentParam = inParam?.content
    contentParam &&
      (this.content = new pannelModules[contentParam.moduleName](contentParam.moduleParam))
    const fieldValueParam = inParam.fieldValue
    if (fieldValueParam) {
      switch (fieldValueParam.type) {
        case FieldType.picker:
          //@ts-ignore
          this.fieldValue = new Picker(fieldValueParam.params, this)
          break
      }
    }
  }
}
