import { redirectTo } from '@mrn/mrn-utils'
import { SubmitData } from '../../common/SubmitData'
import { rerender, validate } from '../../decorators'
import AgreementModail from '../../common/AgreementModail'
import Toast from '@max/leez-toast'
import { BeautyCastomerCaseRequest } from '../../../api/request'
export interface FooterParam {
  agreements: Agreement[]
  submitBtn: SubmitBtn
}

interface SubmitBtn {
  title: string
  before?: (current: Footer) => void
  after?: (current: Footer) => void
  linkUrl?: string
  navigation: Function
}
export interface Agreement {
  title: string
  content: string
}
export interface SubmitRes {}
export interface submitParam {}
export class Footer {
  /**
   * checkbox状态
   * @private
   */
  agreementStatus: boolean = false
  /**
   * 验证状态
   * @private
   */
  validateCheck: boolean = false
  param: FooterParam | undefined

  agreementModal: AgreementModail

  public constructor(inparam: FooterParam) {
    this.param = inparam
  }

  /**
   * 点击checkBox
   */
  @rerender
  @validate
  public tapCheckBox() {
    this.agreementStatus = !this.agreementStatus
  }

  /**
   * 更新验证状态
   * @param state
   */
  @rerender
  public updateValidateState(state: boolean) {
    this.validateCheck = state
  }

  /**
   * 点击提交按钮
   */
  public tapSubmitBtn() {
    SubmitData.getInstance().validate(true) ? this.validateSuccess() : this.validateFailed()
  }

  /**
   * 验证未通过
   * @private
   */

  private validateFailed() {}

  /**
   * 验证通过
   * @private
   */
  private validateSuccess() {
    const before = this.param?.submitBtn?.before
    before && before(this)
    this.submit().then(this.windup.bind(this))
  }

  /**
   * 提交
   * @private
   */
  private submit() {
    //准备数据
    const jsonValueData = SubmitData.getInstance().parseValueTree()
    return BeautyCastomerCaseRequest.submit(jsonValueData)
  }

  /**
   * 数据返回收尾
   * @param res
   * @private
   */
  private windup(res: SubmitRes) {
    const { after, linkUrl, navigation } = this.param.submitBtn
    after && after(this)
    const toastInstance = Toast.open({
      title: '提交成功',
      icon: 'gouxuanmian',
      content: '5s后页面返回',
      duration: 5000
    })
    let count = 5
    const timer = setInterval(() => {
      count -= 1
      if (!count) {
        clearInterval(timer)
        // 可以手动关闭 toast
        // toastInstance.close();
      } else {
        toastInstance.update({
          title: '提交成功',
          icon: 'gouxuanmian',
          content: `${count}s后页面返回`
        })
      }
    }, 1000)

    setTimeout(() => {
      linkUrl ? redirectTo(linkUrl) : navigation()
    }, 5000)
  }
  @rerender
  public openAgreementModail(param: Agreement) {
    this.agreementModal = new AgreementModail(param)
  }
}
