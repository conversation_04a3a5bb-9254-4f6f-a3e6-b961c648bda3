import { Footer, FooterParam } from './footer'
import { Title, TitleParam } from './title'
import { FieldType, Pannel, PannelParam } from './pannel'
import { SubmitData } from '../common/SubmitData'
import { rerender, validate } from '../decorators'
import ImageUpload from './pannel/content/image-upload'
import Input from './pannel/content/Input'
import { Picker } from './pannel/field-value/Picker'
import BeautyCastomerCaseRequest from '../../api/request/BeautyCastomerCaseRequest'
import RequestObj from '../../api/request/RequestObj'
export interface pageParam {
  devEnv: boolean
  titleParam: TitleParam
  pannelParams: PannelParam[]
  footerParam: FooterParam
  pannelModules: PannelModule[]
}

export interface PannelModule {
  moduleName: string
  moduleObj: Object
  view: Object
}
export class Page {
  devEnv: boolean
  title: Title | undefined
  pannels: Pannel[] | undefined
  footer: Footer | undefined
  pannelModules: {}
  pannelViews: {}
  private static page: Page | undefined
  private smartUpdate: React.DispatchWithoutAction | undefined

  private constructor(inParam: pageParam, echoParam?: string) {
    this.devEnv = inParam.devEnv
    SubmitData.getInstance().clear()
    this.pannelModules = {}
    this.pannelViews = {}
    inParam.pannelModules.forEach(x => {
      this.pannelViews[x.moduleName] = x.view
      this.pannelModules[x.moduleName] = x.moduleObj
    })
    this.title = new Title(inParam.titleParam)
    this.pannels = inParam.pannelParams.map(x => new Pannel(x, this.pannelModules))
    this.footer = new Footer(inParam.footerParam)
    ;(echoParam &&
      RequestObj.setEnvData(() => {
        BeautyCastomerCaseRequest.getDataList({ pageNo: 1, size: 1, itemIds: echoParam }).then(
          res =>
            this.analyzeEchoData(res, echoData => {
              echoData && this.dataEcho(echoData)
              SubmitData.getInstance().validate()
            })
        )
      })) ||
      RequestObj.setEnvData()
  }
  public static getInstance() {
    return this.page
  }
  public static createInstance(inParam: pageParam, echoParam: string) {
    this.page = new Page(inParam, echoParam)
    return this.page
  }
  public bindFlusher(smartUpdate: React.DispatchWithoutAction) {
    this.smartUpdate = smartUpdate
  }
  public flushPage() {
    this.smartUpdate()
  }

  private dataEcho(pageValueMock: {}) {
    const moduleEchoHandler = this.moduleEchoHandler.bind(this)
    this.pannels.forEach(function (x) {
      (x?.param?.content?.moduleName || x?.param?.fieldValue?.type) &&
        moduleEchoHandler(
          x?.param?.content?.moduleName || x?.param?.fieldValue?.type,
          x.fieldValue || x.content,
          pageValueMock
        )
    })
  }

  /**
   * 回显决策器
   * @param moduleName
   * @param content
   * @param echoValues
   * @private
   */
  private moduleEchoHandler(moduleName: string | FieldType, content: any, echoValues: {}) {
    switch (moduleName) {
      case 'image-upload':
        this.handleImageUploadEcho(content, echoValues)
        break
      case 'text-area':
      case 'input':
        this.handleInputEcho(content, echoValues)
        break
      case FieldType.picker:
        this.handlePickerEcho(content, echoValues)
        break
    }
  }

  private handleImageUploadEcho(content: ImageUpload, echoValues: {}) {
    const echoValue = echoValues[content.param.key]
    echoValue &&
      ((Array.isArray(echoValue) && echoValue.length > 0) || content.param.defaultArray
        ? echoValue.forEach(x => content.changeImageView(x))
        : content.changeImageView(echoValue))
  }
  private handleInputEcho(content: Input, echoValues: {}) {
    const echoValue = echoValues[content.key]
    echoValue && (content.value = echoValue)
  }
  private handlePickerEcho(content: Picker, echoValues: {}) {
    const echoValue = echoValues[content.key]
    //@ts-ignore
    echoValue && content.setValue(echoValue.value, echoValue.label)
  }
  @rerender
  public lockDoctorAndStore(lockStat: boolean) {
    SubmitData.getInstance()
      .getKeyValue(['doctorId', 'reference'])
      .forEach(x => {
        x.disabled = lockStat
      })
  }
  private analyzeEchoData(echoData: any, analyze: Function) {
    let datas
    let resData
    echoData.data &&
      echoData.data.resultList &&
      echoData.data.resultList.length > 0 &&
      (datas = echoData.data.resultList[0])
    datas &&
      (resData = {
        itemId: datas.itemId,
        content: datas.content,
        pics: datas.pics || [],
        'reference.certPic': (datas.reference && datas.reference.certPic) || [],
        doctorId: {
          value: datas.technicianInfo && datas.technicianInfo.technicianId,
          label: datas.technicianInfo && datas.technicianInfo.technicianName
        },
        shopId: {
          value: datas.shopInfo && datas.shopInfo.shopId,
          label: datas.shopInfo && datas.shopInfo.shopName
        },
        reference: {
          value: {
            productId: datas.reference && datas.reference.productId,
            orderId: datas.reference && datas.reference.orderIdStr
          },
          label: datas.reference && datas.reference.productTitle
        },
        title: datas.title,
        protocolPic: datas.protocolPic || []
      })

    analyze(resData)
  }
}
