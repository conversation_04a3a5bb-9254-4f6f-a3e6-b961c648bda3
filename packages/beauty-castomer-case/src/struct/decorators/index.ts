import { Page } from '../page'
import { SubmitData } from '../common/SubmitData'
export function rerender(target, name, descriptor) {
  const oldValue = descriptor.value
  descriptor.value = function () {
    const apply = oldValue.apply(this, arguments)
    Page.getInstance().flushPage()
    return apply
  }
  return descriptor
}

export function validate(target, name, descriptor) {
  const oldValue = descriptor.value
  descriptor.value = function () {
    const apply = oldValue.apply(this, arguments)
    setTimeout(() => {
      SubmitData.getInstance().validate()
    }, 0)
    return apply
  }
  return descriptor
}
