import { useState } from '@max/max'
import React from 'react'
import Tab from '@max/leez-tab'
import { StyleSheet } from 'react-native'
import { pageRouterClose } from '@mrn/mrn-utils'
import { SubmissionList } from './SubMissionList'

import PagerView from 'react-native-pager-view'
import { Image, Text, View, TouchableWithoutFeedback } from '@mrn/react-native'
import { getDeviceStatusBarHeight } from '@mrn/mrn-gc-base'

const myStyles = StyleSheet.create({
  flexStrech: { flex: 1 },
  title: {
    height: 44,
    width: '100%'
    // display: 'flex',
    //
    // alignItems: 'center',
    // flexDirection: 'row',
  },
  titleText: {
    width: '100%',
    height: '100%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row'
  },
  backIcon: {
    position: 'absolute',
    paddingLeft: 18,
    paddingRight: 18,
    height: '100%',
    zIndex: 2,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center'
  },
  titleTextContent: {
    fontFamily: 'PingFangSC-Regular',
    fontWeight: '500',
    color: '#111111',
    fontSize: 18
  }
})

const dataNormal = [
  { title: '全部投稿' },
  {
    title: '审核通过'
  },
  {
    title: '审核中'
  },
  {
    title: '被驳回'
  }
]

export default () => {
  const [selectedPageIndex, setSelectedPageIndex] = useState(0)
  return (
    <View style={myStyles.flexStrech}>
      {/*<NavigationBar*/}
      {/*  safeArea="normal"*/}
      {/*  backIcon={{ name: 'fanhui' }}*/}
      {/*  onBackPress={pageRouterClose}*/}
      {/*  onActionPress={pageRouterClose}*/}
      {/*>*/}
      {/*  <LText type="title3" lineClamp={1} style={{ textAlign: 'center' }}>*/}
      {/*    我的投稿*/}
      {/*  </LText>*/}
      {/*</NavigationBar>*/}
      <View style={{ height: getDeviceStatusBarHeight() }} />
      <View style={myStyles.title}>
        <TouchableWithoutFeedback onPress={() => pageRouterClose()}>
          <View style={myStyles.backIcon}>
            <Image
              style={{ width: 8.3, height: 16.67 }}
              source={{
                uri: 'https://p1.meituan.net/travelcube/d9e4e566e0f4419a2939bb507892f9d4327.png'
              }}
            />
          </View>
        </TouchableWithoutFeedback>

        <View style={myStyles.titleText}>
          <Text style={myStyles.titleTextContent}>我的投稿</Text>
        </View>
      </View>
      <View style={myStyles.flexStrech}>
        {/* <Tab
          activeIndex={selectedPageIndex}
          onItemPress={setSelectedPageIndex}
          data={dataNormal}
          mode="justify"
          enableAnimation={false}
          bgColorType={'light'}
          theme={{ anchorBgColor: '#FFD101', anchorWidth: 80 }}
        /> */}
        <SubmissionList activeIndex={selectedPageIndex} />
      </View>
    </View>
  )
}
