import RequestObj, { requestParam } from './RequestObj'
import { SubmitData } from '../../struct/common/SubmitData'

export default class BeautyCastomerCaseRequest extends RequestObj {
  private constructor() {
    super()
  }

  static requestAllSku() {
    const shopId = SubmitData.getInstance().getKeyValue('shopId')[0].value
    const allParams = { cityid: this.cityId, shopid: shopId, platform: 101 }
    //const allParams =  {cityid: 10,shopid: 109475855,platform: 201}
    const allConf: requestParam = {
      url: '/api/dzviewscene/productlist/list',
      method: 'GET',
      params: allParams
    }
    return RequestObj.getInstance().request(allConf)
  }
  static requestConsomeSku() {
    const shopId = SubmitData.getInstance().getKeyValue('shopId')[0].value
    const consomedParams = { userId: this.userId, platform: 1, shopId: shopId }
    //const consomedParams = { userId: **********, platform: 1, shopId: 102047277 }
    const consomedConf: requestParam = {
      url: '/gw/medical/content/userexperiencereport/order/list',
      method: 'GET',
      params: consomedParams
    }
    return RequestObj.getInstance().request(consomedConf)
  }

  static searchAllSku(keyword: string) {
    const shopId = SubmitData.getInstance().getKeyValue('shopId')[0].value
    const allParams = { cityid: this.cityId, shopid: shopId, platform: 101, keyword }
    //const allParams = { cityid: 10, shopid: 109475855, platform: 201, keyword }
    const allConf: requestParam = {
      url: '/api/dzviewscene/productshelf/medicalbeautyproductsearch.raw',
      method: 'GET',
      params: allParams
    }
    return RequestObj.getInstance().request(allConf)
  }

  static submit(submitData: any) {
    const extraParams = { userId: this.userId, platform: 1 }
    const submitConf: requestParam = {
      url: '/gw/medical/content/userexperiencereport/report/save',
      method: 'POST',
      data: { ...submitData, ...extraParams }
    }
    return RequestObj.getInstance().request(submitConf)
  }

  static getDataList(params: any) {
    const extraParams = {
      userId: this.userId,
      platform: 1,
      cityId: this.cityId,
      lat: this.lat,
      lng: this.lng
    }
    const dataListConf: requestParam = {
      url: '/gw/medical/content/userexperiencereport/report/list',
      method: 'GET',
      params: { ...params, ...extraParams }
    }
    return RequestObj.getInstance().request(dataListConf)
  }
  static getDoctorByShopId(shopId: string) {
    const extraParams = {
      userId: this.userId,
      platform: 1
    }
    const requestDoctorConf: requestParam = {
      url: '/gw/medical/content/userexperiencereport/technician/list',
      method: 'GET',
      params: { shopId, ...extraParams }
    }
    return RequestObj.getInstance().request(requestDoctorConf)
  }
}
