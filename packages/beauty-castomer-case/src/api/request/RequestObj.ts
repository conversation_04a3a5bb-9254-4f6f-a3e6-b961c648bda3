import MRNKNB from '@mrn/mrn-knb'
import Dialog from '@max/leez-dialog'

import { request } from '@mrn/mrn-utils'
const SCENETOKEN = 'dd-b5e142a49e49ad4d'
export interface requestParam {
  method: string
  url: string
  params?: any
  data?: any
}
export default class RequestObj {
  static userId: number
  static cityId: number
  static lat: number
  static lng: number
  private static requestObj: RequestObj | undefined
  private baseURL: string = 'https://m.dianping.com'
  protected constructor(baseUrl?: string) {
    baseUrl && (this.baseURL = baseUrl)
  }
  errorHandle(error) {
    const title = error.message || '网络异常，请稍后重试'
    const dialogInstance = Dialog.open({
      title: title,
      showCancelButton: false,
      showConfirmButton: false,
      showCloseIcon: false,
      maskClosable: true,
      onClosePress: () => {
        dialogInstance.close()
      }
    })
  }

  static getInstance(baseUrl?: string) {
    if (this.requestObj === void 0) {
      this.requestObj = new RequestObj(baseUrl)
    } else if (baseUrl) {
      this.requestObj.baseURL = baseUrl
    }
    return this.requestObj
  }

  static setEnvData(analyze?: () => void): boolean {
    const promises: Promise<any>[] = []
    promises.push(
      new Promise((resolve, reject) => {
        MRNKNB.getUserInfo({
          success: (user: any) => {
            const that = this
            if (user.userId === '-1') {
              MRNKNB.login({
                success: res => {
                  that.userId = Number(res.userId)
                  resolve(Number(res.userId))
                },
                fail: e => {
                  console.error('loginFailed',e)
                  reject(e)
                }
              })
            } else {
              this.userId = user.userId
              resolve(user.userId)
            }
          },
          fail: e => {
            console.error('getUserInfoFailed',e)
            reject(e)
          }
        })
      })
    )

    const that = this
    promises.push(
      new Promise((resolve, reject) => {
        MRNKNB.getCity({
          success: function (city) {
            that.cityId = Number(city.cityId)
            resolve(Number(city.cityId))
          },
          fail: e => {
            console.error('getCityFailed',e)
            reject(e)
          }
        })
      })
    )

    promises.push(
      new Promise((resolve, reject) => {
        MRNKNB.getLocation({
          sceneToken: SCENETOKEN, // 因隐私合规中长期方案影响，增加sceneToken，根据mode不同需要不同权限，mode为instant和accurate为需要Locate.continuous，其他需要Locate.once
          type: 'wgs84', // 可选值wgs84(标准坐标系)， gcj02（国测局 火星坐标）,1.2.0版本支持，建议微信小写，美团写大写
          timeout: 5000, //定位超时时间，1.2.0版本默认为5000，1.1.0版本默认为6000
          success: function (location) {
            that.lat = location.lat // 纬度
            that.lng = location.lng // 经度
            resolve([location.lat, location.lng])
          },
          fail: e => {
            console.error('getLocationFailed',e)
            reject(e)
          }
        })
      })
    )

    Promise.all(promises).then(() => {
      analyze && analyze()
    })
    return true
  }
  request(requestParam: requestParam) {
    const that = this
    const { method, url, params, data } = requestParam
    const requestConfig = {
      url,
      method,
      params,
      data,
      baseURL: this.baseURL,
      options: {
        disableRisk: false,
        registerCandyHost: false
      }
    }
    return new Promise<any>(function (resolve, reject) {
      request(requestConfig)
        .then(response => {
          if (response.data.code === 200) {
            resolve(response.data)
          } else {
            throw new Error(response.data.msg)
          }
        })
        .catch(error => {
          that.errorHandle(error)
          console.error(`getCraftsmanInfo:${error}`)
          reject(error)
        })
    })
  }
}
