// 禁用风控 //disableRisk即 SAKBaseModel中disableRisk // iOS MRN 2.5.3 支持
import Dialog from '@max/leez-dialog'
import { isDP } from '@mrn/mrn-gc-base'
const errorHandle = error => {
  const title = error.message || '网络异常，请稍后重试'
  const dialogInstance = Dialog.open({
    title: title,
    showCancelButton: false,
    showConfirmButton: false,
    showCloseIcon: false,
    maskClosable: true,
    onClosePress: () => {
      dialogInstance.close()
    }
  })
}

// 示例 request 用法
export const requestConfig = {
  url: '',
  method: 'GET',
  //baseURL: isDP() ? 'https://m.51ping.com' : 'https://test.i.meituan.com',
  baseURL: isDP() ? 'https://m.dianping.com' : 'https://i.meituan.com',

  params: {},
  // // 模拟登陆用
  // headers: {
  //   cookie: 'testUserId=**********;'
  // },
  options: {
    disableRisk: false,
    registerCandyHost: false
  }
}
