import { isDP } from '@mrn/mrn-gc-base'
import { request } from '@mrn/mrn-utils'
import { Doctor } from 'src/struct/page/pannel/field-value/modail-content/doctors/dotctor'
import { Shop } from 'src/struct/page/pannel/field-value/modail-content/shops/shop'

import { requestConfig } from './index'

/**
 * 我的投稿
 */

export interface SubmissionsQueryParams {
  lat: number
  lng: number
  status: number
  pageNo: number
  size: number
  cityId: number
}
export interface QuerySubmissionResult {
  totalHit: number
  resultList: Submission[]
}

export enum SubmissionStatus {
  // 1-审核中，2审核通过，3审核驳回
  Work = 2,
  inProcess = 1,
  Reject = 3
}

export interface Submission {
  itemId?: string
  title?: string
  content?: string
  status?: number //状态  {1-审核中，2审核通过，3审核驳回}
  addTime?: string
  shopInfo?: Shop
  technicianInfo?: Doctor
  reference?: Reference
  protocolPic?: string[]
  pics?: Pic[]
  auditMsg?: string
}
export interface Pic {
  url?: string
  tagType?: number
  tagName?: string
}

export interface Reference {
  productTitle: string

  productId: string

  certPic: string[]

  orderId: string

  productPrice: string
}

export function fetchSubmissions(params: SubmissionsQueryParams): Promise<QuerySubmissionResult> {
  const submissionRequestConfig = Object.assign({}, requestConfig, {
    url: '/gw/medical/content/userexperiencereport/report/list',
    params
  })

  return request(submissionRequestConfig).then(response => {
    if (response.data.code === 200) {
      return response.data.data
    } else {
      console.error('new Error', response.data.msg)
    }
  })
  // }
}

export interface TabsData {
  pageNum: number
  list: Submission[]
  hasMore: boolean
}
