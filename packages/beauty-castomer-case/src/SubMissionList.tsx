import View from '@hfe/max-view'
import Text from '@hfe/max-text'
import RecyclerView, { RecyclerViewRef } from '@hfe/max-recyclerview'
import React from 'react'
import { getSafeAreaBottom, isDP } from '@mrn/mrn-gc-base'
import { Image, StyleSheet } from 'react-native'
import LButton from '@max/leez-button'

import { useEffect, useState, useRef } from 'react'
import { getCityInfo, getLocationInfo } from './utils'
import {
  fetchSubmissions,
  QuerySubmissionResult,
  Submission,
  SubmissionsQueryParams,
  SubmissionStatus,
  TabsData
} from './api/Submissions'
import { redirectTo } from '@mrn/mrn-utils'
import { LoadingView } from '@nibfe/gc-ui'
import MRNKNB from '@mrn/mrn-knb'

const style = StyleSheet.create({
  rightContent: {
    marginLeft: 9,
    justifyContent: 'space-between'
  },
  mainText: {
    color: '#222222',
    fontWeight: '500',
    fontSize: 12,
    height: 17
  },
  subtext: {
    color: '#666666',
    fontWeight: '400',
    fontSize: 12,
    letterSpacing: 0,
    textAlign: 'left',
    height: 17
  },
  subText2: {
    color: '#222222',
    fontWeight: '400',
    fontSize: 12,
    textAlign: 'left',
    height: 17
  },
  recyclerView: {
    backgroundColor: '#F4F4F4',
    flexGrow: 1
  },
  image: { width: 60, height: 80, borderRadius: 6 },

  bottomText: {
    color: '#666666',
    fontWeight: '400',
    fontSize: 12,
    lineHeight: 16,
    textAlign: 'left',
    minWidth: 228,
    flexGrow: 1,
    width: 0,
    alignSelf: 'center'
  },

  bottomTextRebort: {
    color: '#FF2727',
    fontSize: 12,
    lineHeight: 16,
    textAlign: 'left',
    width: 0,
    flexGrow: 1,
    alignSelf: 'center'
  },

  submissionBgInProcess: {
    position: 'absolute',
    backgroundColor: '#FF6000',
    bottom: 0,
    borderBottomStartRadius: 6,
    borderBottomEndRadius: 6
  },

  submissionBgWork: {
    position: 'absolute',
    backgroundColor: '#2ABE3E',
    bottom: 0,
    borderBottomStartRadius: 6,
    borderBottomEndRadius: 6
  },

  submissionBgRejcet: {
    position: 'absolute',
    backgroundColor: '#ff2121',
    bottom: 0,
    borderBottomStartRadius: 6,
    borderBottomEndRadius: 6
  },

  submissionStatus: {
    width: 60,
    bottom: 0,
    color: '#FFFFFF',
    textAlign: 'center',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 10
  },
  split: { backgroundColor: '#EEE', height: 0.5, marginTop: 12 },
  recyclerViewHeader: { height: 0, width: 150 },
  recyclerViewCell: {
    marginTop: 16,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    padding: 12,
    marginStart: 16,
    marginEnd: 16
  },
  imageBg: { borderRadius: 6, overflow: 'hidden' },
  directionRow: {
    flexDirection: 'row'
  },
  bottom: {
    flexDirection: 'row',
    paddingTop: 12,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end'
  }
})
/**
 * 我的投稿列表
 */
const pageSize = 10
export function SubmissionList(props: { activeIndex: any }) {
  const recyclerViewRef = useRef<RecyclerViewRef>(null)
  const tabsData = useRef<TabsData>(null) // 保存的页面数据  pageSize, Submission[]

  var [listData, setListData] = useState<Submission[]>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [isFirstLoad, setIsFirstLoad] = useState(true) // 用于跟踪是否是首次加载
  const cityId = useRef<number>(null)
  const loaction = useRef<any>(null)

  useEffect(() => {
    setLoading(true)

    if (cityId.current && loaction.current) {
      newFunction(cityId, props, loaction, setListData, tabsData, setLoading, setIsFirstLoad)
    } else {
      Promise.all([getCityInfo(), getLocationInfo()]).then(values => {
        const [cityInfo, locationInfo] = values
        cityId.current = cityInfo && (cityInfo as any)?.cityId
        loaction.current = locationInfo
        newFunction(cityId, props, loaction, setListData, tabsData, setLoading, setIsFirstLoad)
      })
    }
  }, [props, tabsData, props.activeIndex])

  return loading ? (
    <LoadingView imageStyle={{ marginTop: 30 }} />
  ) : listData ? (
    <RecyclerView
      style={style.recyclerView}
      stickyHeaderIndices={[0]}
      ref={recyclerViewRef}
      showsVerticalScrollIndicator={false}
      onEndReached={() => {
        if (!isFirstLoad) {
          loadmore(
            tabsData,
            props.activeIndex,
            setListData,
            // listData,
            loaction.current,
            cityId.current
          )
        }
      }}
    >
      <RecyclerView.Header>
        <View style={style.recyclerViewHeader} />
      </RecyclerView.Header>
      {listData.map((item, index) => {
        return SubmissionCell(props, index, item, listData.length)
      })}
    </RecyclerView>
  ) : (
    <View />
  )
}

function newFunction(
  cityId: React.MutableRefObject<number>,
  props: { activeIndex: any },
  loaction: React.MutableRefObject<any>,
  setListData: React.Dispatch<React.SetStateAction<Submission[]>>,
  tabsData: React.MutableRefObject<TabsData>,
  setLoading: any,
  setIsFirstLoad: any
) {
  getUserId().then(userId => {
    const requestParams: SubmissionsQueryParams = {
      cityId: cityId.current,
      status: getStatus(props.activeIndex),
      lng: (loaction.current as any)?.lng,
      lat: (loaction.current as any)?.lat,
      pageNo: 1,
      size: pageSize,
      userId: userId,
      platform: isDP() ? 1 : 2
    }
    fetchSubmissions(requestParams as SubmissionsQueryParams)
      .then((data: QuerySubmissionResult) => {
        const newList = data?.resultList
        setListData(newList) // 更新页面
        setLoading(false)
        setIsFirstLoad(false) // 首次加载完成后设置为false
        tabsData.current = {
          list: newList,
          pageNum: 1,
          hasMore: data?.totalHit > newList?.length
        }
      })
      .catch(error => {
        console.error('error cautgh', error)
      })
  })
}

function loadmore(
  tabsData: React.MutableRefObject<TabsData>,
  activeIndex: number,
  setListData: (data: Submission[]) => void,
  // listData: Submission[],
  location: any,
  cityId: number
) {
  if (tabsData.current.hasMore) {
    getUserId().then(userId => {
      const newLocal = {
        lat: (location as any).lat,
        lng: (location as any).lng,
        status: 0,
        pageNo: ++tabsData.current.pageNum,
        size: pageSize,
        cityId: cityId,
        userId: userId,
        platform: isDP() ? 1 : 2
      }
      fetchSubmissions(newLocal)
        .then((data: QuerySubmissionResult) => {
          const newList = tabsData.current.list.concat(data.resultList)
          setListData(newList)
          // 更新缓存
          tabsData.current.hasMore = data?.totalHit > newList?.length
          tabsData.current.list = newList
        })
        .catch(error => {
          console.error('eror caught', error)
        })
    })
  }
}

function SubmissionCell(
  props: { activeIndex: any },
  index: number,
  item: Submission,
  length: number
): React.JSX.Element {
  const Tag = ({ status }: { status: number }): React.JSX.Element => {
    var mstyle = null
    let statusText
    switch (status) {
      case SubmissionStatus.inProcess:
        mstyle = style.submissionBgInProcess
        statusText = '审核中'
        break
      case SubmissionStatus.Reject:
        mstyle = style.submissionBgRejcet
        statusText = '审核驳回'
        break
      case SubmissionStatus.Work:
        mstyle = style.submissionBgWork
        statusText = '审核通过'
        break
    }
    return (
      <View style={mstyle}>
        <Text style={style.submissionStatus}>{statusText}</Text>
      </View>
    )
  }
  function analyzePic(pics: any[]): string {
    let picObj
    pics &&
      pics.length > 0 &&
      Array.isArray(pics) &&
      pics.forEach(p => {
        if (picObj) {
          p.tagType > picObj.tagType && (picObj = p)
          Number(picObj.tagType) === 3 &&
            extractNumbers(p.tagName)[0] > extractNumbers(picObj.tagName)[0] &&
            (picObj = p)
        } else {
          picObj = p
        }
      })
    return (picObj && picObj.url) || ''
  }
  const extractNumbers = str => {
    // 使用非数字字符分割字符串
    const parts = str.split(/\D+/)
    // 过滤掉空字符串并转换为数字
    return parts.filter(Boolean).map(Number)
  }

  return (
    <RecyclerView.Cell key={props.activeIndex + index}>
      <View style={style.recyclerViewCell}>
        <View style={style.directionRow}>
          {/* 优化成ImageBackGround */}
          <View style={style.imageBg}>
            <Image style={style.image} source={{ uri: analyzePic(item.pics) }} />
            <Tag status={item?.status} />
          </View>

          <View style={style.rightContent}>
            <Text style={style.mainText}>{item?.title}</Text>
            <View style={style.directionRow}>
              <Text style={style.subtext}>关联门店</Text>
              <Text style={style.subText2}>{' ' + item?.shopInfo.shopName}</Text>
            </View>
            <View style={style.directionRow}>
              <Text style={style.subtext}>合作医生</Text>
              <Text style={style.subText2}>{' ' + item?.technicianInfo?.technicianName}</Text>
            </View>
            <View style={style.directionRow}>
              <Text style={style.subtext}>投稿时间</Text>
              {
                item.addTime &&
                <Text style={style.subText2}>
                  {' ' + new Date(Number(item.addTime))
                  .toLocaleString()
                  .replace(/\//g, '-')}
                </Text>
              }

            </View>
          </View>
        </View>
        <View style={style.split} />
        {
          <View style={style.bottom}>
            {item.status !== SubmissionStatus.Reject && (
              <Text style={style.bottomText}>{item?.auditMsg}</Text>
            )}
            {/* 被驳回 4 */}
            {item.status === SubmissionStatus.Reject && (
              <Text style={style.bottomTextRebort}>{item?.auditMsg}</Text>
            )}
            {item.status === SubmissionStatus.Reject && (
              <LButton
                style={{ marginStart: 23 }}
                level="small"
                type="weak"
                onPress={() => {
                  redirectTo(
                    'dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-beauty-castomer-case&mrn_component=beautyCastomerCase&id=' +
                      item.itemId
                  )
                }}
                text={'去编辑'}
              />
            )}
          </View>
        }
      </View>
      {index === length - 1 && <View style={{ height: getSafeAreaBottom() }} />}
    </RecyclerView.Cell>
  )
}

function getStatus(activIndex: number) {
  switch (activIndex) {
    case 0:
      return undefined
    case 1:
      return SubmissionStatus.Work
    case 2:
      return SubmissionStatus.inProcess
    case 3:
      return SubmissionStatus.Reject
  }
}


function getUserId() {
  return new Promise((resolve, reject) => {
    MRNKNB.getUserInfo({
      success: (user: any) => {
        const that = this
        if (user.userId === '-1') {
          MRNKNB.login({
            success: res => {
              that.userId = Number(res.userId)
              resolve(Number(res.userId))
            },
            fail: e => {
              console.error(e)
              reject(e)
            }
          })
        } else {
          this.userId = user.userId
          resolve(user.userId)
        }
      },
      fail: e => {
        console.error(e)
        reject(e)
      }
    })
  })
}
