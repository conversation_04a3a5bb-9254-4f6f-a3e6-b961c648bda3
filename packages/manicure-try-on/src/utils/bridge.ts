
import KNB from '@mrn/mrn-knb'
interface ChooseImageOptions {
  count?: number;
  sizeType?: string[];
  source?: "gallery" | "camera" | undefined;
}
// msi ｜ max方案 调试有问题待解决
export const uploadFile = (resource: string) => {
  return new Promise((resolve, reject) => {
    KNB.uploadFile({
      sceneToken: 'dd-3035e7d8c8db5db5', // 受隐私合规中长期方案影响，新增sceneToken，权限如下关于参数的表格说明
      fileName: resource, // String 类型，必填，容器媒体协议或者文件路径
      contentType: 'image/*', // String 类型，选填，默认走 venus 上传图片，如果以 image 开头走 venus，其他场景走 S3
      serverInfo: {
        bucket: 'pilotimages', // 如果有 serverInfo 字段，该字段必填
        client: 'gcsdw9n4prdbf4f20000000000712728', // 如果有 serverInfo 字段，该字段必填
        // secret: 'lqx2eefTs******', // secret不能直接使用Venus的，需要使用https://m.sankuai.com/api/venus/encrypt?secret=${venus_secret}进行转换
        // secure: true, // 针对隐私的 bucket，开启安全访问模式，将多返回 token 字段用于访问图片，如果开启安全模式，client 和 secret 参数必填，否则将返回错误，默认为 false
        // maxAge: 3600, // 安全模式下图片保存时间
      }, // Object 类型，选填，上传账号信息
      success: function (result) {
        // url 是以 http://p0.meituan.net/xxx 开头的 venus 链接，业务如果需要可直接转为 https 的协议，venus 仍然支持访问
        // venusToken 为安全访问的 token，在安全模式下会返回
        const { url } = result
        console.log('url: ', url)
        resolve(url)
      },
      fail: function (reason) {
        // alert(JSON.stringify(reason))
        console.error('reason: ', reason)
        reject(reason)
      },
    })
  })
}
export const openCamera = (options: ChooseImageOptions) => {
  return new Promise((resolve, reject) => {
    KNB.chooseImage({
      sceneToken: 'dd-3035e7d8c8db5db5', // 受隐私合规中长期方案影响，新增sceneToken，权限如下关于参数的表格说明
      source: 'camera', //参数请查看下面参数说明，iOS和和安卓行为不一致， 图片类型：'gallery'相册, 'camera'相机，''相机相册
      count: 1, //可选,表示可以选择图片的最大数量,当type: camera时此参数无效。default: 9
      returnType: 'localId', //可选，指定返回类型, localId返回本地URL对应的具体图片。 base64:返回base64编码，返回值以data; image/jpeg; base64开头。default: localId
      ...options,
      success: function (result) {
        var photos = result.photoInfos // photoInfos是一个对象数组，每个对象包括以下内容
        const tempArr = photos.map((photo: any) => {
          return photo.localId
        })
        console.log('tempArr: ', tempArr)
        resolve(tempArr)
        // photos.forEach(function (photo) {
        //   var resource = photo.localId //返回本地资源id

        // })
      },
    })
  })
}


// export const openCamera = (options: any) => {
//   return new Promise((resolve, reject) => {
//     msi.chooseImage({
//       _mt: {
//         sceneToken: 'dd-fbcf2c5be739d354',
//         saveToSandbox: true,
//       },
//       count: 1,
//       success(res) {
//         const tempFilePaths = res.tempFilePaths;
//         msi.uploadFile({
//           url: 'https://pic-up.meituan.com/extrastorage/new/pilotimages', //仅为示例，非真实的接口地址
//           filePath: tempFilePaths[0],
//           name: 'file',
//           header: {
//             'Content-Type': 'multipart/form-data',
//             'client-id': 'pilotimages',
//             'token': 'AgEpI48xm06HT5jE_6K9DPkx1aZy_Sx9LRV41guTDyRF3yJPotvgGCa27UfNAHKEuYACwIJ22hl4_gAAAAD2KQAAMkiQQqXkDv2QbiuxjsBQ6bLN82xJyKGJTMNHBVSG4WATGhb8MXwp2Vbj27ZZpB8-'
//           },
//           success(res) {
//             const data = res.data;
//             console.log(res);
//           },
//           fail(err) {
//             console.log(err);
//           }
//         })
//       }
//     })
//   })
// }
