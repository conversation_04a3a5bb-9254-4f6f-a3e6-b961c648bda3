import { getStorage, setStorage } from '@max/meituan-uni-storage';

export interface IExample {
  url: string
  des: string
  icon: string
}

export const exampleSuccessImg: IExample[] = [
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '单人清晰正脸',
    icon: 'https://p0.meituan.net/ingee/8b6ec80948f01856e46082dc617b892f728.png'
  },
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '保持面部居中',
    icon: 'https://p0.meituan.net/ingee/8b6ec80948f01856e46082dc617b892f728.png'
  },
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '光线充足',
    icon: 'https://p0.meituan.net/ingee/8b6ec80948f01856e46082dc617b892f728.png'
  }
]

export const exampleErrorImg: IExample[] = [
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '非正面/模糊',
    icon: 'https://p0.meituan.net/ingee/8f0dc307081abc11e92df30145ebf2aa685.png'
  },
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '面部有遮挡',
    icon: 'https://p0.meituan.net/ingee/8f0dc307081abc11e92df30145ebf2aa685.png'
  },
  {
    url: 'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg',
    des: '光线过暗',
    icon: 'https://p0.meituan.net/ingee/8f0dc307081abc11e92df30145ebf2aa685.png'
  }
]

export const configKey = 'HINT_MODAL_KEY'

export const openHintModal = (config) => {
  return new Promise((resolve, reject)=>{
    getStorage({
      key: configKey,
      _meituan: {
        shareConfig: {
          shared: true
        }
      },
      ...config
    }).then((res) => {
      if (res.data) {
        setStorage({
          key: configKey,
          _meituan: {
            shareConfig: {
              shared: true
            }
          },
          ...config
        })
        resolve(false)
      } else {
        resolve(true)
      }
    })
    .catch(err => {
      console.log('err',err)
    })
  })
}