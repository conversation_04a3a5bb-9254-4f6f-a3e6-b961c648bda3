import { AppRegistry, Text, TextInput } from '@mrn/react-native';
import Home from './pages/Home/index';
import Detail from './pages/Detail/index';
import Photo from './pages/Photo/index';

// 设置Text字体不随系统字体大小改变
try {
  // @ts-ignore
  Text.defaultProps = Text.defaultProps || {};
  // @ts-ignore
  Text.defaultProps.allowFontScaling = false;
} catch (error) {
  console.log(error);
}

// 设置TextInput字体不随系统字体大小改变
try {
  // @ts-ignore
  TextInput.defaultProps = TextInput.defaultProps || {};
  // @ts-ignore
  TextInput.defaultProps.allowFontScaling = false;
} catch (error) {
  console.log(error);
}

// 试款主页
AppRegistry.registerComponent('manicure-try-on-home', () => Home);
// 试款详情页
AppRegistry.registerComponent('manicure-try-on-detail', () => Detail);
// 试款拍照页/相册页
AppRegistry.registerComponent('manicure-try-on-photo', () => Photo);
