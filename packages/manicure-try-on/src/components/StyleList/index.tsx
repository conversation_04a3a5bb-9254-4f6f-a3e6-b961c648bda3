import { createElement, memo, useState, useCallback, useEffect } from '@max/max';
import ScrollView from '@hfe/max-scrollview';
import { remStyleSheet, toRem } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Image from '@hfe/max-image';
import Text from '@hfe/max-text';
import { StyleImagesItems } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_styles';

// 直接使用API返回的类型
export type StyleImage = StyleImagesItems;

interface StyleListProps {
  // 发型数据列表
  list?: StyleImage[];
  // 当前选中的索引
  selectedIndex?: number;
  // 发型选择回调
  onStyleSelect?: (item: StyleImage, index: number) => void;
}

function StyleList(props: StyleListProps) {
  const {
    list = [],
    selectedIndex = 0,
    onStyleSelect
  } = props;

  const [currentSelectedIndex, setCurrentSelectedIndex] = useState(selectedIndex);

  // 同步外部选中状态
  useEffect(() => {
    setCurrentSelectedIndex(selectedIndex);
  }, [selectedIndex]);

  // 处理发型选择
  const handleStyleSelect = useCallback((item: StyleImage, index: number) => {
    if (index === currentSelectedIndex) return;

    setCurrentSelectedIndex(index);
    onStyleSelect?.(item, index);
  }, [currentSelectedIndex, onStyleSelect]);

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        horizontal
        showsHorizontalScrollIndicator={false}
      >
        <View style={{ width: 15 }} />
        <View
          style={[styles.optionItem, { width: 52 }]}
          onClick={() => handleStyleSelect({}, 0)}
        >
          <View style={styles.originalImageContainer}>
            {currentSelectedIndex === 0 ? (
              <View style={styles.orginalSelectedIndicator}>
                <Image
                  style={styles.checkIcon}
                  source={{ uri: 'https://p0.meituan.net/joyplaystatic/895221a7d1ca3b91d0ecce12d05ab04c1110.png' }}
                />
              </View>
            ) : (
              <View style={styles.originalImagePlaceholder}>
                <Image
                  style={styles.disabledIcon}
                  source={{ uri: 'https://p0.meituan.net/joyplaystatic/03a3ee04192d638227d5b6bc2176844e2106.png' }}
                />
              </View>
            )}
          </View>
          <Text style={[styles.styleName, currentSelectedIndex === 0 && styles.styleNameSelected]}>
            原图
          </Text>
        </View>
        {list.map((item: StyleImage, index: number) => {
          const actualIndex = index + 1; // 因为原图占了第0个位置
          const isSelected = actualIndex === currentSelectedIndex;
          return (
            <View
              key={item.styleImageUrl + `-${index}`}
              style={styles.optionItem}
              onClick={() => handleStyleSelect(item, actualIndex)}
            >
              <View style={{ marginBottom: 6 }}>
                <Image
                  style={styles.styleImage}
                  source={{ uri: item.styleImageUrl || '' }}
                  resizeMode="cover"
                />
                {isSelected && (
                  <View style={styles.selectedIndicator}>
                    <Image
                      style={styles.checkIcon}
                      source={{ uri: 'https://p0.meituan.net/joyplaystatic/895221a7d1ca3b91d0ecce12d05ab04c1110.png' }}
                    />
                  </View>
                )}
              </View>
              <Text style={[styles.styleName, isSelected && styles.styleNameSelected]}>
                {item.title || ''}
              </Text>
            </View>
          );
        })}
        <View style={{ width: 7 }} />
      </ScrollView>
    </View>
  );
}

export default memo(StyleList);

const styles = remStyleSheet({
  container: {
    flex: 1,
    marginTop: -12,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: '#fff',
    paddingTop: 15,
    overflow: 'hidden',
    zIndex: 1,
  },
  scrollView: {
    width: '100%',
  },
  optionItem: {
    alignItems: 'center',
    width: 76,
    marginRight: 8,
  },
  styleImage: {
    width: 76,
    height: 76,
    borderRadius: 6,
    overflow: 'hidden',
  },
  originalImageContainer: {
    width: 51,
    height: 76,
    marginBottom: 6,
  },
  originalImagePlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    borderRadius: 6,
    backgroundColor: '#eee',
  },
  orginalSelectedIndicator: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    borderRadius: 6,
    backgroundColor: '#FFF0F9',
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: '#EB2F93',
  },
  disabledIcon: {
    width: 24,
    height: 24,
  },
  selectedIndicator: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
    borderWidth: toRem(1),
    borderColor: '#EB2F93',
    backgroundColor: 'rgba(0, 0, 0, 0.4)'
  },
  checkIcon: {
    width: 40,
    height: 40,
  },
  styleName: {
    color: '#555555',
    fontSize: 12,
  },
  styleNameSelected: {
    color: '#FF5093',
    fontWeight: '500',
  },
})
