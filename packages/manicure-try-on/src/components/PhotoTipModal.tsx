import { createElement, memo, useCallback, useEffect, useState } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Image from '@hfe/max-image';
import Text from '@hfe/max-text';

import ModalBaseContainer from '@max/leez-modal-base-container'
import TopView from '@max/leez-top-view';
import Icon from '@max/leez-icon';
import SlideModal from '@max/leez-slide-modal';
import LinearGradient from '@hfe/max-linear-gradient';
import { exampleSuccessImg, exampleErrorImg, openHintModal } from '@/utils/util';
import { getWidth } from '@mrn/mrn-gc-utils'

const configKey = 'HINT_MODAL_KEY'
interface Props {
  position: 'center' | 'down'
}

const styles = remStyleSheet({
  modalWrap: {
    width: 331,
    borderRadius: 16,
    backgroundColor: '#fff',
    paddingTop: 15,
    paddingBottom: 18,
    paddingHorizontal: 18
  },
  title: {
    fontSize: 15,
    fontWeight: '500',
    fontFamily: 'PingFang SC',
    color: '#111',
    marginBottom: 10
  },
  photoRow: {
    flexDirection: 'row',
    justifyContent: "space-between",
    marginBottom: 6
  },
  photo: {
    width: 86,
    height: 86,
    borderRadius: 8,
    overflow: "hidden"
  },
  btnRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    width: '100%'
  },
  btn: {
    flex: 1,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F5F5F5',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 6
  },
  btnPrimary: {
    color: '#fff'
  },
  btnText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#222'
  },
  btnTextPrimary: {
    color: '#fff'
  },
  saveBtn: {
    width: '100%',
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    marginTop: 7
  },
  saveBtnGradient: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveBtnText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: 'bold',
    fontFamily: "PingFang SC"
  },
  exampleItemWrap: {
    alignItems: 'center',
  },
  exampleItemDescWrap: {
    marginTop: 3,
    flexDirection: 'row',
    justifyContent: 'center',
    alignSelf: 'center',
    alignItems: 'center'
  },
  exampleItemIcon: {
    width: 11,
    height: 11,
    marginRight: 2,
    resizeMode: 'contain',
  },
  exampleItemDescText: {
    fontSize: 12,
    color: '#555',
    fontFamily: 'PingFang SC',
  },
  modalHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  closeBtn: {
    marginTop: -15,
    marginRight: -18,
  },
  errorTitle: {
    marginTop: 3,
  },
})

const PhotoTipModal = (props: Props) => {

  const { position = 'down' } = props

  const [visible, setVisible] = useState(true)

  const onClose = useCallback(() => setVisible(false), [])

  useEffect(() => {
    openHintModal({
      key: configKey,
    }).then(
      (res) => {
        setVisible(Boolean(res))
      }
    ).catch(err => {
      console.log('err', err)
    })
  }, [])

  const ExampleItem = useCallback((item, i, isDown) => {
    const itemSize = (getWidth() - 32 - 24) / 3
    return (
      <View style={styles.exampleItemWrap} key={i}>
        <View style={[styles.photo, isDown && {
          width: itemSize,
          height: itemSize,
          marginLeft: i ? 12 : 0
        }]}>
          <Image key={i} source={{ uri: item.url }} style={{ flex: 1 }} />
        </View>
        <View style={styles.exampleItemDescWrap}>
          <Image source={{ uri: item.icon }} style={styles.exampleItemIcon} />
          <Text style={styles.exampleItemDescText}>{item.des}</Text>
        </View>
      </View>
    )
  }, [])


  const renderContent = useCallback(() => {
    const isDown = position === 'down'
    return <View style={[styles.modalWrap, isDown && {
      width: '100%',
      paddingHorizontal: 0,
      paddingBottom: 0
    }]}>
      <View style={styles.modalHeaderRow}>
        <Text style={styles.title}>{isDown ? '正确照片' : '正确照片示例'}</Text>
        {!isDown && <View
          onClick={() => onClose()}
          style={styles.closeBtn}
        >
          <Icon name={'guanbixian'} type={'extraBody1'} size={32} />
        </View>}
      </View>
      <View style={styles.photoRow}>
        {exampleSuccessImg.map((item, i) => ExampleItem(item, i, isDown))}
      </View>
      <Text style={[styles.title, styles.errorTitle]}>{isDown ? '错误照片' : '错误照片示例'}</Text>
      <View style={styles.photoRow}>
        {exampleErrorImg.map((item, i) => ExampleItem(item, i, isDown))}
      </View>
      <View style={styles.saveBtn} onClick={() => onClose()}>
        <LinearGradient
          colors={['#FF8FB6', '#FF3C87']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.saveBtnGradient}
        >
          <Text style={styles.saveBtnText}>我知道了，开始拍摄</Text>
        </LinearGradient>
      </View>
    </View>
  }, [])

  return (
    <TopView>
      {Boolean(position === 'center') && <ModalBaseContainer
        visible={visible}
        useRNModal={false}
        type="fade"
      >
        {renderContent()}
      </ModalBaseContainer>}
      {Boolean(position === 'down') && <SlideModal
        visible={visible}
        title={'照片示例'}
        modalMaxHeight={'70%'}
        maskClosable
        contentUseScrollview={true}
        onClosePress={() => onClose()}
      >
        {renderContent()}
      </SlideModal>}
    </TopView>

  )
}

export default memo(PhotoTipModal)
