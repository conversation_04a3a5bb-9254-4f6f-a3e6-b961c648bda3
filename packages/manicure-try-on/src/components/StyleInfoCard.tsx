import { memo, useCallback } from '@max/max';
import { remStyleSheet, toRem } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Text from '@hfe/max-text';

interface StyleInfoCardProps {
  style?: any;
  // 款式信息数据
  title?: string;
  tags?: string[];
}

function StyleInfoCard(props: StyleInfoCardProps) {
  const { style, title, tags } = props;

  const handleSaveImage = useCallback(() => {
    console.log('保存图片');
  }, []);

  return (
    <View style={[styles.container, style]}>
      <View style={styles.content}>
        <View style={styles.infoSection}>
          {/* 款式名称 */}
          <Text style={styles.styleName}>
            {title || ''}
          </Text>
          {/* 标签 */}
          <View style={styles.tags}>
            {tags?.map((tag, index) => (
              <View style={[styles.tagWrap, { marginLeft: index > 0 ? 4 : 0 }]} key={tag}>
                <Text style={styles.tagText}>
                  {tag}
                </Text>
              </View>
            ))}
          </View>
        </View>
        {/* 保存图片按钮 */}
        <View style={styles.saveButton} onClick={handleSaveImage}>
          <Text style={styles.saveButtonText}>保存图片</Text>
        </View>
      </View>
    </View>
  );
}

const styles = remStyleSheet({
  container: {
    justifyContent: 'center',
    width: '100%',
    paddingHorizontal: 15,
  },
  content: {
    backgroundColor: '#FFFFFFE5',
    borderWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#FFFFFF',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  tags: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  infoSection: {
    flex: 1,
    marginRight: 12,
  },
  styleName: {
    color: '#222222',
    fontWeight: '500',
    fontSize: 12,
    marginBottom: 4,
  },
  tagWrap: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 7,
    paddingVertical: 2,
    borderRadius: 10,
    backgroundColor: '#FF50931A',
  },
  tagText: {
    color: '#FF3C87',
    fontSize: 10,
  },
  saveButton: {
    height: 20,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: toRem(0.5),
    borderStyle: 'solid',
    borderColor: '#EB2F93',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 8,
  },
  saveButtonText: {
    color: '#EB2F93',
    fontSize: 11,
  },
});

export default memo(StyleInfoCard);
