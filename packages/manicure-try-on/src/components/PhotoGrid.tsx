// import { createElement, memo, useCallback } from '@max/max';
// import { remStyleSheet } from '@max/leez-style-util';
// import View from '@hfe/max-view';
// import Image from '@hfe/max-image';
// import Text from '@hfe/max-text';

// import { getWidth } from '@mrn/mrn-gc-utils';
// import Checkbox from '@max/leez-checkbox';

// interface Props {
//   photos?: any[],
//   selected?: boolean,
//   onToggle?: () => void
// }

// // const PHOTO_SIZE = (getWidth() - 18 * 2 - 3 * 8) / 4; // 4列，左右边距18，间距8

// const PHOTO_GAP = 8;

// const PhotoGrid: React.FC<Props> = (props) => {
//   const {photos, selected, onToggle} = props
//   return null
//   return (
//     <FlatList
//       data={photos}
//       renderItem={({ item, index }) => {
//         let checked = selected === item.id
//         return (
//           <View style={styles.photoItem}
//             onClick={() => {
//               onToggle(item)
//             }
//           }
//           >
//             <View style={[styles.checkBoxWrap]}>
//               <Checkbox checked={checked}
//                 sizeType={'level2'}
//                 borderColor={'#ffffff00'}
//                 theme={
//                   {
//                     enabled: {
//                       checked: {
//                         bgColor: ['#FF8FB6', '#FF3C87']
//                       },
//                       unchecked: {
//                         bgColor: "#ffffff20",
//                       },
//                     }
//                   }
//                 }
//                 style={
//                   Boolean(!checked) && {
//                     borderWidth: 1,
//                     borderColor: "#fff"
//                   }
//                 }
//               />
//             </View>
//             <Image source={{ uri: item.url }} style={styles.photo} />
//           </View>
//         )
//       }}
//       keyExtractor={item => item.id}
//       numColumns={4}
//       columnWrapperStyle={styles.row}
//       contentContainerStyle={styles.grid}
//       showsVerticalScrollIndicator={false}
//     />
//   )
// };


// const styles = remStyleSheet({
//   safeArea: {
//     backgroundColor: '#fff',
//   },
//   container: {
//     flex: 1,
//     backgroundColor: '#fff',
//     paddingHorizontal: 18,
//     paddingTop: 0,
//   },
//   header: {
//     height: 56,
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     paddingTop: 12,
//     marginBottom: 8,
//   },
//   backBtn: {
//     width: 40,
//     height: 40,
//     justifyContent: 'center',
//     alignItems: 'flex-start',
//   },
//   backIcon: {
//     fontSize: 22,
//     color: '#222',
//     fontWeight: 'bold',
//   },
//   title: {
//     fontSize: 18,
//     fontWeight: 'bold',
//     color: '#222',
//     flex: 1,
//     textAlign: 'center',
//   },
//   confirmBtn: {
//     backgroundColor: '#FF6A9A',
//     borderRadius: 16,
//     paddingHorizontal: 18,
//     paddingVertical: 6,
//   },
//   confirmText: {
//     color: '#fff',
//     fontSize: 15,
//     fontWeight: 'bold',
//   },
//   grid: {
//     paddingBottom: 24,
//   },
//   row: {
//     flexDirection: 'row',
//     justifyContent: 'flex-start',
//     marginBottom: PHOTO_GAP,
//   },
//   photoItem: {
//     // width: PHOTO_SIZE,
//     // height: PHOTO_SIZE,
//     marginRight: PHOTO_GAP,
//     marginBottom: 0,
//     borderRadius: 12,
//     overflow: 'hidden',
//     backgroundColor: '#f7f7f7',
//     position: "relative"
//   },
//   photo: {
//     width: '100%',
//     height: '100%',
//     borderRadius: 12,
//   },
//   checkBoxWrap: {
//     position: "absolute",
//     zIndex: 1,
//     right: 6,
//     top: 6,
//   },
//   checkCircle: {
//     position: 'absolute',
//     top: 6,
//     right: 6,
//     width: 24,
//     height: 24,
//     borderRadius: 12,
//     backgroundColor: 'rgba(255,255,255,0.9)',
//     justifyContent: 'center',
//     alignItems: 'center',
//     borderWidth: 1,
//     borderColor: '#FF6A9A',
//   },
//   checked: {
//     width: 14,
//     height: 14,
//     borderRadius: 7,
//     backgroundColor: '#FF6A9A',
//   },
//   unchecked: {
//     width: 14,
//     height: 14,
//     borderRadius: 7,
//     borderWidth: 1.5,
//     borderColor: '#FF6A9A',
//     backgroundColor: '#fff',
//   },
// });

// export default React.memo(PhotoGrid)
