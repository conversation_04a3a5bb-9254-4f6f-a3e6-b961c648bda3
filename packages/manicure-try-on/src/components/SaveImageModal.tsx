import { createElement, memo, useCallback } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Text from '@hfe/max-text';
import Image from '@hfe/max-image';
import ModalBaseContainer from '@max/leez-modal-base-container';

interface SaveImageModalProps {
  // 是否显示弹窗
  visible: boolean;
  // 关闭弹窗回调
  onClose: () => void;
  // 图片URL
  imageUrl?: string;
}

function SaveImageModal(props: SaveImageModalProps) {
  const { visible, onClose, imageUrl = '' } = props;

  const handleSave = useCallback(() => {
    // TODO: 实现保存图片逻辑
    console.log('保存图片:', imageUrl);
    onClose();
  }, [imageUrl, onClose]);

  return (
    <ModalBaseContainer
      visible={visible}
      useRNModal={false}
      type="fade"
      onMaskPress={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          {/* 关闭按钮 */}
          <View style={styles.closeButton} onClick={onClose}>
            <Text style={styles.closeButtonText}>×</Text>
          </View>
          
          {/* 图片预览 */}
          <View style={styles.imageContainer}>
            <Image
              style={styles.previewImage}
              source={{ uri: imageUrl }}
              resizeMode="cover"
            />
          </View>
          
          {/* 保存按钮 */}
          <View style={styles.saveButton} onClick={handleSave}>
            <Text style={styles.saveButtonText}>保存至相册</Text>
          </View>
        </View>
      </View>
    </ModalBaseContainer>
  );
}

const styles = remStyleSheet({
  modalContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    maxWidth: '80%',
    maxHeight: '80%',
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    borderRadius: 15,
    zIndex: 1,
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666',
    fontWeight: 'bold',
  },
  imageContainer: {
    marginTop: 20,
    marginBottom: 20,
  },
  previewImage: {
    width: 200,
    height: 200,
    borderRadius: 8,
  },
  saveButton: {
    backgroundColor: '#FF3C87',
    borderRadius: 25,
    paddingHorizontal: 30,
    paddingVertical: 12,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default memo(SaveImageModal);
