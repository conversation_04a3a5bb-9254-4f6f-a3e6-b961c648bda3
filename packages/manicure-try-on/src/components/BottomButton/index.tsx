import { createElement, memo, useCallback, useMemo } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import LText from '@max/leez-text';
import Image from '@hfe/max-image';
// import { openUrl } from '@max/meituan-uni-navigate';
import { LinearGradient } from '@mrn/react-native-linear-gradient';
import { ButtonInfo } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_styles';

interface BottomButtonProps {
  data?: ButtonInfo | null;
}

function BottomButton(props: BottomButtonProps) {
  const { data } = props;

  const handlePress = useCallback(() => {
    if (data?.jumpUrl) {
      // openUrl(data.jumpUrl);
    }
  }, [data?.jumpUrl]);

  // 处理模板内容，将高亮部分替换
  const getFormattedContent = useCallback(() => {
    if (!data?.content || !data?.highlight) {
      return data?.content || '';
    }
    return data.content.replace('%s', data.highlight);
  }, [data?.content, data?.highlight]);

  const getContentList = useMemo(() => {

  }, [data?.content]);

  if (!data?.jumpUrl) {
    return <View style={styles.container} />
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        colors={['#FF8FB6', '#FF3C87']}
        style={styles.button}
      >
        <View style={styles.buttonContent} onClick={handlePress}>
          {/* 提示标签 */}
          {data.tips && (
            <View style={styles.tipsContainer}>
              <LText style={styles.tipsText}>{data.tips}</LText>
            </View>
          )}

          {/* 主要内容 */}
          <Image
            source={{ uri: 'https://p0.meituan.net/joyplaystatic/4f24bddc1e0309edf96792f29a273712565.png' }}
            style={styles.iconCircle}
          />

          <LText style={styles.titleText}>{data.title || ''}</LText>
          <View style={styles.line} />
          <LText style={styles.contentText}>
            {getFormattedContent()}
          </LText>
        </View>
      </LinearGradient>
    </View>
  )
}

const styles = remStyleSheet({
  container: {
    minHeight: 48,
    marginTop: 24,
    paddingHorizontal: 15,
  },
  button: {
    position: 'relative',
    borderRadius: 25,
    height: 48,
    shadowColor: 'rgba(255, 20, 147, 0.3)',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 8,
  },
  buttonContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  tipsContainer: {
    position: 'absolute',
    top: -8,
    right: 12,
    backgroundColor: '#FF8C00', // 橙色背景
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 2,
    zIndex: 1,
  },
  tipsText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '500',
  },
  iconCircle: {
    width: 20,
    height: 20,
    marginRight: 6,
    borderRadius: 10,
    overflow: 'hidden',
  },
  textContainer: {
    flex: 1,
  },
  titleText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
    marginBottom: 2,
  },
  contentText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  line: {
    width: 0.5,
    height: 14,
    marginHorizontal: 12,
    backgroundColor: '#fff',
  },
});

export default memo(BottomButton);
