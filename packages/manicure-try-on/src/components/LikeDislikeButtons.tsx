import { createElement, memo, useCallback, useMemo, useState, useEffect } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Image from '@hfe/max-image';
import Toast from '@max/leez-toast';
import { REQUEST_PLATFORM } from '@/utils';

import { MRN_GET__dzim_pilot_assistant_hair_task_like } from '@APIs/MRN_GET__dzim_pilot_assistant_hair_task_like';
import { MRN_GET__dzim_pilot_assistant_beauty_task_like_query } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_task_like_query';

interface LikeDislikeButtonsProps {
  // 任务ID，用于点赞点踩接口
  taskId?: number;
}

enum Operation {
  LIKE = 1,
  DISLIKE = 2,
  CANCEL = 3,
}

function LikeDislikeButtons(props: LikeDislikeButtonsProps) {
  const { taskId = 0 } = props;
  const [operation, setOperation] = useState<Operation>(Operation.CANCEL);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // 处理点赞 点踩 取消
  const handleOperation = useCallback(async (nextOperation: Operation) => {
    if (isLoading) return;
    setIsLoading(true);
    const currentOperation = operation;
    try {
      if (taskId) {
        setOperation(nextOperation);
        const result = await MRN_GET__dzim_pilot_assistant_hair_task_like({
          taskId,
          platform: REQUEST_PLATFORM,
          operation: nextOperation, // 1-点赞
        });
        if (result) {
          Toast.open({ title: '感谢你的反馈' });
        } else {
          setOperation(currentOperation);
        }
      }
    } catch (error) {
      console.warn('点赞/点踩失败:', error);
      setOperation(currentOperation);
    } finally {
      setIsLoading(false);
    }
  }, [taskId, operation, isLoading]);

  const likeIcon = useMemo(() => {
    if (operation === Operation.LIKE) {
      return 'https://p0.meituan.net/ingee/682a43c32eda28aa5f3673628fcbc529678.png';
    }
    return 'https://p0.meituan.net/ingee/fa926c22d3610d38ae830736c16ae918548.png';
  }, [operation]);

  const dislikeIcon = useMemo(() => {
    if (operation === Operation.DISLIKE) {
      return 'https://p0.meituan.net/ingee/962b477742b5b8885a674a7a7586ba8d677.png';
    }
    return 'https://p0.meituan.net/ingee/c8b5a93493e04cc1cd55ad63f247e099539.png';
  }, [operation]);

  useEffect(() => {
    if (taskId) {
      MRN_GET__dzim_pilot_assistant_beauty_task_like_query({
        taskId,
        platform: REQUEST_PLATFORM,
      }).then((res) => {
        if (res?.status) {
          setOperation(res.status as Operation);
        }
      });
    }
  }, [taskId]);

  return (
    <View style={styles.container}>
      {/* 点赞按钮 */}
      <View style={styles.button} onClick={() => handleOperation(operation === Operation.LIKE ? Operation.CANCEL : Operation.LIKE)}>
        <Image
          style={styles.icon}
          source={{ uri: likeIcon }} // 暂时使用空字符串占位
          resizeMode="contain"
        />
      </View>

      {/* 分割线 */}
      <View style={styles.line} />

      {/* 点踩按钮 */}
      <View style={styles.button} onClick={() => handleOperation(operation === Operation.DISLIKE ? Operation.CANCEL : Operation.DISLIKE)}>
        <Image
          style={styles.icon}
          source={{ uri: dislikeIcon }} // 暂时使用空字符串占位
          resizeMode="contain"
        />
      </View>
    </View>
  );
}

const styles = remStyleSheet({
  container: {
    position: 'absolute',
    top: 12,
    right: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 7,
    zIndex: 10,
    borderRadius: 20,
    backgroundColor: '#00000066',
    borderWidth: 0.5,
    borderStyle: 'solid',
    borderColor: '#FFFFFF',
  },
  button: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    width: 14,
    height: 14,
  },
  line: {
    width: 1,
    height: 16,
    marginHorizontal: 10,
    backgroundColor: '#FFFFFF',
    opacity: 0.5,
  },
});

export default memo(LikeDislikeButtons);
