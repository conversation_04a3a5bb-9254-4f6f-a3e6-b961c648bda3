.tab {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  padding: 0 40rpx;
}

.tabItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100rpx;
  height: 100rpx;
  background-color: #fff0f0;
  border-radius: 8rpx;
  // padding: 10rpx;
  transition: all 0.3s ease;
}

.tabItemActive {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100rpx;
  height: 100rpx;
  background-color: #bbe0ff;
}

.tabItemIcon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: rgb(237, 176, 205);
  transition: background-color 0.3s ease;
}
