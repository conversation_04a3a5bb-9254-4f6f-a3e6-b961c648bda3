import { createElement, memo, useState } from '@max/max';
import View from '@hfe/max-view';
import Text from '@hfe/max-text'; 
// 工具函数导入
import styles from './index.module.scss';

// 样式导入

interface StyleTabProps {
  onTabChange?: (item: string) => void;
}

function StyleTab(props: StyleTabProps) {
  const { onTabChange } = props;
  const [activeIndex, setActiveIndex] = useState(0);

  const handleTabClick = (item: string, index: number) => {
    setActiveIndex(index);
    onTabChange?.(item); // 调用从父组件传入的回调函数
  };

  const renderTabItem = (item, index) => {
    const isActive = activeIndex === index;
    return (
      <View className={isActive ? styles.tabItemActive : styles.tabItem} onClick={() => handleTabClick(item, index)}>
        <View className={styles.tabItemIcon}></View>
        <Text className={isActive ? styles.tabTextActive : ''}>试发型</Text>
      </View>
    );
  }

  return <View className={styles.tab}>
    {
      ['hair', 'manicure', 'color'].map((item, index) => {
        return renderTabItem(item, index);
      })
    }
  </View>;
}

export default memo(StyleTab);
