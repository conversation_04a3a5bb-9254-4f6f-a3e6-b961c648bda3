import { memo } from '@hfe/max';
import View from '@hfe/max-view';
import MaxVideoView from '@hfe/max-video-view';
import { createElement, useRef } from '@max/max';

interface VideoProps {
  wrapperStyle?: any;
}

const Video = memo((props: VideoProps) => {
  const { wrapperStyle } = props;
  const ref = useRef(null);
  return (
    <View style={wrapperStyle}>
      <MaxVideoView
        ref={ref}
        moduleName="max-video-view"
        autoplay
        style={{ width: '100%', height: '100%' }}
        displayMode={0}
        repeat={true}
        mute={true}
        videoUrl="https://1251413404.vod2.myqcloud.com/vodcq1251413404/387702306192692418/F19vfylJSpoA.mp4"
      />
    </View>
  );
});

export default Video;
