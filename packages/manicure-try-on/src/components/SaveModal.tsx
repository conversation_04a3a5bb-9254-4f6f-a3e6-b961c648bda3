import { createElement, memo, useCallback } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Image from '@hfe/max-image';
import Text from '@hfe/max-text';

import ModalBaseContainer from '@max/leez-modal-base-container'
import { saveImageToPhotosAlbum } from '@max/meituan-uni-image';
import { downloadFile } from '@max/meituan-uni-network';
import LinearGradient from '@hfe/max-linear-gradient';
import Toast from '@max/leez-toast';

interface Props {
  // 是否显示弹窗
  visible: boolean;
  // 关闭弹窗回调
  onClose: () => void;
  // 图片URL
  imageUrl?: string;
}

const SaveModal = (props: Props) => {
  const { visible, imageUrl, onClose } = props
  // 保存图片到相册
  const handleSave = useCallback(async () => {
    try {
      const res = await downloadFile({ url: imageUrl || '' });
      if (res && res.tempFilePath) {
        saveImageToPhotosAlbum({
          _meituan: {
            sceneToken: 'dd-20f752dd436d1949',
          },
          filePath: res.tempFilePath
        }).then((e) => {
          Toast.open({
            title: '保存成功',
            content: "请在我的相册中查看",
          })
          onClose()
        }).catch((err) => {
          Toast.open({
            title: '保存失败',
            content: err.errMsg,
          })
        });
      }
    } catch (err) {
      console.log('err', err)
    }
  }, [onClose])

  return (
    <ModalBaseContainer
      visible={visible}
      useRNModal={false}
      type="fade"
      onMaskPress={onClose}
    >
      <View style={styles.modalBox}>
        <View style={styles.closeBtn} onClick={onClose}>
          <Image source={{ uri: 'https://p0.meituan.net/ingee/573b9d2b4caab1a8f84aefdbceedf40b1292.png' }} style={styles.closeIcon} />
        </View>
        <Image source={imageUrl ? { uri: imageUrl } : { uri: '' }} style={styles.avatar} />
        <View style={styles.saveBtn} onClick={handleSave}>
          <LinearGradient
            colors={['#FF8FB6', '#FF3C87']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.saveBtnGradient}
          >
            <Text style={styles.saveBtnText}>保存至相册</Text>
          </LinearGradient>
        </View>
      </View>
    </ModalBaseContainer>
  );
};

const styles = remStyleSheet({
  modalBox: {
    width: 338,
    backgroundColor: '#fff',
    paddingHorizontal: 21,
    paddingBottom: 21,
    borderRadius: 12,
    alignItems: 'center',
    position: 'relative',
  },
  closeBtn: {
    position: 'absolute',
    top: -31,
    right: 0,
    zIndex: 2
  },
  closeIcon: {
    width: 24,
    height: 24
  },
  avatar: {
    width: 302,
    height: 402.5,
    borderRadius: 12,
    marginTop: 18,
    marginBottom: 15,
    marginHorizontal: 18,
    resizeMode: 'cover'
  },
  saveBtn: {
    width: 299,
    height: 40,
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  saveBtnGradient: {
    flex: 1,
    width: '100%',
    height: '100%',
    borderRadius: 26,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveBtnText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: 'bold',
    fontFamily: "PingFang SC"
  },
  shareRow: {
    flexDirection: 'row',
    width: 299,
    marginTop: 18,
  },
  channelItem: {
    alignItems: 'center',
    width: 64
  },
  channelIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    overflow: 'hidden',
    marginBottom: 8
  },
  channelName: {
    fontSize: 12,
    color: '#666'
  }
});

export default memo(SaveModal)
