import { createElement, useState, useCallback, memo, useMemo, useEffect, useRef } from '@max/max';
import View from '@hfe/max-view';
import LText from '@max/leez-text';
import NavBar from '@/components/common/NavBar';
import StyleTab from '@/components/StyleTab';
import ChangeStyle, { ChangeStyleHandles } from '@/pages/Home/component/ChangeStyle';
import NavigationBar from '@max/leez-navigation-bar';
import LinearGradient from '@hfe/max-linear-gradient';

import { navigateBack } from '@max/meituan-uni-navigate';

// Leez接入： https://km.sankuai.com/collabpage/1744524702
import Button from '@hfe/max-button';
import { styleImagesMap } from './const';
import { openCamera } from '@/utils/bridge';
import { Animated } from '@max/mbc-animated';
import Video from '@/components/Video';
import { request } from '@max/meituan-uni-network';
import { remStyleSheet } from '@max/leez-style-util';

function Home() {
  //  新增，设置主题(餐和综可省略这一步)
  const [styleImages, setStyleImages] = useState(styleImagesMap.hair);

  const changeStyleRef = useRef<ChangeStyleHandles>(null);

  const handleTabChange = useCallback((item: string) => {
    console.log('Tab changed, stopping animation.', styleImages);
    setStyleImages(styleImagesMap[item]);
    changeStyleRef.current?.stopAnimation();
  }, []);

  useEffect(() => {
    console.log('HHHHHome');
    // 新版网络请求（版本 >= 2.0.0），MRN和WEB端默认会接入ELink, 并开启上报开关。文档：https://km.sankuai.com/collabpage/2193577916
    // 注意：web端上报，需要在build.js的define字段中定义owl: {catKey: 'xxx'}
    request({
      url: 'https://yapi.sankuai.com/mock/14789/trip/list',
      method: 'GET',
      _meituan: {
        // MRN端可以在这关闭ELink 日志上报
        // enableElinkLog: false,
        mrnChannel: 'travel',
        requestType: 'MRNUtils',
      },
      _web: {
        // Web端可以在这关闭ELink 日志上报
        // enableElinkLog: false,
      },
    })
      .then((res) => {
        console.log('请求数据成功', res);
      })
      .catch((err) => {
        console.log('请求数据失败', err);
      });
  }, []);

  const onBackPress = useCallback(() => {
    navigateBack();
  }, []);
  const handleCamera = useCallback((sourceType: 'gallery' | 'camera') => {
    openCamera({
      count: 1,
      source: sourceType,
    }).then((res) => {
      console.log('getURl', res);
      console.log('跳转');
    });
  }, []);
  return (
    <LinearGradient
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 0, y: 1 }}
      colors={['#fddee9', '#ffedf3', '#f4f4f5', '#f4f4f5']}
      locations={[0, 0.2, 0.23, 1]}
    >
      <NavigationBar
        style={{ backgroundColor: 'transparent' }}
        safeArea="normal"
        backIcon={{ name: 'fanhui' }}
        onBackPress={onBackPress}
      >
        <LText type="title3" lineClamp={1}>
          AI试款
        </LText>
      </NavigationBar>

      <StyleTab onTabChange={handleTabChange} />
      <View style={styles.changeStyleContainer}>
        <View style={styles.changeStyleContent}>
          <ChangeStyle images={styleImages} ref={changeStyleRef} animationDuration={2000} />
          {/* <Video wrapperStyle={styles.videoWrapper} /> */}
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <Button
          onPress={() => {
            handleCamera('camera');
          }}
          style={styles.photoButton}
        >
          <LText>拍摄正脸照片</LText>
        </Button>
        <Button
          onPress={() => {
            handleCamera('gallery');
          }}
          style={styles.photoButton}
        >
          <LText>从相册中选取</LText>
        </Button>
      </View>
    </LinearGradient>
  );
}


const styles = remStyleSheet({
  container: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: 'red',
  },
  textContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    margin: 10,
  },
  info: {
    fontSize: 18,
    marginHorizontal: 24,
    marginVertical: 8,
    color: '#555',
  },
  changeStyleContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
    width: '100%',
    // paddingHorizontal: 21,
  },
  changeStyleContent: {
    width: 333,
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 9,
    height: 471,
    overflow: 'hidden',
  },
  buttonContainer: {
    display: 'flex',
    alignItems: 'center',
    marginTop: 18,
  },
  photoButton: {
    width: 300,
    height: 48,
    backgroundColor: '#FF8FB6',
    borderRadius: 24,
    marginBottom: 12,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },

  videoWrapper: {
    borderRadius: 12,
    overflow: 'hidden',
  },
});
export default memo(Home);
