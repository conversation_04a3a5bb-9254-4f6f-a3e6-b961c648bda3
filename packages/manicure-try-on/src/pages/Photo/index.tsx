import { createElement, memo, useCallback, useMemo, useState, useEffect } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Image from '@hfe/max-image';
import Text from '@hfe/max-text';

// import NavBar from '@/components/NavBar';
// import PhotoGrid from '@/components/PhotoGrid'
import TopViewProvider from '@max/leez-top-view-provider';
import PhotoTipModal from '@/components/PhotoTipModal';
import SaveModal from '@/components/SaveModal';

import ToastManager from '@max/leez-toast-manager';
import { chooseImage } from '@max/meituan-uni-image';

// sceneToken 隐私注册
// https://privacy.sankuai.com/register/list?page=current-3_pageSize-20

const PhotoPage = (props) => {

  const [photoDate, setPhotoDate] = useState<any>([])

  const [selected, setSelected] = useState<string>('-1');

  const [saveVisible, setSaveVisible] = useState<boolean>(true)

  useEffect(() => {
    chooseImage({
      _meituan: {
        sceneToken: 'dd-20f752dd436d1949',
      },
      count: 1,
      sourceType:['album']
    })
      .then((res) => console.log(res,'本地相册'))
      .catch((err) => console.log(err,'本地相册获取失败'));
  }, [])

  const toggleSelect = useCallback((item) => setSelected(item.id), [])

  const disabled = useMemo(() => !photoDate.find(item => item.id === selected), [photoDate, selected])

  const onSelectPhoto = useCallback(() => {
    if (!disabled) {
      console.log('点击了选择好了')
    }
  }, [disabled])

  return (
    <TopViewProvider>
      <View style={styles.container}>
        {/* <NavBar onSelectPhoto={onSelectPhoto} disabled={disabled} /> */}
        {/* <PhotoGrid photos={photoDate} selected={selected} onToggle={toggleSelect} /> */}
        <PhotoTipModal position={'center'}/>
        {/* <SaveModal
          visible={saveVisible}
          imageUrl={'https://p0.meituan.net/joyplaystatic/c2a309b294e1ea679dd8060565ca304325839.jpg'}
          onClose={() => setSaveVisible(false)}
        /> */}
      </View>
      <ToastManager />
    </TopViewProvider>
  );
};

const styles = remStyleSheet({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingHorizontal: 18,
    paddingTop: 0,
  },
});

export default memo(PhotoPage);
