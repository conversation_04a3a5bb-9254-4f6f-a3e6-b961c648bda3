{"name": "@dzfe/manicure-try-on", "author": "liuzhimeng", "description": "美甲AI试款", "version": "1.0.0", "scripts": {"start": "yarn start:mrn", "build": "max-app build --config build.js", "start:mrn": "export MAX_TARGET_ENV=mrn && max-app start --config build.js --disableAsk", "start:web": "export MAX_TARGET_ENV=web && max-app start --config build.js --disableAsk", "start:wx": "export MAX_TARGET_ENV=wechat-miniprogram && max-app start --config build.js --disableAsk", "clean": "max-app cache --clean", "eslint": "eslint --ext .js,.jsx,.tsx,.ts ./", "stylelint": "stylelint \"**/*.{css,scss,less}\" --formatter unix", "prettier": "prettier **/* --write", "lint": "yarn eslint && yarn stylelint", "fix": "yarn eslint --fix", "test": "echo 'test'"}, "repository": {"type": "git", "url": "git+ssh://*******************/hfe/max-miniapp-ts.git"}, "scaffoldConfig": {"title": "Max App", "category": "Basic", "name": "Max App"}, "publishConfig": {"access": "public"}, "resolutions": {"react-error-overlay": "6.0.9", "@types/react": "17.0.30", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "jsx-ast-utils": "^3.0.0", "react-native": "0.63.5", "@mrn/react-native": "^3.0.40", "react": "16.14.0", "@mrn/mrn-knb": "0.5.1", "@mtfe/msi-mrn": "1.15.0"}, "dependencies": {"@hfe/max-image": "^5.0.0", "@hfe/max-text": "^5.0.0", "@hfe/max-video-view": "^0.6.2", "@hfe/max-view": "^4.1.0", "@max/leez-dependencies": "2.5.82", "@max/max": "^1.0.5", "@max/meituan-uni-image": "^1.1.2", "@max/meituan-uni-mtShare": "^1.0.2", "@max/meituan-uni-network": "^3.0.9", "@max/meituan-uni-storage": "^1.1.2", "@mrn/mrn-gc-utils": "^0.1.10"}, "devDependencies": {"@hfe/max-types": "^0.0.4", "@max/build-mrn-dependencies": "^1.1.0", "@max/kit-lib-build-config-helper": "^1.0.8", "@max/max-app": "^2.0.4", "@max/max-spec": "^1.0.2", "eslint": "^6.8.0", "prettier": "^2.1.2", "stylelint": "^13.7.2"}, "engines": {"node": ">=14 <17"}, "license": "MIT"}