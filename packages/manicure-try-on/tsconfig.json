{"compileOnSave": false, "buildOnSave": false, "compilerOptions": {"baseUrl": ".", "outDir": "build", "module": "esnext", "target": "es6", "jsx": "preserve", "jsxFactory": "createElement", "moduleResolution": "node", "lib": ["es6", "dom"], "sourceMap": true, "allowJs": true, "rootDir": "./", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "importHelpers": true, "strictNullChecks": true, "noUnusedLocals": true, "skipLibCheck": true, "paths": {"@/*": ["./src/*"], "@max/max-app": [".max/index.ts"], "@APIs/*": ["./APIs/*"]}}, "include": ["src", ".max"], "exclude": ["node_modules", "build", "public"]}