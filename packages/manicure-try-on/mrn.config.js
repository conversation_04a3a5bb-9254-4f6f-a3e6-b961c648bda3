// mrn.config.js 配置文档
// http://mrn.sankuai.com/docs/guide/conf.html#mrn-config-js

let iconfont;
try {
  iconfont = require('@max/leez-icon/font.js').fonts;
} catch (e) {
  console.error('fail load @max/leez-icon iconfont');
}

module.exports = {
  name: 'manicure-try-on',
  fonts: {
    ...iconfont, // 新增的icon
  },
  main: 'src/app_mrn.tsx',
  biz: 'gcbu',
  bundleType: 1,
  bundleDependencies: ['@mrn/mrn-base'],
  debugger: {
    moduleName: 'manicure-try-on-detail',
    initialProperties: {
      hideNavigationBar: true,
      type: 1,
      tempFile:
        'knb-media://client?url=0D39C85F-44A5-40BA-8210-3CCB4F169C0A%2FL0%2F001&type=image&sceneToken=dd-3035e7d8c8db5db5',
    },
  },
};
