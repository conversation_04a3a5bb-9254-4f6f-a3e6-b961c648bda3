//import axios from 'axios'
import { request } from "@mrn/mrn-utils";

export interface Query {
  /**
   * 任务id
   */
  taskId: number;
  /**
   * 平台，1-点评，2-美团
   */
  platform: number;
}
export interface Response {
  /**
   * 状态，1-点赞，2-点踩，3-无
   */
  status?: number;
}

// type: app
// apiId: 7320
// elink文档地址: https://elink.sankuai.com/main/api-doc-v3/list?groupId=621&id=7320
// 接口名称：查询点赞点踩状态接口
// export function MRN_GET__dzim_pilot_assistant_beauty_task_like_query(
//   params: Query,
// ): Promise<Response> {
//   return request({
//     baseURL: "https://e.dianping.com/",
//     url: "/dzim/pilot/assistant/beauty/task/like/query",
//     method: "GET",
//     params,
//     // options.removeToken 移除默认添加的token (iOS特有)
//     // options.removeUserId 移除默认添加的userId (iOS特有)
//     options: {
//       disableShark: false, // options.disableShark: bool 关闭走 Shark 通道 // iOS MRN 2.5.3 支持 默认网络请求走 Shark 通道，如特殊原因需要关闭时设置 disableShark: true
//       disableRisk: false, // / 禁用风控 //disableRisk即 SAKBaseModel中disableRisk // iOS MRN 2.5.3 支持
//       registerCandyHost: false, // options.registerCandyHost: bool 注册当前URL的Host到CandyHost 即使用 MTGuard 签名
//     },
//   })
//     .then((result: any) => {
//       return result.data;
//     })
//     .catch((e: any) => {
//       e.userInfo = {
//         ...e.userInfo,
//         requestInfo: {
//           url: "/dzim/pilot/assistant/beauty/task/like/query",
//         },
//       };
//       throw e;
//     });
// }

// mock 时替换原 function，获取elink上管理的mock数据
export function MRN_GET__dzim_pilot_assistant_beauty_task_like_query(params: Query): Promise<Response> {
  return request({
    baseURL: 'https://elink.sankuai.com/',
    url: '/fbs/mock/group/621/dzim/pilot/assistant/beauty/task/like/query',
    method: 'GET',
    params,
  }).then((response) => {
    return response.data;
  }) as any as Promise<Response>
}
