// import axios from 'axios'
import { request } from "@mrn/mrn-utils";

export interface Query {
  /**
   * 试戴类型，1-试发型，2-试美甲，3-试染发
   */
  type: string;
  /**
   * 平台，1-点评，2-美团
   */
  platform: number;
  /**
   * 款式图，用户试戴款式图片链接
   */
  styleUrl: string;
  /**
   * 原始图，用户原始部位图片链接
   */
  originUrl: string;
}
export interface Response {
  /**
   * 任务id
   */
  taskId?: number;
}

// type: app
// apiId: 7312
// elink文档地址: https://elink.sankuai.com/main/api-doc-v3/list?groupId=621&id=7312
// 接口名称：任务提交接口（试戴接口）

// export function MRN_GET__dzim_pilot_assistant_hair_task_submit(
//   params: Query,
// ): Promise<Response> {
//   return request({
//     baseURL: 'https://mapi.dianping.com/',
//     url: '/dzim/pilot/assistant/beauty/task/submit',
//     method: 'GET',
//     params,
//     // options.removeToken 移除默认添加的token (iOS特有)
//     // options.removeUserId 移除默认添加的userId (iOS特有)
//     options: {
//       disableShark: false, // options.disableShark: bool 关闭走 Shark 通道 // iOS MRN 2.5.3 支持 默认网络请求走 Shark 通道，如特殊原因需要关闭时设置 disableShark: true
//       disableRisk: false, // / 禁用风控 //disableRisk即 SAKBaseModel中disableRisk // iOS MRN 2.5.3 支持
//       registerCandyHost: false, // options.registerCandyHost: bool 注册当前URL的Host到CandyHost 即使用 MTGuard 签名
//     },
//   })
//     .then((result: any) => {
//       return result.data;
//     })
//     .catch((e: any) => {
//       e.userInfo = {
//         ...e.userInfo,
//         requestInfo: {
//           url: '/dzim/pilot/assistant/beauty/task/submit',
//         },
//       };
//       throw e;
//     });
// }

// mock 时替换原 function，获取elink上管理的mock数据
export function MRN_GET__dzim_pilot_assistant_hair_task_submit(params: Query): Promise<Response> {
  return request({
    baseURL: 'https://elink.sankuai.com/',
    url: '/fbs/mock/group/621/dzim/pilot/assistant/beauty/task/submit',
    method: 'GET',
    params,
  }).then((response) => {
    return response.data;
  }) as any as Promise<Response>
}
