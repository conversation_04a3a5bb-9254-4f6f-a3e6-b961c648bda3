// mrn.config.js 配置文档
// http://mrn.sankuai.com/docs/guide/conf.html#mrn-config-js
let iconfont
try {
  iconfont = require('@max/leez-icon/font.js').fonts
} catch (e) {
  console.error('fail load @max/leez-icon iconfont')
}

module.exports = {
  name: 'buy-often-list-page',
  main: './index.tsx', // 页面入口地址
  biz: 'gcbu',
  bundleType: 1,
  bundleDependencies: ['@mrn/mrn-base'],
  debugger: {
    //
    moduleName: 'mrn-beauty-buy-often-list-page',
    initialProperties: {
      hideNavigationBar: true
    }
  },
  fonts: {
    ...iconfont,
    'Meituan Type': './assets/MeituanType-Bold.ttf'
  },
  // 转 H5 配置
  one: {
    appConfig: {
      pages: [
        {
          name: 'buy-often-list-page',
          path: 'index.tsx',
          enableShareAppMessage: true
        }
      ]
    }
  }
}
