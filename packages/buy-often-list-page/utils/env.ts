import { oneEnv } from '@nibfe/mc-one-env'
// 点评所有有关端环境 点评app、点评微信小程序、点评站外等
export const isDP = oneEnv.app('dp')
// 美团所有有关端环境
export const isMT = oneEnv.app('mt')
// 小程序原生页面
export const isNativeMP = oneEnv.platform('mp')
// 小程序页面（原生 + h5）
export const isMP = oneEnv.platform('mp') || oneEnv.engine('wxmph5') || oneEnv.engine('ksmph5')
// h5页面，除了微信小程序外的其他平台
export const isWeb = oneEnv.platform('web')
// 美团所有端h5页面
export const isMTWeb = oneEnv.app('mt') && isWeb
// 点评所有端h5页面
export const isDPWeb = oneEnv.app('dp') && isWeb
//  微信小程序webview
export const isWXMPWebview = oneEnv.engine('wxmph5')
// 公司内app环境，目前包括美团 + 点评
export const isApp = oneEnv.platform('mrn') || (oneEnv.platform('web') && oneEnv.engine('titans'))

export enum PLATFORM {
  NOT_FIND = 0,
  DP_XCX = 102,
  MT_XCX = 202
}

function getPlatform(): PLATFORM {
  // 本期只支持点评和美团小程序两个环境
  if (isDP && isWXMPWebview) {
    return PLATFORM.DP_XCX
  }
  if (isMT && isWXMPWebview) {
    return PLATFORM.MT_XCX
  }
  return PLATFORM.NOT_FIND
}

export const platform = getPlatform()
