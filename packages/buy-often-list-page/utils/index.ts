import { isMT } from './env'
import { openUrl } from '@mrn/mrn-utils'
import { StatusBar } from '@mrn/react-native'
import { IS_ANDROID } from '@mrn/mrn-gc-utils'

export const AdjustHeight = IS_ANDROID ? StatusBar.currentHeight || 0 : 0

export const openInApp = (url: string) => {
  const schema = isMT
    ? 'https://w.dianping.com/cube/evoke/meituan.html?url='
    : 'https://w.dianping.com/cube/evoke/dianping.html?url='

  openUrl(schema + encodeURIComponent(url))
}
