import React from 'react'
import { View, Text, StyleSheet } from 'react-native'

import Swiper from 'react-native-swiper'

interface Props {
  textList: string[]
}

const SwiperText: React.FC<Props> = props => {
  const { textList } = props
  return (
    <View style={styles.swiperContainer}>
      <Swiper
        height={15}
        style={styles.swiperStyle}
        // 这个很重要，解决白屏问题
        removeClippedSubviews={false}
        autoplayTimeout={5}
        autoplay={true}
        horizontal={false}
        dot={<></>}
        activeDot={<></>}
      >
        {textList.length &&
          textList.map(item => {
            return (
              <View key={item} style={styles.swiperItem}>
                <Text ellipsizeMode="tail" numberOfLines={1} style={styles.swiperItemText}>
                  {item}
                </Text>
              </View>
            )
          })}
      </Swiper>
    </View>
  )
}

export default SwiperText

const styles = StyleSheet.create({
  swiperContainer: {
    maxWidth: 80
  },
  // 必须给宽度
  swiperStyle: {
    maxWidth: 80
  },
  swiperItem: {
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
  swiperItemText: {
    fontSize: 10,
    fontFamily: 'PingFang SC',
    color: '#999999'
  }
})
