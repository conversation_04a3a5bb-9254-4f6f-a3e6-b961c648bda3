import React from 'react'
import { pageRouterClose } from '@mrn/mrn-utils'
import { MCModule } from '@nibfe/doraemon-practice'
import NavigationBar from '@max/leez-navigation-bar'
import LText from '@max/leez-text'
import { View, StyleSheet, Text, Image, Animated, StatusBar } from '@mrn/react-native'
import type { TabListItem } from '../../types/index'
import { getRecommendHeight, getRecommendPaddingTop } from '@nibfe/dm-navigation'
import { AdjustHeight } from '../../utils/index'
import { IS_ANDROID } from '@mrn/mrn-gc-utils'

interface Props {
  title: string
  subTitle: string
  tabList: TabListItem[]
  scrollY: any
}

export const NavModule: React.FC<Props> = props => {
  const { title, subTitle, tabList, scrollY } = props
  const back = () => {
    pageRouterClose()
  }

  const renderNav = () => {
    return (
      <View
        style={[styles.navBg, IS_ANDROID && { marginTop: getRecommendPaddingTop() + AdjustHeight }]}
      >
        {IS_ANDROID ? (
          <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
        ) : null}
        <NavigationBar
          safeArea="normal"
          iconPosition="absolute"
          onBackPress={back}
          backIcon={{ name: 'fanhui' }}
        >
          <View style={styles.navPage}>
            <LText type="title3" lineClamp={1} style={styles.pageTitle}>
              {title}
            </LText>
            <View style={styles.pageSubTitle}>
              <Text style={styles.pageSubTitleText} ellipsizeMode="tail" numberOfLines={1}>
                {subTitle}
              </Text>
            </View>
          </View>
        </NavigationBar>
        {Boolean(tabList && tabList.length) && (
          <Image source={require('../../assets/img/navBg.png')} style={styles.radiusBg} />
        )}
      </View>
    )
  }

  return (
    <MCModule
      paddingLeft={0}
      paddingRight={0}
      hoverType="alwaysHover"
      hoverOffset={0}
      backgroundColor={'#ffffff00'}
    >
      {renderNav()}
      <Animated.View
        style={[
          styles.navWrapper,
          {
            opacity: scrollY.interpolate({
              inputRange: [0, 50],
              outputRange: [0, 1]
            })
          }
        ]}
      >
        {renderNav()}
      </Animated.View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  navBg: {
    position: 'relative'
  },
  radiusBg: {
    width: 20,
    height: 20,
    position: 'absolute',
    bottom: 0,
    right: 0,
    zIndex: 10
  },
  navPage: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 40,
    paddingRight: 40
  },
  pageTitle: {
    fontFamily: 'Meituan Type',
    textAlign: 'left',
    flexShrink: 0,
    fontWeight: 'bold'
  },
  pageSubTitle: {
    backgroundColor: '#f5f5f5',
    marginLeft: 10,
    borderRadius: 10,
    flexShrink: 1
  },
  pageSubTitleText: {
    fontSize: 10,
    fontFamily: 'PingFang SC',
    color: '#FF571F',
    paddingVertical: 2,
    paddingHorizontal: 8
  },
  navWrapper: {
    height: getRecommendHeight() + AdjustHeight,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#fff'
  }
})
