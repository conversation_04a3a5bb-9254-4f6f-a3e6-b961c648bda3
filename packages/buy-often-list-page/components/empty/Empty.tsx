import React from 'react'
import { View, Text, Image, StyleSheet, Dimensions } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'

interface Props {
  empty: boolean
}
const { height } = Dimensions.get('window')

export const Empty: React.FC<Props> = ({ empty }) => {
  return (
    <MCModule gapTop={0} paddingLeft={0} paddingRight={0} backgroundColor={'#f4f4f4'}>
      <View style={styles.empty}>
        {empty && (
          <>
            <Image
              source={{
                uri: 'https://p0.meituan.net/travelcube/cd114f8e0589a5717d78bbfee1b0743597809.png'
              }}
              style={styles.emptyImg}
            />
            <Text style={styles.emptyTitle}>暂无数据</Text>
          </>
        )}
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  empty: {
    flex: 1,
    paddingTop: 200,
    flexDirection: 'column',
    alignItems: 'center',
    backgroundColor: '#f4f4f4',
    height: height || 800
  },
  emptyImg: {
    width: 150,
    height: 163
  },
  emptyTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#222',
    fontFamily: 'PingFang SC'
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    fontFamily: 'PingFang SC'
  }
})
