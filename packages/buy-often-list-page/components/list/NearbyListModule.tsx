import React from 'react'
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableWithoutFeedback,
  TouchableOpacity
} from '@mrn/react-native'
import LButton from '@max/leez-button'
import { openUrl } from '@mrn/mrn-utils'
import type { NearbyList, ButtonType } from '../../types/index'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'
import SwiperText from '../swiper'

interface Props {
  nearbyList: {
    cardDTOS: NearbyList[]
    rankDetailDTO: ButtonType
  }
}

export const NearbyListModule: React.FC<Props> = props => {
  const {
    nearbyList: { cardDTOS, rankDetailDTO }
  } = props

  const swiperContainer = (saleTag, repurchaseCountTag) => {
    const arr = []
    if (saleTag) {
      arr.push(saleTag)
    }
    if (repurchaseCountTag) {
      arr.push(repurchaseCountTag)
    }
    if (arr.length) {
      return <SwiperText textList={arr} />
    }
    return <></>
  }

  return (
    <>
      <View style={styles.techniciansList}>
        <Text style={styles.listTitle}>附近的人常买</Text>
        {cardDTOS.map((item, index) => {
          lxTrackMGEViewEvent('gc', 'b_gc_0ys1s2mq_mv', 'c_gc_6h37mce0', {
            index: index,
            deal_id: item.productInfo?.productId || ''
          })
          return (
            <TouchableWithoutFeedback
              onPress={() => {
                if (item?.productInfo?.detailUrl) {
                  openUrl(item?.productInfo?.detailUrl)
                  lxTrackMGEClickEvent('gc', 'b_gc_0ys1s2mq_mc', 'c_gc_6h37mce0', {
                    click_type: 0,
                    deal_id: item.productInfo?.productId || '',
                    index: index
                  })
                }
              }}
              key={`nearbyList${index}`}
              style={styles.container}
            >
              <View style={styles.container}>
                <View style={styles.cardItem}>
                  <View style={styles.cardHeader}>
                    <Image source={require('../../assets/img/shop.png')} style={styles.cardIcon} />
                    <Text ellipsizeMode="tail" numberOfLines={1} style={styles.cardAddress}>
                      {item?.shopInfo?.star && <Text>{item?.shopInfo?.star}</Text>}
                      <Text> · </Text>
                      <Text>{item.shopInfo?.shopName}</Text>
                      <Text> · </Text>
                      <Text>{item.shopInfo?.distance}</Text>
                    </Text>
                    <Image source={require('../../assets/img/right.png')} style={styles.cardIcon} />
                  </View>
                  <View style={styles.cardContainer}>
                    <View style={styles.cardContainerLeft}>
                      <Image
                        source={{ uri: item.productInfo?.headPic }}
                        style={styles.cardHeadPortrait}
                      />
                    </View>
                    <View style={styles.cardContainerRight}>
                      <Text ellipsizeMode="tail" numberOfLines={1} style={styles.cardName}>
                        {item?.productInfo?.name}
                      </Text>
                      {Boolean(item?.productInfo?.subName) && (
                        <Text ellipsizeMode="tail" numberOfLines={1} style={styles.cardType}>
                          {item?.productInfo?.subName}
                        </Text>
                      )}
                      <View style={styles.priceContainer}>
                        <View style={styles.priceContainerLeft}>
                          {item?.productInfo?.rankTag ? (
                            <TouchableOpacity
                              onPress={() => {
                                openUrl(item?.productInfo?.rankUrl)
                              }}
                              style={styles.cardRanking}
                            >
                              <View style={styles.cardRankingTextContainer}>
                                <Text
                                  style={styles.cardRankingTextBox}
                                  numberOfLines={1}
                                  ellipsizeMode="tail"
                                >
                                  <Text style={styles.cardRankingText}>
                                    {item?.productInfo?.rankTag}
                                  </Text>
                                  <Image
                                    source={require('../../assets/img/rightIcon.png')}
                                    style={styles.cardRankingIcon}
                                  />
                                </Text>
                              </View>
                              <View style={styles.EmptyView} />
                            </TouchableOpacity>
                          ) : (
                            <View />
                          )}
                          <View style={styles.priceContainerLeftTop}>
                            <View style={styles.priceNumberContainer}>
                              <Text style={styles.priceIcon}>¥</Text>
                              <Text style={styles.priceNumber}>
                                {item?.productInfo?.promoPrice}
                              </Text>
                            </View>
                            {Boolean(item?.productInfo?.discountTag) && (
                              <Text style={styles.priceDiscount}>
                                {item?.productInfo?.discountTag}
                              </Text>
                            )}
                            {Boolean(item?.productInfo?.marketPrice) && (
                              <Text style={styles.marketPrice}>
                                {`¥${item?.productInfo?.marketPrice}`}
                              </Text>
                            )}
                          </View>
                        </View>
                        <View style={styles.priceContainerRight}>
                          <View style={styles.priceContainerRightTop}>
                            {Boolean(
                              item?.productInfo?.saleTag || item?.productInfo?.repurchaseCountTag
                            ) &&
                              swiperContainer(
                                item?.productInfo?.saleTag,
                                item?.productInfo?.repurchaseCountTag
                              )}
                          </View>
                          <View style={styles.priceContainerRightBottom}>
                            {Boolean(item?.buttonInfoList?.length) &&
                              item?.buttonInfoList?.map((it, ind) => {
                                return (
                                  <>
                                    {it.type === 3 ? (
                                      <LButton
                                        level="small"
                                        type="weak"
                                        onPress={() => {
                                          if (it?.jumpUrl) {
                                            openUrl(it.jumpUrl)
                                            lxTrackMGEClickEvent(
                                              'gc',
                                              'b_gc_0ys1s2mq_mc',
                                              'c_gc_6h37mce0',
                                              {
                                                click_type: 0,
                                                deal_id: item.productInfo?.productId || '',
                                                index: ind
                                              }
                                            )
                                          }
                                        }}
                                        text={it?.text}
                                      />
                                    ) : (
                                      <LButton
                                        level="small"
                                        style={{ marginLeft: 3 }}
                                        onPress={() => {
                                          if (item?.productInfo?.orderUrl) {
                                            openUrl(it?.jumpUrl)
                                            lxTrackMGEClickEvent(
                                              'gc',
                                              'b_gc_0ys1s2mq_mc',
                                              'c_gc_6h37mce0',
                                              {
                                                click_type: 0,
                                                deal_id: item.productInfo?.productId || '',
                                                index: ind
                                              }
                                            )
                                          }
                                        }}
                                        text={it?.text}
                                      />
                                    )}
                                  </>
                                )
                              })}
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </TouchableWithoutFeedback>
          )
        })}
        {rankDetailDTO?.jumpUrl && (
          <TouchableOpacity
            onPress={() => {
              if (rankDetailDTO?.jumpUrl) {
                openUrl(rankDetailDTO.jumpUrl)
              }
            }}
            style={styles.moreContent}
          >
            <Text style={styles.moreText}>{rankDetailDTO.title}</Text>
            <Image source={require('../../assets/img/right.png')} style={styles.bottomIcon} />
          </TouchableOpacity>
        )}
      </View>
    </>
  )
}

const styles = StyleSheet.create({
  techniciansList: {
    flex: 1,
    flexDirection: 'column'
  },
  listTitle: {
    fontFamily: 'PingFang SC',
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222222',
    marginLeft: 18,
    marginTop: 21,
    marginBottom: 8
  },
  container: {
    marginHorizontal: 6,
    flexDirection: 'row',
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
    marginBottom: 6,
    borderRadius: 8
  },
  cardItem: {
    flex: 1,
    flexDirection: 'column'
  },
  cardHeader: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center'
  },
  cardIcon: {
    width: 12,
    height: 12,
    flexShrink: 0
  },
  cardAddress: {
    flexShrink: 1,
    fontFamily: 'PingFang SC',
    fontSize: 12,
    color: '#222222',
    marginLeft: 3
  },
  cardContainer: {
    flex: 1,
    flexDirection: 'row',
    marginTop: 10
  },
  cardContainerLeft: {
    flexShrink: 0,
    width: 84,
    height: 84,
    marginRight: 9,
    borderRadius: 6,
    overflow: 'hidden'
  },
  cardHeadPortrait: {
    width: 84,
    height: 84
  },
  cardContainerRight: {
    flex: 1,
    flexShrink: 1,
    flexDirection: 'column'
  },
  cardName: {
    fontFamily: 'PingFang SC',
    fontSize: 14,
    color: '#222222',
    fontWeight: 'bold'
  },
  cardType: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    color: '#666'
  },
  priceContainer: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  priceContainerLeft: {
    flexShrink: 1,
    flexDirection: 'column',
    justifyContent: 'space-between',
    marginTop: 3
  },
  cardRanking: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  cardRankingTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFEDDE',
    paddingHorizontal: 3,
    paddingVertical: 1,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 15,
    borderBottomRightRadius: 15,
    borderBottomLeftRadius: 3,
    marginTop: 2,
    overflow: 'hidden'
  },
  EmptyView: {},
  cardRankingTextBox: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  cardRankingText: {
    fontFamily: 'PingFang SC',
    fontSize: 10,
    color: '#8E3C12'
  },
  cardRankingIcon: {
    width: 10,
    height: 10
  },
  priceContainerLeftTop: {
    flexDirection: 'row',
    alignItems: 'flex-end'
  },
  priceNumberContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end'
  },
  priceIcon: {
    fontFamily: 'MTfin2.0',
    fontSize: 12,
    color: '#FF4B10'
  },
  priceNumber: {
    fontFamily: 'MTfin2.0',
    fontSize: 18,
    color: '#FF4B10',
    marginBottom: -3
  },
  priceDiscount: {
    fontFamily: 'PingFang SC',
    fontSize: 10,
    color: '#FF4B10',
    backgroundColor: '#FFF1EC',
    paddingHorizontal: 3,
    paddingVertical: 1,
    borderRadius: 3,
    overflow: 'hidden',
    marginLeft: 3
  },
  marketPrice: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    color: '#999',
    marginLeft: 3,
    textDecorationLine: 'line-through',
    marginBottom: -1
  },
  priceContainerRightBottom: {
    flexDirection: 'row',
    justifyContent: 'flex-end'
  },
  priceContainerRight: {
    flexShrink: 0,
    flexDirection: 'column',
    justifyContent: 'flex-end'
  },
  priceContainerRightTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginBottom: 2
  },
  // priceContainerRightTopText: {
  //   fontSize: 10,
  //   fontFamily: 'PingFang SC',
  //   color: '#999999'
  // },
  moreContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2
  },
  moreText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    color: '#222'
  },
  bottomIcon: {
    width: 12,
    height: 12
  }
})
