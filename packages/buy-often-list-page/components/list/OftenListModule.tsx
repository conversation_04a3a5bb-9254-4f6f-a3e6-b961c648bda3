import React, { useState } from 'react'
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableWithoutFeedback,
  TouchableOpacity,
  FlatList
} from '@mrn/react-native'
import LButton from '@max/leez-button'
import { openUrl } from '@mrn/mrn-utils'
import type { OftenPageList } from '../../types/index'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'

interface Props {
  buyoftenList: {
    totalHit?: number
    resultList?: OftenPageList[]
  }
  onWhole: () => void
}

export const OftenListModule: React.FC<Props> = props => {
  const {
    buyoftenList: { totalHit, resultList = [] },
    onWhole
  } = props
  const [isWhole, setWhole] = useState(true) // 是否展示全部

  return (
    <>
      <FlatList
        style={[styles.techniciansList]}
        data={resultList}
        keyExtractor={(_it, index) => index + 'resultList'}
        renderItem={({ item, index }) => {
          lxTrackMGEViewEvent('gc', 'b_gc_2zcse8ip_mv', 'c_gc_6h37mce0', {
            index: index,
            deal_id: item.productInfo?.productId || ''
          })
          return (
            <TouchableWithoutFeedback
              key={`buyoftenList${index}`}
              style={styles.container}
              onPress={() => {
                if (item?.productInfo?.detailUrl) {
                  openUrl(item?.productInfo?.detailUrl)
                }
                lxTrackMGEClickEvent('gc', 'b_gc_2zcse8ip_mc', 'c_gc_6h37mce0', {
                  click_type: 0,
                  deal_id: item.productInfo?.productId || '',
                  index: index
                })
              }}
            >
              <View style={styles.container}>
                <View style={styles.cardItem}>
                  <View style={styles.cardHeader}>
                    <Image source={require('../../assets/img/shop.png')} style={styles.cardIcon} />
                    <Text ellipsizeMode="tail" numberOfLines={1} style={styles.cardAddress}>
                      {Boolean(item?.shopInfo?.star) && <Text>{item?.shopInfo?.star}</Text>}
                      <Text> · </Text>
                      <Text>{item.shopInfo?.shopName}</Text>
                      <Text> · </Text>
                      <Text>{item.shopInfo?.distance}</Text>
                    </Text>
                    <Image source={require('../../assets/img/right.png')} style={styles.cardIcon} />
                  </View>
                  <View style={styles.cardContainer}>
                    <View style={styles.cardContainerLeft}>
                      <Image
                        source={{ uri: item?.productInfo?.headPic }}
                        style={styles.cardHeadPortrait}
                      />
                      <View style={styles.numberContainer}>
                        <Image
                          source={require('../../assets/img/icon-bg.png')}
                          style={styles.numberContainerBg}
                        />
                        <Text ellipsizeMode="tail" numberOfLines={1} style={styles.numberText}>
                          {item?.productInfo?.purchaseCountTag}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.cardContainerRight}>
                      <Text ellipsizeMode="tail" numberOfLines={1} style={styles.cardName}>
                        {item?.productInfo?.name}
                      </Text>
                      <Text ellipsizeMode="tail" numberOfLines={1} style={styles.cardType}>
                        {item?.productInfo?.subName}
                      </Text>
                      <View style={styles.priceContainer}>
                        <View style={styles.priceContainerLeft}>
                          <View style={styles.priceContainerLeftTop}>
                            <View
                              style={[
                                styles.priceNumberContainer,
                                !item?.productInfo?.comparePriceTag && { marginBottom: -3 }
                              ]}
                            >
                              <Text style={styles.priceIcon}>¥</Text>
                              <Text style={styles.priceNumber}>
                                {item?.productInfo?.promoPrice}
                              </Text>
                            </View>
                            {Boolean(item?.productInfo?.discountTag) && (
                              <Text
                                style={[
                                  styles.priceDiscount,
                                  Boolean(item?.productInfo?.comparePriceTag) && { marginBottom: 3 }
                                ]}
                              >
                                {item?.productInfo?.discountTag}
                              </Text>
                            )}
                          </View>
                          {Boolean(item?.productInfo?.comparePriceTag) && (
                            <View style={styles.priceContainerLeftBox}>
                              <View style={styles.priceContainerLeftBottom}>
                                <Image
                                  source={require('../../assets/img/tag.png')}
                                  style={styles.discountIcon}
                                />
                                <Text style={styles.discountText}>
                                  {item?.productInfo?.comparePriceTag}
                                </Text>
                              </View>
                              <View style={styles.priceContainerLeftEmpty} />
                            </View>
                          )}
                        </View>
                        <View style={styles.priceContainerRight}>
                          {Boolean(item?.buttonInfoList?.length) &&
                            item?.buttonInfoList?.map((it, ind) => {
                              return (
                                <>
                                  {it?.type === 3 ? (
                                    <LButton
                                      level="small"
                                      type="weak"
                                      onPress={() => {
                                        if (it?.jumpUrl) {
                                          openUrl(it.jumpUrl)
                                          lxTrackMGEClickEvent(
                                            'gc',
                                            'b_gc_2zcse8ip_mc',
                                            'c_gc_6h37mce0',
                                            {
                                              click_type: 2,
                                              deal_id: item.productInfo?.productId || '',
                                              index: ind
                                            }
                                          )
                                        }
                                      }}
                                      text={it?.text}
                                    />
                                  ) : (
                                    <LButton
                                      level="small"
                                      style={{ marginLeft: 3 }}
                                      onPress={() => {
                                        if (it?.jumpUrl) {
                                          openUrl(it.jumpUrl)
                                          lxTrackMGEClickEvent(
                                            'gc',
                                            'b_gc_2zcse8ip_mc',
                                            'c_gc_6h37mce0',
                                            {
                                              click_type: 1,
                                              deal_id: item.productInfo?.productId || '',
                                              index: ind
                                            }
                                          )
                                        }
                                      }}
                                      text={it?.text}
                                    />
                                  )}
                                </>
                              )
                            })}
                        </View>
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </TouchableWithoutFeedback>
          )
        }}
      />
      {totalHit && totalHit > 4 && isWhole && (
        <TouchableOpacity
          onPress={() => {
            if (onWhole) {
              setWhole(false)
              onWhole()
            }
          }}
          style={styles.moreContent}
        >
          <Text style={styles.moreText}>更多 {totalHit - 4} 个常买商品</Text>
          <Image source={require('../../assets/img/bottomIcon.png')} style={styles.bottomIcon} />
        </TouchableOpacity>
      )}
    </>
  )
}

const styles = StyleSheet.create({
  techniciansList: {
    flex: 1,
    flexDirection: 'column'
  },
  container: {
    marginHorizontal: 6,
    flexDirection: 'row',
    paddingHorizontal: 12,
    paddingVertical: 12,
    backgroundColor: '#fff',
    marginBottom: 6,
    borderRadius: 8
  },
  cardItem: {
    flex: 1,
    flexDirection: 'column'
  },
  cardHeader: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center'
  },
  cardIcon: {
    width: 12,
    height: 12,
    flexShrink: 0
  },
  cardAddress: {
    flexShrink: 1,
    fontFamily: 'PingFang SC',
    fontSize: 12,
    color: '#222222',
    marginLeft: 3
  },
  cardContainer: {
    flex: 1,
    flexDirection: 'row',
    marginTop: 10
  },
  cardContainerLeft: {
    flexShrink: 0,
    borderRadius: 6,
    marginRight: 9,
    width: 84,
    height: 84,
    overflow: 'hidden',
    position: 'relative'
  },
  numberContainer: {
    position: 'absolute',
    // backgroundColor: '#000',
    left: 0,
    right: 0,
    bottom: 0,
    height: 22
  },
  numberContainerBg: {
    height: 22,
    width: '100%'
  },
  numberText: {
    position: 'absolute',
    fontFamily: 'PingFang SC',
    color: '#fff',
    fontSize: 10,
    fontWeight: '500',
    bottom: 4,
    left: 6,
    right: 6
  },
  cardHeadPortrait: {
    width: 84,
    height: 84
  },
  cardContainerRight: {
    flex: 1,
    flexShrink: 1,
    flexDirection: 'column'
  },
  cardName: {
    fontFamily: 'PingFang SC',
    fontSize: 14,
    color: '#222222',
    fontWeight: 'bold'
  },
  cardType: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    color: '#666',
    marginTop: 3
  },
  priceContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between'
  },
  priceContainerLeft: {
    flexShrink: 1,
    flexDirection: 'column'
  },
  priceContainerLeftTop: {
    flexDirection: 'row',
    alignItems: 'flex-end'
  },
  priceNumberContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end'
  },
  priceIcon: {
    fontFamily: 'MTfin2.0',
    fontSize: 12,
    color: '#FF4B10',
    paddingBottom: 2.5
  },
  priceNumber: {
    fontFamily: 'MTfin2.0',
    fontSize: 18,
    color: '#FF4B10'
  },
  priceDiscount: {
    fontFamily: 'PingFang SC',
    fontSize: 10,
    color: '#FF4B10',
    backgroundColor: '#FFF1EC',
    paddingHorizontal: 3,
    paddingVertical: 1,
    borderRadius: 3,
    overflow: 'hidden',
    marginLeft: 3
  },
  priceContainerLeftBox: {
    flexDirection: 'row'
  },
  priceContainerLeftEmpty: {
    flexShrink: 0
  },
  priceContainerLeftBottom: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF1EC',
    paddingHorizontal: 3,
    borderRadius: 3,
    paddingVertical: 1,
    overflow: 'hidden',
    marginTop: 3
  },
  discountIcon: {
    width: 10,
    height: 10
  },
  discountText: {
    fontFamily: 'PingFang SC',
    fontSize: 10,
    color: '#FF4B10',
    marginLeft: 2
  },
  priceContainerRight: {
    flexShrink: 0,
    flexDirection: 'row'
  },
  moreContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2
  },
  moreText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    color: '#222'
  },
  bottomIcon: {
    width: 12,
    height: 12
  }
})
