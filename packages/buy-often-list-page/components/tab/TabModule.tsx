import React, { useState, useEffect } from 'react'
import { MCModule } from '@nibfe/doraemon-practice'
import Tab from '@max/leez-tab'
import type { TabListItem } from '../../types/index'
import { View, StyleSheet } from '@mrn/react-native'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'

interface Props {
  tabList: TabListItem[]
  onTabChange?: (index: number, item: TabListItem) => void
}

export const TabModule: React.FC<Props> = props => {
  const { tabList, onTabChange } = props
  const [activeIndex1, setActiveIndex1] = useState(0)
  const onScroll = e => {
    console.log('滚动偏移量', e.detail.contentOffset.x)
  }

  useEffect(() => {
    if (tabList && tabList.length > 0) {
      tabList.map((item, index) => {
        lxTrackMGEViewEvent('gc', 'b_gc_7f11vtas_mv', 'c_gc_6h37mce0', {
          tab_index: index,
          tab_name: item.title
        })
      })
    }
  }, [tabList])
  return (
    <MCModule
      gapTop={0}
      paddingLeft={0}
      paddingRight={0}
      hoverType="none"
      // hoverOffset={100}
      backgroundColor={'#ffffff00'}
      onExpose={() => {}}
    >
      <View style={styles.tabBg}>
        <LinearGradient start={{ x: 0, y: 0 }} end={{ x: 0, y: 1 }} colors={['#ffffff', '#f4f4f4']}>
          <Tab
            activeIndex={activeIndex1}
            onItemPress={(index: number, item: TabListItem) => {
              lxTrackMGEClickEvent('gc', 'b_gc_7f11vtas_mc', 'c_gc_6h37mce0', {
                tab_index: index,
                tab_name: item?.title || ''
              })
              setActiveIndex1(index)
              if (onTabChange) {
                onTabChange(index, item)
              }
            }}
            data={tabList}
            enableAnimation={false}
            onScroll={onScroll}
          />
        </LinearGradient>
      </View>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  tabBg: {
    borderTopLeftRadius: 25,
    overflow: 'hidden',
    backgroundColor: '#f4f4f4'
  }
})
