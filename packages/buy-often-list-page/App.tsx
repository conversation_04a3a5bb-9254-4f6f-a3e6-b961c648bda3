import React, { useEffect, useState, useRef } from 'react'
import { MCModule, AnimatedMCPage } from '@nibfe/doraemon-practice'
import { NavModule } from 'components/nav/NavModule'
import { TabModule } from 'components/tab/TabModule'
import { OftenListModule } from 'components/list/OftenListModule'
import { NearbyListModule } from 'components/list/NearbyListModule'
import { Empty } from 'components/empty/Empty'
import TopViewProvider from '@max/leez-top-view-provider'
import { getTab, getOftenList, getNearbyList } from './api/index'
import type { TabListItem } from './types/index'
import { View, Image, StyleSheet } from '@mrn/react-native'
import { DPMER_THEME_GCUI, Provider as ThemeProvider } from '@nibfe/theme-provider-lighter'
import type { BuyoftenList, NearbyList } from './types/index'
import LX from '@analytics/mrn-sdk'
import { Animated } from '@mrn/react-native'

const App = () => {
  const scrollY = useRef(new Animated.Value(0)).current
  const [empty, setEmpty] = useState(false)
  const [title, setTitle] = useState('')
  const [subTitle, setSubTitle] = useState('')
  const [tabList, setTabList] = useState([]) // tab
  const [activeIndex, setActiveIndex] = useState<TabListItem>(null) // 当前选中tab
  const [buyoftenList, setBuyoftenList] = useState<BuyoftenList>() // 常买列表
  const [nearbyList, setNearbyList] = useState<NearbyList>() // 常买列表
  // 获取列表数据
  const getList = (pageSize?: number) => {
    // 常买清单列表
    getOftenList({
      pageNo: 1,
      rows: pageSize || 4,
      categoryId: activeIndex?.categoryId || ''
    }).then(res => {
      setBuyoftenList(res)
    })
    setEmpty(true)
    if (pageSize) return
    // 附近的人常买列表
    getNearbyList({ categoryId: activeIndex?.categoryId || '' }).then(res => {
      setNearbyList(res)
    })
  }

  // tab切换
  const onTabChange = (index: number, item: TabListItem) => {
    setActiveIndex(item)
  }

  // 获取tab
  const getTabList = async () => {
    const res = await getTab()
    if (res?.categoryList?.length) {
      const list = res?.categoryList.map(item => {
        return {
          title: item.categoryName,
          categoryId: item.categoryId
        }
      })
      setTabList(list || [])
      if (list?.length) {
        setActiveIndex(list[0])
      }
    } else {
      getList()
    }
    LX.pageView({
      category: 'gc',
      cid: 'c_gc_6h37mce0',
      pageInfoKey: 'mrn-beauty-buy-often-list-page',
      valLab: {
        cat_id: activeIndex?.categoryId || ''
      }
    })
    setTitle(res.title)
    setSubTitle(res.subTitle)
  }

  useEffect(() => {
    getTabList()
  }, [])

  const onWhole = () => {
    getList(50)
  }

  useEffect(() => {
    if (activeIndex?.categoryId) {
      getList()
    }
  }, [activeIndex])

  return (
    <TopViewProvider>
      <ThemeProvider theme={DPMER_THEME_GCUI}>
        <View style={styles.bgView}>
          <Image source={require('./assets/img/bg.png')} style={styles.bgImg} />
        </View>
        <AnimatedMCPage
          showScrollIndicator={false}
          style={{
            backgroundColor: '#ffffff00'
          }}
          paddingHorizontal={0}
          pageGap={0}
          pageTopGap={0}
          // eslint-disable-next-line react-native/no-inline-styles
          separatorLineStyle={{
            display: 'hidden-all'
          }}
          enableBounce={false}
          onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
            useNativeDriver: true
          })}
          scrollEventThrottle={1}
          modules={[
            {
              moduleKey: 'NavModule',
              module: (
                <NavModule subTitle={subTitle} title={title} tabList={tabList} scrollY={scrollY} />
              )
            },
            {
              moduleKey: 'TabModule',
              module: Boolean(tabList.length) && (
                <TabModule tabList={tabList} onTabChange={onTabChange} />
              )
            },
            {
              moduleKey: 'ListModule',
              module: (
                <MCModule backgroundColor={'#f4f4f4'}>
                  {Boolean(buyoftenList?.resultList?.length) && (
                    <OftenListModule buyoftenList={buyoftenList} onWhole={onWhole} />
                  )}
                  {Boolean(nearbyList?.cardDTOS?.length) && (
                    <NearbyListModule nearbyList={nearbyList} />
                  )}
                </MCModule>
              )
            },
            {
              moduleKey: 'Empty',
              module: !buyoftenList?.resultList?.length && !nearbyList?.cardDTOS?.length && (
                <Empty empty={empty} />
              )
            }
          ]}
        />
      </ThemeProvider>
    </TopViewProvider>
  )
}

export default App

const styles = StyleSheet.create({
  bgView: {
    position: 'absolute',
    flex: 1,
    zIndex: 0,
    top: 0,
    bottom: 0,
    right: 0,
    left: 0,
    backgroundColor: '#f5f5f5'
  },
  bgImg: {
    height: 300,
    width: '100%'
  }
})
