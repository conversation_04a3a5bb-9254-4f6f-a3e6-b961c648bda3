export interface GuaranteeDetailRequest {}
export interface GuaranteeDetailResponse {
  title?: string
  subTitle?: string
  categoryList?: CategoryList[]
}

export interface CategoryList {
  categoryId: string
  categoryName: string
}

export interface TabListItem {
  categoryId: string
  title: string
}

export interface PageList {
  platform?: number
  lat?: number | string
  lng?: number | string
  cityId?: number
  categoryId: string
  pageNo: number
  rows: number
}

export interface OftenPageList {
  shopInfo: {
    shopName: string
    star: number
    address: string
    distance: string
  }
  productInfo: {
    productName: string
    productPrice: number
    productImg: string
    headPic: string
    name: string
    subName: string
    promoPriceTag: string
    discountTag: string
    comparePriceTag: string
    detailUrl: string
    orderUrl: string
    purchaseState: number
    productId?: number
    promoPrice?: string
    purchaseCountTag?: string
  }
  buttonInfoList: Array<ItemBut>
}

export interface NearbyList {
  shopInfo: {
    shopName: string
    star: number
    address: string
    distance: string
  }
  productInfo: {
    productName: string
    productPrice: number
    productImg: string
    headPic: string
    name: string
    subName: string
    promoPriceTag: string
    discountTag: string
    comparePriceTag: string
    saleTag: string
    detailUrl: string
    orderUrl: string
    tagName: string
    purchaseState: number
    promoPrice?: string
    rankTag?: string
    rankUrl?: string
    productId?: number
    marketPrice?: string
    repurchaseCountTag?: string
  }
  buttonInfoList: Array<ItemBut>
}

export interface ItemBut {
  text: string
  jumpUrl: string
  type: number
}

export interface ButtonType {
  title: string
  jumpUrl: string
}

export interface BuyoftenList {
  resultList: []
}

export interface NearbyList {
  cardDTOS: []
  rankDetailDTO: ButtonType
}
