import { mapi, env } from '@mrn/mrn-utils'
import { GuaranteeDetailResponse, PageList } from '../types'
import { getLocationInfo } from '../utils/MRNUtils'
import { oneEnv } from '@nibfe/mc-one-env'
import KNB from '@mrn/mrn-knb'

export interface KNBCityRes {
  cityId: string
  locCityId: string
  type: 'mt' | 'dp'
  cityName: string
  locCityName: string
  areaId: string
  areaName: string
  noCache: boolean
}

const baseURL = env.isBeta ? 'mapi.51ping.com' : 'mapi.dianping.com'

const TAB = `https://${baseURL}/api/beautyapigw/queryoftenbuyheadinfo.bin`
const LIST = `https://${baseURL}/api/beautyapigw/queryuseroftenbuylist.bin`
const OFTEN_LIST = `https://${baseURL}/api/beautyapigw/querynearoftenbuylist.bin`

export function getCity() {
  return new Promise(res => {
    KNB.getCity({
      success: (data: KNBCityRes) => {
        res(data)
      }
    })
  })
}

export const getTab: () => Promise<GuaranteeDetailResponse> = async () => {
  let catyInfo = await getCity()
  const location: any = await getLocationInfo()
  const lat = location?.lat
  const lng = location?.lng
  return mapi({
    url: TAB,
    method: 'GET',
    params: {
      cityId: catyInfo?.cityId,
      lat,
      lng,
      platform: oneEnv.app('dp') ? 1 : 2
    }
  })
    .then(res => {
      if (res.code === 200) {
        return res.data
      }
    })
    .catch((e: any) => {
      throw e
    })
}

// 常买接口
export const getOftenList = async (params: PageList) => {
  let catyInfo = await getCity()
  const location: any = await getLocationInfo()
  const lat = location?.lat
  const lng = location?.lng
  return mapi({
    url: LIST,
    method: 'GET',
    params: {
      cityId: catyInfo?.cityId,
      lat,
      lng,
      platform: oneEnv.app('dp') ? 1 : 2,
      ...params
    }
  })
    .then(res => {
      if (res.code === 200) {
        return res.data
      }
    })
    .catch((e: any) => {
      throw e
    })
}

// 附近回头客推荐
export const getNearbyList = async (params: { categoryId: string }) => {
  let catyInfo = await getCity()
  const location: any = await getLocationInfo()
  const lat = location?.lat
  const lng = location?.lng
  return mapi({
    url: OFTEN_LIST,
    method: 'GET',
    params: {
      cityId: catyInfo?.cityId,
      lat,
      lng,
      platform: oneEnv.app('dp') ? 1 : 2,
      ...params
    }
  })
    .then(res => {
      if (res.code === 200) {
        return res.data
      }
    })
    .catch((e: any) => {
      throw e
    })
}
