import { GlobalComponentReporter } from '@mrn/mcc-component-report'
import { AppRegistry } from '@mrn/react-native'
import Guarantee from './src/pages/Guarantee'
import ProductGuarantee from './src/pages/ProductGuarantee'
import GuaranteePop from './src/pages/GuaranteePop'

// 这里注册的 mrnproject 可以是全集团不冲突的任意名字
GlobalComponentReporter.start({ appKey: 'rn_gcbu_mrn-common-guarantee-page' })
AppRegistry.registerComponent('common-guarantee-pop', () => GuaranteePop)
AppRegistry.registerComponent('common-guarantee-page', () => Guarantee)
AppRegistry.registerComponent('common-poi-guarantee-page', () => ProductGuarantee)
AppRegistry.registerComponent('beauty-poi-guarantee-page', () => ProductGuarantee) // 为了强制用户访问到最新内容，新增一个bundle
