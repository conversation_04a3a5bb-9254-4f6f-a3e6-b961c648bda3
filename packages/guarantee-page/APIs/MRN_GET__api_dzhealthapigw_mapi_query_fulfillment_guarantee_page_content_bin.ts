import { mapi } from '@mrn/mrn-utils'
export interface Query {
  /**
   * 门店id（区分平台）
   */
  shopId?: string
  /**
   * 短订单id
   */
  orderId?: string
  /**
   * 平台：1=点评，2=美团
   */
  platform: number
  /**
   * 查询指定若干保障IP，英文逗号分隔。若传空则根据“displayScene”参数决定默认展示的保障IP。
   */
  guaranteeIp?: string
  /**
   * 展示场景：1=从商详页跳转；2=从团详页跳转；3=从订详页跳转；
   */
  displayScene: number

  // 枚举IOS：1，android：2
  clientOs: number
}
/**
 * “联系商家”按钮
 */
export interface ContactButton {
  /**
   * 文案
   */
  text?: string
  /**
   * 商家电话
   */
  contact?: string
  /**
   * 是否禁用按钮
   */
  disabled?: boolean
  /**
   * 跳转链接
   */
  jumpUrl?: string
}
export interface GuaranteeProductListItems {
  /**
   * 下单门店名称
   */
  shopName?: string
  /**
   * 商品名称
   */
  productName?: string
  /**
   * 券码
   */
  serialNumber?: string
  contactButton?: ContactButton
  customerServiceButton?: ContactButton
  /**
   * 保障时间
   */
  guaranteeTime?: string
  /**
   * 核销门店名称
   */
  verifyShopName?: string
  /**
   * 保障状态：1=待生效，2=生效中，3=已过期，4=已失效
   */
  guaranteeStatus?: number
  /**
   * 商品头图
   */
  productImageUrl?: string
}
export interface SectionListItems {
  /**
   * 段落标题
   */
  title?: string
  /**
   * 正文
   */
  content?: string[]
  /**
   * 段落标题前的icon
   */
  titleIcon?: string
}
/**
 * 页面内容，对应pageAssembleType=1
 */
export interface PageContent1 {
  /**
   * 段落列表
   */
  sectionList?: SectionListItems[]
}
/**
 * 前端埋点信息
 */
export interface Ocean {
  /**
   * 保障IP关联的前端类目id
   */
  frontCategoryId?: number
}
export interface GuaranteeIpListItems {
  ocean?: Ocean
  /**
   * IP名称
   */
  ipName?: string
  /**
   * 标签页名称
   */
  tabName?: string
  pageContent1?: PageContent1
  /**
   * 页面组装方式：1=后端下发完整内容，遵循第一版页面结构；
   */
  pageAssembleType?: number
}
export interface Data {
  /**
   * 保障页头图
   */
  headerPic?: string
  /**
   * 保障页头图比例
   */
  picRatio?: number
  /**
   * 保障IP列表
   */
  guaranteeIpList?: GuaranteeIpListItems[]
  /**
   * 保障商品列表
   */
  guaranteeProductList?: GuaranteeProductListItems[]
}
export interface Response {
  msg?: string
  /**
   * 200=成功，500=失败
   */
  code?: number
  data?: Data
}

// type: mapi
// apiId: 3613
// elink文档地址: https://elink.sankuai.com/main/api-doc-v3/list?groupId=293&id=3613
// 接口名称：查询保障详情页内容
interface MapiOptions {
  // 签名，可选，默认true
  signature?: boolean
  // 防刷单，可选，默认false
  fabricate?: boolean
  // 是否支持幂等，可选，POST默认false，GET默认true
  failOver?: boolean
  // 缓存设置，可选，只有get支持，默认不缓存（0：不缓存，其他：缓存）
  cacheType?: number
  // 请求头
  headers?: {
    // 默认请求头
    picasso?: 'no-js'
    'mrn-version'?: ''
    'mrn-project'?: ''
  }
  // 设置压缩
  compress?: boolean
}
export function MRN_GET__api_dzhealthapigw_mapi_query_fulfillment_guarantee_page_content_bin(
  params: Query,
  mapiOptions?: MapiOptions
): Promise<Response | null> {
  return mapi({
    url: 'https://mapi.meituan.com/api/dzhealthapigw/mapi/query_fulfillment_guarantee_page_content.bin',
    method: 'GET',
    params,
    ...mapiOptions
  }).catch((e: any) => {
    e.userInfo = {
      ...e.userInfo,
      requestInfo: {
        url: 'https://mapi.meituan.com/api/dzhealthapigw/mapi/query_fulfillment_guarantee_page_content.bin'
      }
    }
    throw e
  })
}
