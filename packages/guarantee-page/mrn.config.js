// mrn.config.js 配置文档
// http://mrn.sankuai.com/docs/guide/conf.html#mrn-config-js
let iconfont
try {
  // 由于 talos 流水线 MRN-CAT 插件在 Yarn 插件前，
  // 该插件会 require('mrn.config.js')，但此时尚未安装 npm 依赖。
  // 此时直接忽略错误即可。
  iconfont = require('@max/leez-icon/font').fonts
} catch (e) {}
module.exports = [
  {
    name: 'mrn-common-guarantee-pop',
    main: './index.tsx', // 页面入口地址
    biz: 'gcbu',
    bundleType: 1,
    bundleDependencies: ['@mrn/mrn-base'],
    debugger: {
      moduleName: 'common-guarantee-pop',
      initialProperties: {
        hideNavigationBar: true,
        shopId: 1,
        displayScene: 1,
        guaranteeIp: '5,6,7',
        isTransparent: true,
        hideLoading: true,
        mrn_hideloading: true,
        mrn_hideNextNavBar: true,
        exitAnim: 'mrn_anim_exit_from_top',
        overridePendingTransition: 1,
        enterAnim: 'mrn_anim_enter_from_bottom',
        bundleLevel: 1,
        mrn_transparent: true
      }
    },
    // 转 H5 配置
    one: {
      appConfig: {
        pages: [
          {
            name: 'mrn-common-guarantee-pop',
            path: 'index.tsx',
            enableShareAppMessage: true
          }
        ]
      }
    },
    fonts: {
      ...iconfont
    }
  },
  {
    name: 'mrn-common-guarantee-page',
    main: './index.tsx', // 页面入口地址
    biz: 'gcbu',
    bundleType: 1,
    bundleDependencies: ['@mrn/mrn-base'],
    debugger: {
      moduleName: 'common-guarantee-page',
      initialProperties: {
        hideNavigationBar: true,
        shopId: 1,
        displayScene: 1,
        guaranteeIp: '5,6,7'
      }
    },
    // 转 H5 配置
    one: {
      appConfig: {
        pages: [
          {
            name: 'mrn-common-guarantee-page',
            path: 'index.tsx',
            enableShareAppMessage: true
          }
        ]
      }
    },
    fonts: {
      ...iconfont
    }
  },
  {
    name: 'mrn-common-poi-guarantee-page',
    main: './index.tsx', // 页面入口地址
    biz: 'gcbu',
    bundleType: 1,
    bundleDependencies: ['@mrn/mrn-base'],
    debugger: {
      moduleName: 'common-poi-guarantee-page',
      initialProperties: {
        hideNavigationBar: true,
        displayScene: 3,
        // guaranteeIp: '5,6,7',
        // orderId: '4971996186669656482'
        guaranteeIp: '10',
        orderId: '4981003387880388695'
      }
    },
    // 转 H5 配置
    one: {
      appConfig: {
        pages: [
          {
            name: 'mrn-common-guarantee-page',
            path: 'index.tsx',
            enableShareAppMessage: true
          }
        ]
      }
    },
    fonts: {
      ...iconfont
    }
  },
  {
    name: 'mrn-beauty-poi-guarantee-page',
    main: './index.tsx', // 页面入口地址
    biz: 'gcbu',
    bundleType: 1,
    bundleDependencies: ['@mrn/mrn-base'],
    debugger: {
      moduleName: 'beauty-poi-guarantee-page',
      initialProperties: {
        hideNavigationBar: true,
        displayScene: 3,
        guaranteeIp: '6',
        orderId: '4971997552130085539'
      }
    },
    // 转 H5 配置
    one: {
      appConfig: {
        pages: [
          {
            name: 'mrn-common-guarantee-page',
            path: 'index.tsx',
            enableShareAppMessage: true
          }
        ]
      }
    },
    fonts: {
      ...iconfont
    }
  }
]
