import {FulfillmentGuaranteeContentFragment} from "./FulfillmentGuaranteeContentFragment"
export interface nonnull_t {
	fragmentList: FulfillmentGuaranteeContentFragment.nonnull_t[];    
}
export interface t {
	fragmentList?: FulfillmentGuaranteeContentFragment.t[];    
}
export interface safe_t {
	readonly fragmentList: FulfillmentGuaranteeContentFragment.safe_t[];    
}

export function from(m: t): safe_t { 
	const u = m == null? {} as t : m
	const s = {} as nonnull_t
	s.fragmentList = u.fragmentList == null? [] : u.fragmentList.map((e : FulfillmentGuaranteeContentFragment.t) => { return FulfillmentGuaranteeContentFragment.from(e) })
	return s
}