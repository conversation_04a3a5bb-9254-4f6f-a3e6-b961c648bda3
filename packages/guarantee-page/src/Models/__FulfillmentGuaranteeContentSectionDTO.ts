import { FulfillmentGuaranteeContentParagraphDTO } from './FulfillmentGuaranteeContentParagraphDTO'
export interface nonnull_t {
  richTextContent: FulfillmentGuaranteeContentParagraphDTO.nonnull_t[]
  content: string[]
  titleIcon: string
  title: string
  link: string
}
export interface t {
  richTextContent?: FulfillmentGuaranteeContentParagraphDTO.t[]
  content?: string[]
  titleIcon?: string
  title?: string
  link?: string
}
export interface safe_t {
  readonly richTextContent: FulfillmentGuaranteeContentParagraphDTO.safe_t[]
  readonly content: string[]
  readonly titleIcon: string
  readonly title: string
  readonly link: string
}

export function from(m: t): safe_t {
  const u = m == null ? ({} as t) : m
  const s = {} as nonnull_t
  s.richTextContent =
    u.richTextContent == null
      ? []
      : u.richTextContent.map((e: FulfillmentGuaranteeContentParagraphDTO.t) => {
          return FulfillmentGuaranteeContentParagraphDTO.from(e)
        })
  s.content = u.content || []
  s.titleIcon = u.titleIcon || ''
  s.title = u.title || ''
  s.link = u.link || ''
  return s
}
