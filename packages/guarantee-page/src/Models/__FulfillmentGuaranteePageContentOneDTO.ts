import {FulfillmentGuaranteeContentSectionDTO} from "./FulfillmentGuaranteeContentSectionDTO"
export interface nonnull_t {
	sectionList: FulfillmentGuaranteeContentSectionDTO.nonnull_t[];    
}
export interface t {
	sectionList?: FulfillmentGuaranteeContentSectionDTO.t[];    
}
export interface safe_t {
	readonly sectionList: FulfillmentGuaranteeContentSectionDTO.safe_t[];    
}

export function from(m: t): safe_t { 
	const u = m == null? {} as t : m
	const s = {} as nonnull_t
	s.sectionList = u.sectionList == null? [] : u.sectionList.map((e : FulfillmentGuaranteeContentSectionDTO.t) => { return FulfillmentGuaranteeContentSectionDTO.from(e) })
	return s
}