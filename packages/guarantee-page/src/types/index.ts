export interface GuaranteeRequest {
  shopId?: string
  orderId?: string
  platform?: number
  guaranteeIp?: string
  displayScene: number
  clientOs: number
}

export interface GuaranteeIpList {
  ipName: string
  pageAssembleType: string
  pageContent: SectionList[]
}

export interface SectionList {
  titleIcon: string
  title: string
  content: string[]
}

export interface TabItem {
  label: string
}
