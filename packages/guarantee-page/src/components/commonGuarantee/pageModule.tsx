import React, { useCallback } from 'react'
import { MCListModule } from '@nibfe/doraemon-practice'
import { Block } from './Block'

interface Props {
  guaranteeData: any
  // data: any
  modalDisplay: number
}

export const PageModule: React.FC<Props> = props => {
  const { guaranteeData, modalDisplay } = props
  // const { data, modalDisplay } = props
  // const dataList1 = data[0]
  // const dataList2 = data[1]
  const keyExtractor = useCallback((item, index: number) => {
    return `${index}`
  }, [])
  if (!guaranteeData) return null
  return (
    <MCListModule
      backgroundColor="white"
      paddingHorizontal={0}
      data={guaranteeData[modalDisplay]?.pageContent1?.sectionList}
      // data={modalDisplay === 0 ? dataList1 : dataList2}
      renderItem={item => {
        return <Block item={item} />
      }}
      // eslint-disable-next-line react-native/no-inline-styles
      separatorLineStyle={{
        display: 'only-middle',
        lineColor: '#E1E1E1',
        middleLineMarginHorizontal: 25
      }}
      keyExtractor={keyExtractor}
    />
  )
}
