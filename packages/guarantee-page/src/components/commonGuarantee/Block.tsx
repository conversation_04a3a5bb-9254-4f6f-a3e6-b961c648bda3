import React from 'react'
import { View, Text, Image, StyleSheet, Dimensions } from '@mrn/react-native'
const DimensionWidth = Dimensions.get('window').width

export const Block = params => {
  const innerParams = { ...params }.item
  return (
    <View style={styles.container}>
      <View style={styles.upperLine}>
        <Image style={styles.innerImg} source={{ uri: innerParams.titleIcon }} />
        <Text style={styles.title}>{innerParams.title}</Text>
      </View>
      <Text style={styles.grayText}>{innerParams?.content?.[0]}</Text>
      {innerParams?.content?.[1] && (
        <>
          <Text style={styles.notice}>{innerParams?.content?.[1]}</Text>
        </>
      )}
    </View>
  )
}

// export const Block = params => {
//   const innerParams = { ...params }
//   return (
//     <View style={styles.container}>
//       <View style={styles.upperLine}>
//         <Image style={styles.innerImg} source={{ uri: innerParams.item.picLink }} />
//         <Text style={styles.title}>{innerParams.item.title}</Text>
//       </View>
//       <Text style={styles.grayText}>{innerParams.item.text}</Text>
//       {innerParams.item.notice && (
//         <>
//           <Text style={styles.notice}>{innerParams.item.notice}</Text>
//         </>
//       )}
//     </View>
//   )
// }

const styles = StyleSheet.create({
  container: {
    alignSelf: 'center',
    width: DimensionWidth - 30,
    padding: 12,
    paddingBottom: 18
    // marginTop: 12
  },
  upperLine: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  innerImg: {
    height: 18,
    width: 15.5
  },
  title: {
    fontFamily: 'PingFang SC',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 7.25
  },
  grayText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    fontWeight: '400',
    color: '#666666',
    marginTop: 11
  },
  notice: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    fontWeight: '400',
    color: '#666666',
    marginTop: 9
  }
})
