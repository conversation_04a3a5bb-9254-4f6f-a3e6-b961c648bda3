import React from 'react'
import { View, StyleSheet, TouchableOpacity, Image, StatusBar } from '@mrn/react-native'
import { getWidth } from '@mrn/mrn-gc-utils'
import { pageRouterClose } from '@mrn/mrn-utils'
import { getStatusBarHeight } from '@nibfe/dm-navigation'
export const Navigation = () => {
  return (
    <View style={styles.extraView}>
      <StatusBar backgroundColor="transparent" barStyle="dark-content" translucent />
      <TouchableOpacity onPress={() => pageRouterClose()} style={styles.topRow} activeOpacity={1}>
        <Image
          source={{
            uri: 'https://p0.meituan.net/travelcube/59db9140d45f11d26ef8b5383d54de981540.png'
          }}
          // eslint-disable-next-line react-native/no-inline-styles
          style={{
            width: 29,
            height: 29
          }}
        />
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  extraView: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 20
  },
  topRow: {
    width: getWidth(),
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 7,
    marginTop: getStatusBarHeight()
  }
})
