import React, { useEffect } from 'react'
import { View, ImageBackground, StyleSheet, Dimensions } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { Tab } from '@nibfe/gc-ui'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import MRNUtils from '@mrn/mrn-utils'
import { TabItem } from '../../types/index'
import { cid, pageInfoKey } from '../../MGE/oceanConfig'

interface Props {
  bgImage?: string
  ipList: any[]
  setModalDisplay: (value: number) => void
  modalDisplay: number
  mrnComponent?: string
}

const DimensionWidth = Dimensions.get('window').width
export const BackgroundModule: React.FC<Props> = props => {
  const { bgImage, ipList, setModalDisplay, modalDisplay } = props
  let data: TabItem[] = []
  if (ipList && ipList.length) {
    data = ipList.map(a => ({ label: a.tabName }))
  }
  useEffect(() => {
    data?.map((item, index) => {
      MRNUtils.lxTrackModuleView({
        channelName: 'gc',
        pageInfoKey: pageInfoKey,
        val_bid: 'b_gc_8o21t1yb_mv',
        val_cid: cid,
        val_lab: {
          tab_index: index,
          tab_name: item.label
          // mrnComponent
        }
      })
    })
  }, [data])

  const backImgUrl =
    bgImage !== ''
      ? bgImage
      : 'https://p0.meituan.net/travelcube/bca6fd438e8fa8868fbea1a60d863b5f136999.jpg'
  return (
    <MCModule>
      <ImageBackground
        style={styles.backgroundImage}
        source={{
          uri: backImgUrl
        }}
      />
      {Boolean(data?.length > 1) && (
        <View style={styles.tab}>
          <Tab
            data={data}
            // eslint-disable-next-line react-native/no-inline-styles
            defaultItemStyle={{ textSize: 16, textColor: '#222222', fontWeight: '500' }}
            colGap={30}
            initialIndex={0}
            isEqualized={false}
            // slideBar={
            //   <LinearGradient
            //     start={{ x: 0, y: 0 }}
            //     end={{ x: 1, y: 1 }}
            //     colors={['#0cd0c2', '#05d1b5']}
            //     style={styles.gradientBtn}
            //   />
            // }
            // eslint-disable-next-line react-native/no-inline-styles
            slideBarStyle={{
              slideBarHeight: 5,
              slideBarIsRounded: true
            }}
            onItemSelect={(_, index) => {
              setModalDisplay(index)
              MRNUtils.lxTrackModuleClick({
                channelName: 'gc',
                pageInfoKey: pageInfoKey,
                val_bid: 'b_gc_8o21t1yb_mc',
                val_cid: cid,
                val_lab: {
                  tab_index: index,
                  tab_name: data[index]?.label
                  // mrnComponent
                }
              })
            }}
          />
        </View>
      )}
    </MCModule>
  )
}

const styles = StyleSheet.create({
  backgroundImage: {
    width: '100%',
    resizeMode: 'contain',
    height: 220
  },
  tab: {
    marginTop: -40,
    marginLeft: 15,
    width: DimensionWidth - 30,
    alignSelf: 'center'
  },
  gradientBtn: {
    width: 36,
    height: 8,
    borderRadius: 2
  }
})
