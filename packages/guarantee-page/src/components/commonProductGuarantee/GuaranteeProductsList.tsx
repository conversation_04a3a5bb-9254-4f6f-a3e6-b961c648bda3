import React, { useCallback } from 'react'
import { View, StyleSheet, Image, Text, TouchableOpacity } from '@mrn/react-native'
import { MCListModule } from '@nibfe/doraemon-practice'
import Button from '@max/leez-button'
import MRNUtils from '@mrn/mrn-utils'
import { cid_p, pageInfoKey_p } from '../../MGE/oceanConfig'

enum GuaranteeStatus {
  UNKNOWN = 0, // '未知'
  PENDING = 1, // '待生效'
  ACTIVE = 2, // '生效中'
  EXPIRED = 3, // '已过期'
  INVALIDATED = 4 // '已失效'
}

export const GuaranteeProductsList = props => {
  const { guaranteeProductsList, evokoPhoneCall, shopId, ipName, mrnComponent } = props
  const renderButton = useCallback(
    (productItem: any, buttonInfo: any, isPhone: boolean = true) => {
      if (!buttonInfo) return null
      return (
        <TouchableOpacity
          style={{ flex: 1 }}
          activeOpacity={1}
          onPress={() => {
            buttonInfo?.jumpUrl && MRNUtils.openUrl(buttonInfo?.jumpUrl)
            buttonInfo?.contact && evokoPhoneCall && evokoPhoneCall(buttonInfo?.contact)
            MRNUtils.lxTrackModuleClick({
              channelName: 'gc',
              pageInfoKey: pageInfoKey_p,
              val_bid: 'b_gc_sq63w86o_mc',
              val_cid: cid_p,
              val_lab: {
                poi_id: shopId,
                title: ipName
                // mrnComponent
              }
            })
          }}
        >
          <View
            style={[
              styles.buttonWrapper,
              {
                opacity: [GuaranteeStatus.PENDING, GuaranteeStatus.ACTIVE].includes(
                  productItem?.guaranteeStatus
                )
                  ? 1
                  : 0.6,
                marginLeft: !isPhone ? 8 : 0
              }
            ]}
          >
            <Image
              source={{
                uri:
                  buttonInfo.icon ||
                  (isPhone
                    ? 'https://p0.meituan.net/medicalvenus/1cd73ae7a89a0dba8bc44b31dde148c21569.png'
                    : 'https://p0.meituan.net/medicalvenus/8dcb436904902310f103def7e16593ba1096.png')
              }}
              style={styles.buttonImg}
            />
            <Text style={styles.buttonText}>{buttonInfo?.text}</Text>
          </View>
        </TouchableOpacity>
      )
    },
    [evokoPhoneCall, ipName, shopId]
  )
  const renderProductItem = useCallback(
    (productItem: any) => {
      return (
        <View style={styles.cardContainer}>
          <Text style={styles.titleText}>{'保障商品'}</Text>
          <View style={styles.productInfoContainer}>
            <View style={styles.productImgContainer}>
              <Image
                source={{
                  uri: productItem?.productImageUrl
                }}
                style={{
                  width: 70,
                  height: 70
                }}
              />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.productNameText}>{productItem.productName}</Text>
              <Text style={[styles.productInfoText, { marginTop: 6 }]}>
                {'券码：' + productItem.productId}
              </Text>
              {Boolean(productItem.verifyTime) && (
                <Text style={[styles.productInfoText, { marginTop: 6 }]}>
                  {'验券：' + productItem.verifyTime}
                </Text>
              )}
              {Boolean(
                productItem?.guaranteeStatus === GuaranteeStatus.INVALIDATED && productItem.shopName
              ) && (
                <Text style={styles.productInfoText}>{'下单门店：' + productItem.shopName}</Text>
              )}
              {Boolean(
                productItem?.guaranteeStatus === GuaranteeStatus.INVALIDATED &&
                  productItem.useShopName
              ) &&
                Boolean(productItem?.useShopName) && (
                  <Text style={styles.productInfoText}>
                    {'验券门店：' + productItem.useShopName}
                  </Text>
                )}
              {(productItem?.guaranteeStatus === GuaranteeStatus.ACTIVE ||
                productItem?.guaranteeStatus === GuaranteeStatus.EXPIRED) &&
                productItem.guaranteeTime && (
                  <Text style={styles.productInfoText}>
                    {'保障时间：' + productItem.guaranteeTime}
                  </Text>
                )}
            </View>
          </View>
          <View style={styles.buttonSection}>
            {renderButton(productItem, productItem?.contactButton, true)}
            {renderButton(productItem, productItem?.customerServiceButton, false)}
          </View>
        </View>
      )
    },
    [evokoPhoneCall, shopId]
  )
  return (
    <MCListModule
      data={guaranteeProductsList}
      renderItem={renderProductItem}
      backgroundColor={'#F4F4F400'}
      onItemAppear={index => {
        MRNUtils.lxTrackModuleView({
          channelName: 'gc',
          pageInfoKey: pageInfoKey_p,
          val_bid: 'b_gc_sq63w86o_mv',
          val_cid: cid_p,
          val_lab: {
            poi_id: shopId,
            title: ipName
            // mrnComponent
          }
        })
      }}
    />
  )
}

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 9,
    padding: 12,
    marginHorizontal: 10,
    marginBottom: 10
  },
  titleText: {
    color: '#222222',
    fontWeight: '500',
    fontSize: 16,
    fontFamily: 'PingFangSC-Medium'
  },
  productInfoContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 6
  },
  productImgContainer: {
    width: 70,
    height: 70,
    borderRadius: 3,
    overflow: 'hidden',
    marginRight: 10
  },
  productNameText: {
    color: '#222222',
    fontWeight: '500',
    fontSize: 13,
    fontFamily: 'PingFangSC-Medium'
  },
  productInfoText: {
    color: '#999999',
    fontWeight: '400',
    fontSize: 12,
    marginTop: 3
  },
  buttonStyle: {
    width: '100%',
    marginTop: 12,
    fontWeight: '600'
  },
  buttonTextStyle: {
    fontWeight: '600'
  },
  buttonSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12
  },
  buttonWrapper: {
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    borderWidth: 0.5,
    borderColor: '#cccccc',
    borderRadius: 22
  },
  buttonImg: {
    width: 18,
    height: 18,
    marginRight: 4
  },
  buttonText: {
    color: '#111111',
    fontSize: 16
  }
})
