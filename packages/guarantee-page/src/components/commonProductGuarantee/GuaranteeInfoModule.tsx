import React from 'react'
import { View, StyleSheet, Text } from '@mrn/react-native'

export const GuaranteeInfoModule = props => {
  const { guaranteeInfoList } = props

  if (guaranteeInfoList && guaranteeInfoList?.length > 0)
    return (
      <View style={styles.cardContainer}>
        <Text style={styles.titleText}>{'保障内容'}</Text>
        {guaranteeInfoList?.map((item, index) => {
          return (
            <View key={index}>
              <Text style={styles.subTitleText}>
                {'(' + String(index + 1) + ')  ' + item.title}
              </Text>
              {Boolean(item?.richTextContent) &&
                item?.richTextContent?.map(richTextContent => {
                  return (
                    <Text style={{ marginTop: 6 }}>
                      {richTextContent?.fragmentList?.map(fragment => {
                        return (
                          <Text style={[styles.contentText, { color: fragment.color }]}>
                            {fragment.text}
                          </Text>
                        )
                      })}
                    </Text>
                  )
                })}
              {Boolean(item?.text && !item?.richTextContent) &&
                item?.text?.map(_text => {
                  return <Text style={styles.contentText}>{_text}</Text>
                })}
            </View>
          )
        })}
      </View>
    )
  else return null
}

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 9,
    padding: 12,
    marginHorizontal: 10
  },
  titleText: {
    color: '#222222',
    fontWeight: '500',
    fontSize: 16,
    fontFamily: 'PingFangSC-Medium'
  },
  subTitleText: {
    color: '#222222',
    fontWeight: '400',
    fontSize: 13,
    marginTop: 12
  },
  contentText: {
    color: '#666666',
    fontWeight: '400',
    fontSize: 12,
    marginTop: 6,
    lineHeight: 16
  }
})
