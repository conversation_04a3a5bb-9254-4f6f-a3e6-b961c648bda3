import React from 'react'
import { View, StyleSheet, TouchableOpacity, Image, StatusBar, Text } from '@mrn/react-native'
import { getWidth } from '@mrn/mrn-gc-utils'
import { pageRouterClose } from '@mrn/mrn-utils'
import { getStatusBarHeight } from '@nibfe/dm-navigation'
export const SimpleNavigationBar = props => {
  const { navBarOpacity } = props
  return (
    <View style={[styles.extraView, { backgroundColor: `rgba(255, 255, 255, ${navBarOpacity})` }]}>
      <StatusBar backgroundColor="transparent" barStyle="dark-content" translucent />
      <View style={styles.topRow}>
        <TouchableOpacity onPress={() => pageRouterClose()} activeOpacity={1}>
          <Image
            source={{
              uri: 'https://p0.meituan.net/travelcube/59db9140d45f11d26ef8b5383d54de981540.png'
            }}
            style={{
              width: 29,
              height: 29
            }}
          />
        </TouchableOpacity>
        {navBarOpacity === 1 && (
          <View style={styles.titleContainer}>
            <Text style={styles.titleText}>{'保障详情'}</Text>
          </View>
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  extraView: {
    position: 'absolute',
    flexDirection: 'row',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 20
  },
  topRow: {
    width: getWidth(),
    height: 44,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 7,
    marginTop: getStatusBarHeight()
  },
  titleContainer: {
    flex: 1,
    marginRight: 44,
    alignItems: 'center',
    justifyContent: 'center'
  },
  titleText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#202020'
  }
})
