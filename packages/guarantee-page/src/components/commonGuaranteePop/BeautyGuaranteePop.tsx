import React from 'react'
import { MCModule } from '@nibfe/doraemon-practice'
import { getSafeAreaBottom } from '@mrn/mrn-gc-utils'
import { FulfillmentGuaranteePageContentOneDTO } from '../../Models/FulfillmentGuaranteePageContentOneDTO'
import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions } from '@mrn/react-native'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import MRNUtil from '@mrn/mrn-utils'
import SlideModal from '@max/leez-slide-modal'
const screenWidth = Dimensions.get('window').width

/**
 * 丽人保障弹窗
 */
interface Props {
  visible: boolean
  data: FulfillmentGuaranteePageContentOneDTO.safe_t
  onClosePop: () => void
}

export const GuaranteePopModule: React.FC<Props> = props => {
  const { visible, data, onClosePop } = props
  return (
    <MCModule>
      <SlideModal
        visible={visible}
        showHeader={false}
        showCloseBtn={false}
        addBottomSpace={false}
        maskClosable={true}
        contentUseScrollview={true}
        modalMaxHeight={'80%'}
        onClosePress={onClosePop}
        onBackPress={onClosePop}
        theme={{
          spacing: {
            headerHorizontal: 0,
            contentHorizontal: 0
          }
        }}
      >
        <View style={styles.container}>
          <View style={styles.headerPicWrapper}>
            <Image
              source={{
                uri: 'https://p0.meituan.net/travelcube/f4247609e59bf70e208ef2a6d3f8ae8092338.png@750w_80q'
              }}
              style={styles.headerPic}
            />
            <TouchableOpacity
              onPress={() => {
                onClosePop()
              }}
              style={styles.closeIconWrapper}
            >
              <Image
                source={{
                  uri: 'https://p0.meituan.net/travelcube/70ba6e76ef431261885bc848dff5773e635.png'
                }}
                style={styles.closeIcon}
              />
            </TouchableOpacity>
          </View>
          <LinearGradient
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            colors={['#FFE8E7', '#FFFFFF']}
            style={styles.contentWrapper}
          >
            <View style={styles.innerWrapper}>
              <LinearGradient
                start={{ x: 0, y: 0 }}
                end={{ x: 0, y: 1 }}
                colors={['#FFFFFF57', '#FFFFFF']}
                locations={[0, 0.2]}
                style={{ borderTopLeftRadius: 10, borderTopRightRadius: 10 }}
              >
                {data?.sectionList?.map((item, index) => {
                  return (
                    <View style={styles.item} key={index}>
                      <View style={styles.titleItem}>
                        <View style={styles.titleItemLeft}>
                          <Image
                            source={{
                              uri: item.titleIcon
                            }}
                            style={{
                              width: 18,
                              height: 18,
                              marginRight: 3
                            }}
                          />
                          <Text style={styles.title}>{item.title}</Text>
                        </View>
                        <TouchableOpacity
                          style={styles.titleItemRight}
                          onPress={() => {
                            item.link && MRNUtil.openUrl(item.link)
                            onClosePop()
                          }}
                        >
                          <Text style={styles.detailTag}>{'详情'}</Text>
                          <Image
                            source={{
                              uri: 'https://p0.meituan.net/travelcube/208d46be818f3fa87e73955bc2ede35d393.png'
                            }}
                            style={{
                              width: 12,
                              height: 12
                            }}
                          />
                        </TouchableOpacity>
                      </View>
                      <Text style={styles.content}>{item.content}</Text>
                    </View>
                  )
                })}
              </LinearGradient>
            </View>
          </LinearGradient>
          <TouchableOpacity
            onPress={() => {
              onClosePop()
            }}
            style={styles.buttonWrapper}
          >
            <LinearGradient
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              colors={['#FF7700', '#FF4B10']}
              style={styles.button}
            >
              <Text style={styles.buttonText}>{'我知道了'}</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </SlideModal>
    </MCModule>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingBottom: getSafeAreaBottom(),
    backgroundColor: '#fff',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12
  },
  headerPicWrapper: {
    width: screenWidth,
    height: screenWidth / 5.68,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10
  },
  headerPic: {
    width: screenWidth,
    height: screenWidth / 5.68,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10
  },
  closeIconWrapper: {
    position: 'absolute',
    right: 12,
    top: 12
  },
  closeIcon: {
    width: 24,
    height: 24
  },
  contentWrapper: {},
  item: {
    display: 'flex',
    marginBottom: 15,
    paddingHorizontal: 12,
    paddingTop: 15
  },
  innerWrapper: {
    marginHorizontal: 9,
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
    backgroundColor: 'transparent',
    marginBottom: 60,
    borderWidth: 1,
    borderColor: '#ffffff'
  },
  titleItem: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'nowrap',
    marginBottom: 10
  },
  titleItemLeft: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'nowrap'
  },
  titleItemRight: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'nowrap'
  },
  title: {
    color: '#222222',
    fontWeight: '500',
    fontSize: 16
  },
  detailTag: {
    color: '#222222',
    fontWeight: '400',
    fontSize: 12
  },
  content: {
    color: '#999999',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 1.4 * 14
  },
  buttonWrapper: {
    position: 'absolute',
    bottom: getSafeAreaBottom(),
    marginBottom: 10,
    left: 0,
    right: 0,
    height: 44
  },
  button: {
    marginHorizontal: 15,
    height: 44,
    borderRadius: 22
  },
  buttonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 44,
    textAlign: 'center'
  }
})
