/* eslint-disable react-native/no-inline-styles */
import React, { useMemo, useState } from 'react'
import { AnimatedMCPage } from '@nibfe/doraemon-practice'
import TopViewProvider from '@max/leez-top-view-provider'
import { isDP, IS_IOS } from '@mrn/mrn-gc-utils'
import { useFetchHooks } from '../hooks/fetchHooks'
import { Provider } from '@nibfe/gc-ui'
import { GuaranteePopModule } from '../components/commonGuaranteePop/BeautyGuaranteePop'
import MRNUtil from '@mrn/mrn-utils'
import { View } from '@mrn/react-native'

interface Props {
  shopId: string
  orderId: string
  displayScene: string
  guaranteeIp: string
  mrn_component?: string
}

const GuaranteePop: React.FC<Props> = props => {
  const [visible, setVisible] = useState(true)
  const { shopId, orderId, displayScene, guaranteeIp } = props

  const { ipList } = useFetchHooks({
    guaranteeParams: {
      shopId,
      orderId,
      platform: isDP() ? 1 : 2,
      displayScene: displayScene,
      guaranteeIp: guaranteeIp,
      clientOs: IS_IOS ? 1 : 2
    }
  })

  const detailData = useMemo(() => {
    return ipList?.[0]?.pageContent1
  }, [ipList])

  const closePop = () => {
    setVisible(false)
    MRNUtil.close()
  }

  return (
    <Provider theme={''}>
      <TopViewProvider>
        <AnimatedMCPage
          style={{ backgroundColor: 'transparent' }}
          cardStyle={{
            backgroundColor: 'transparent'
          }}
          enableBounce={false}
          scrollEventThrottle={1}
          paddingHorizontal={0}
          pageBottomGap={30}
          contentBackgroundColor={'transparent'}
          separatorLineStyle={{
            display: 'hidden-all'
          }}
          pageTopGap={0}
          pageGap={0}
          modules={[
            {
              moduleKey: 'PopModule',
              module: detailData ? (
                <GuaranteePopModule data={detailData} onClosePop={closePop} visible={visible} />
              ) : null
            }
          ]}
        />
      </TopViewProvider>
    </Provider>
  )
}

export default GuaranteePop
