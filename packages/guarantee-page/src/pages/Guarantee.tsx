/* eslint-disable react-native/no-inline-styles */
import React, { useState } from 'react'
import { View } from '@mrn/react-native'
import { AnimatedMCPage } from '@nibfe/doraemon-practice'
import TopViewProvider from '@max/leez-top-view-provider'
import { Navigation } from '../components/commonGuarantee/Navigation'
import { isDP, IS_IOS } from '@mrn/mrn-gc-utils'
import { useFetchHooks } from '../hooks/fetchHooks'
// import { useMock } from '../hooks/fetchHooks'
import { PageModule } from '../components/commonGuarantee/pageModule'
import { BackgroundModule } from '../components/commonGuarantee/BackgroundModule'
import { Provider } from '@nibfe/gc-ui'
import LoadingView from '@max/leez-loading-view'

interface Props {
  shopId: string
  orderId: string
  displayScene: string
  guaranteeIp: string
  mrn_component?: string
}

const Guarantee: React.FC<Props> = props => {
  const { shopId, orderId, displayScene, guaranteeIp, mrn_component } = props
  const { bgImage, ipList } = useFetchHooks({
    guaranteeParams: {
      shopId,
      orderId,
      platform: isDP() ? 1 : 2,
      displayScene: displayScene,
      guaranteeIp: guaranteeIp,
      clientOs: IS_IOS ? 1 : 2
    }
  })
  // const totalData = useMock()
  const [modalDisplay, setModalDisplay] = useState(0)
  if (bgImage === '')
    return (
      <View
        style={{
          height: '100%',
          width: '100%',
          backgroundColor: 'white',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <LoadingView level={'large'} />
      </View>
    )
  return (
    <Provider>
      <TopViewProvider>
        <Navigation />
        <View style={{ height: '100%', width: '100%' }}>
          <AnimatedMCPage
            mptInfo={{
              cid: 'c_gc_j2t97lut',
              category: 'gc',
              labs: {
                cat_id: ipList?.length > 0 ? ipList[0]?.ocean?.frontCategoryId : -1
              }
            }}
            enableBounce={false}
            scrollEventThrottle={1}
            paddingHorizontal={0}
            pageBottomGap={30}
            contentBackgroundColor={'white'}
            separatorLineStyle={{
              display: 'hidden-all'
            }}
            pageTopGap={0}
            pageGap={0}
            modules={[
              {
                moduleKey: 'BackgroundModule',
                module: (
                  <BackgroundModule
                    setModalDisplay={setModalDisplay}
                    modalDisplay={modalDisplay}
                    ipList={ipList}
                    bgImage={bgImage}
                    mrnComponent={mrn_component}
                  />
                )
                // module: <BackgroundModule setModalDisplay={setModalDisplay} />
              },
              {
                moduleKey: 'PageModule',
                module: <PageModule guaranteeData={ipList} modalDisplay={modalDisplay} />
                // module: <PageModule data={totalData} modalDisplay={modalDisplay} />
              }
            ]}
          />
        </View>
      </TopViewProvider>
    </Provider>
  )
}

export default Guarantee
