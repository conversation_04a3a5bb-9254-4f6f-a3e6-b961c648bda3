// import React, { useRef } from 'react'
import React, { useState, useCallback, useMemo } from 'react'
import { Image } from '@mrn/react-native'
import { MCPage, MCModule } from '@nibfe/doraemon-practice'
import TopViewProvider from '@max/leez-top-view-provider'
import { SimpleNavigationBar } from '../components/commonProductGuarantee/SimpleNavigationBar'
// import { useFetchHooks, useMock } from './hooks/fetchHooks'
// import { isDP } from '@mrn/mrn-gc-utils'
import { Provider } from '@nibfe/gc-ui'
import { GuaranteeInfoModule } from '../components/commonProductGuarantee/GuaranteeInfoModule'
import { getWidth, isDP, IS_IOS } from '@mrn/mrn-gc-utils'
import { GuaranteeProductsList } from '../components/commonProductGuarantee/GuaranteeProductsList'
import PhoneModal from '@max/leez-phone-modal'
import { useFetchHooks } from '../hooks/fetchHooks'
import { getStatusBarHeight } from '@nibfe/dm-navigation'

interface Props {
  shopId: string
  moduleKey: string
  bizParam?: string
  orderId: string
  displayScene: string
  guaranteeIp: string
  mrn_component?: string
}
const AspectRatio = 1.32 // 头部比例
const NavHeight = 44
const StatusBarHeight = getStatusBarHeight()

const ProductGuarantee: React.FC<Props> = props => {
  const { shopId, orderId, displayScene, guaranteeIp } = props
  const { bgImage, bgImageRatio, ipList, productList } = useFetchHooks({
    guaranteeParams: {
      shopId: shopId,
      orderId,
      platform: isDP() ? 1 : 2,
      displayScene: displayScene,
      guaranteeIp: guaranteeIp,
      clientOs: IS_IOS ? 1 : 2
    }
  })
  const aspectRatio = bgImageRatio || AspectRatio
  const ipName = useMemo(() => {
    if (!ipList || ipList?.length < 1) return '后端没有ip类'
    return ipList[0]?.ipName
  }, [ipList])

  const guaranteeInfoList = useMemo(() => {
    if (!ipList || ipList?.length < 1) return []
    return ipList[0]?.pageContent1?.sectionList?.map(item => {
      return {
        title: item?.title,
        picLink: item?.titleIcon,
        text: item?.content,
        richTextContent: item?.richTextContent
      }
    })
  }, [ipList])

  const guaranteeProductsList = useMemo(() => {
    if (!productList || productList?.length < 1) return []
    return productList?.map(item => {
      return {
        productImageUrl: item?.productImageUrl,
        productName: item?.productName,
        productId: item?.serialNumber,
        shopName: item?.shopName,
        useShopName: item?.verifyShopName,
        guaranteeStatus: item?.guaranteeStatus,
        guaranteeTime: item?.guaranteeTime,
        verifyTime: item?.verifyTime,
        jumpUrl: item?.contactButton?.jumpUrl,
        status: item?.contactButton?.disabled ? 0 : 1,
        phoneNumber: item?.contactButton?.contact,
        contactButtonText: item?.contactButton?.text,
        contactButton: item?.contactButton,
        customerServiceButton: item?.customerServiceButton
      }
    })
  }, [productList])

  const [phoneVisible, setPhoneVisible] = useState(false)
  const [curPhoneNumber, setCurPhoneNumber] = useState('')
  const evokoPhoneCall = useCallback((phoneNumber: string) => {
    setPhoneVisible(true)
    setCurPhoneNumber(phoneNumber)
  }, [])

  const [navBarOpacity, setNavBarOpacity] = useState(0)
  const bannerHeight = getWidth() / AspectRatio - NavHeight - StatusBarHeight
  return (
    <Provider>
      <TopViewProvider>
        <SimpleNavigationBar navBarOpacity={navBarOpacity} />
        <MCPage
          mptInfo={{
            cid: 'c_gc_kwss2l0i',
            category: 'gc',
            labs: {
              cat_id: ipList?.length > 0 ? ipList[0]?.ocean?.frontCategoryId : -1
            }
          }}
          onScroll={event => {
            const curOpacity = (1.68 * event.nativeEvent.contentOffset.y) / bannerHeight
            setNavBarOpacity(curOpacity > 1 ? 1 : curOpacity)
          }}
          showScrollIndicator={false}
          enableBounce={false}
          scrollEventThrottle={1}
          paddingHorizontal={0}
          pageBottomGap={10}
          contentBackgroundColor={'#F4F4F4'}
          contentBackgroundView={
            <Image
              source={{
                uri: bgImage
              }}
              style={{
                width: '100%',
                aspectRatio
              }}
            />
          }
          separatorLineStyle={{
            display: 'hidden-all'
          }}
          pageTopGap={bannerHeight}
          pageGap={0}
          modules={[
            {
              moduleKey: 'MCModuleListGuaranteeProducts',
              module: (
                <GuaranteeProductsList
                  guaranteeProductsList={guaranteeProductsList}
                  evokoPhoneCall={evokoPhoneCall}
                  shopId={shopId}
                  ipName={ipName}
                />
              )
            },
            {
              moduleKey: 'GuaranteeInfos',
              module: (
                <MCModule backgroundColor={'#F4F4F400'} zPosition={20}>
                  <GuaranteeInfoModule guaranteeInfoList={guaranteeInfoList} />
                </MCModule>
              )
            }
          ]}
        />
        <PhoneModal
          visible={phoneVisible}
          phoneNumber={curPhoneNumber}
          autoCall={true}
          onCancelPress={() => setPhoneVisible(false)}
        />
      </TopViewProvider>
    </Provider>
  )
}

export default ProductGuarantee
