import { useState, useEffect, useCallback } from 'react'
import { MRN_GET__api_dzhealthapigw_mapi_query_fulfillment_guarantee_page_content_bin } from '../../APIs/MRN_GET__api_dzhealthapigw_mapi_query_fulfillment_guarantee_page_content_bin'
import { GuaranteeRequest } from '../types'

export const useFetchHooks = params => {
  const [ipList, setIpList] = useState([])
  const [productList, setProductList] = useState([])

  const [bgImage, setBgImage] = useState('')
  const [bgImageRatio, setBgImageRatio] = useState(0)
  const getGuaranteeData = useCallback((_params: GuaranteeRequest) => {
    MRN_GET__api_dzhealthapigw_mapi_query_fulfillment_guarantee_page_content_bin({
      shopId: _params.shopId,
      orderId: _params.orderId,
      platform: _params.platform,
      guaranteeIp: _params.guaranteeIp,
      displayScene: _params.displayScene,
      clientOs: _params.clientOs
    }).then(res => {
      // setGuaranteeData(res?.data?.guaranteeIpList)
      const { guaranteeIpList, guaranteeProductList, headerPic, picRatio } = res?.data || {}
      setBgImage(headerPic)
      setBgImageRatio(picRatio ? +picRatio : 0)
      setIpList(guaranteeIpList)
      setProductList(guaranteeProductList)
      console.log('res: ', res)
    })
  }, [])

  useEffect(() => {
    getGuaranteeData(params?.guaranteeParams)
  }, [])

  return {
    bgImage,
    bgImageRatio,
    ipList,
    productList
  }
}
