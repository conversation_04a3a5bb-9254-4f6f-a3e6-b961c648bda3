import React, { useState, useEffect, useCallback } from 'react'
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  Dimensions,
  Image
} from '@mrn/react-native'
import { MCWaterfallListModule, MCLoadingStatusString } from '@nibfe/doraemon-practice'
import { openUrl, lxTrackMGEViewEvent, lxTrackMGEClickEvent } from '@mrn/mrn-utils'
import { MRN_GET_Mapi_nibmp_mcp_gateway_content_feeds_manicure_contentfeedslist } from 'APIs/MRN_GET_Mapi_nibmp_mcp_gateway_content_feeds_manicure_contentfeedslist'
import { ManicureContentItem } from 'Models/ManicureContentItem'
import { ManicureContentResp } from 'Models/ManicureContentResp'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { IS_WEB } from '@mrn/mrn-gc-utils'

interface Props {
  platform?: number
  location?: any
  themeId?: number
  sortRule?: number
  dealTagId?: string
}

interface ListState {
  listData: ManicureContentItem.t[]
  loadingStatus: MCLoadingStatusString
  loadingMoreStatus: MCLoadingStatusString
}
const cardWidth = (Dimensions.get('window').width - 18) / 2

export const WaterListModule: React.FC<Props> = props => {
  const { location, platform, themeId, sortRule, dealTagId } = props
  const [listState, setListState] = useState<ListState>({
    listData: [],
    loadingStatus: 'loading',
    loadingMoreStatus: 'done'
  })
  const listParams = React.useRef({
    h5Flag: IS_WEB ? 1 : 0,
    cityId: location?.cityId,
    lng: location?.lng,
    lat: location?.lat,
    pageStart: 0,
    pageSize: 10,
    platform,
    themeId,
    sortRule,
    dealTagId
  })
  const setListParams = useCallback(
    newParams => {
      listParams.current = {
        ...listParams.current,
        ...newParams
      }
    },
    [listParams]
  )

  // 加载列表数据
  const fetchListData = useCallback(() => {
    setListState(preState => ({
      ...preState,
      loadingStatus: 'loading'
    }))
    const params = {
      ...listParams.current
    }
    MRN_GET_Mapi_nibmp_mcp_gateway_content_feeds_manicure_contentfeedslist(params)
      .then((res: ManicureContentResp.t) => {
        if (res?.commonResp?.code === 200) {
          const { contentFeedsList, isEnd } = res || {}
          setListState(preState => ({
            ...preState,
            listData: contentFeedsList?.length ? [...contentFeedsList] : [],
            loadingStatus: 'done',
            loadingMoreStatus: isEnd ? 'done' : 'loading'
          }))
          if (!isEnd) {
            setListParams({ pageStart: listParams.current.pageStart + 1 })
          }
        } else {
          setListState(preState => ({
            ...preState,
            loadingStatus: 'done',
            loadingMoreStatus: 'done'
          }))
        }
      })
      .catch(() => {
        setListState(preState => ({
          ...preState,
          loadingStatus: 'done', // 'fail', 改成'done'是为了方便用假数据
          loadingMoreStatus: 'done'
        }))
      })
  }, [setListParams])
  // 加载更多list数据
  const fetchMoreListData = useCallback(() => {
    const params = {
      ...listParams.current
    }
    MRN_GET_Mapi_nibmp_mcp_gateway_content_feeds_manicure_contentfeedslist(params)
      .then((res: ManicureContentResp.t) => {
        if (res?.commonResp?.code === 200 && res?.contentFeedsList) {
          const { contentFeedsList, isEnd } = res || {}
          setListState(preState => ({
            ...preState,
            listData: [...preState.listData, ...contentFeedsList],
            loadingStatus: 'done',
            loadingMoreStatus: isEnd ? 'done' : 'loading'
          }))
          if (!isEnd) {
            setListParams({ pageStart: listParams.current.pageStart + 1 })
          }
        } else {
          setListState(preState => ({
            ...preState,
            loadingStatus: 'done',
            loadingMoreStatus: 'done'
          }))
        }
      })
      .catch(() => {
        setListState(preState => ({
          ...preState,
          loadingStatus: 'done',
          loadingMoreStatus: 'fail'
        }))
      })
  }, [setListParams])

  const string2number = str => {
    if (typeof str === 'string' && !isNaN(Number(str))) {
      return Number(str)
    }
    return 0
  }

  /**
   * 瀑布流列表项组件
   */
  const renderFeedsItem = useCallback((item: ManicureContentItem.t, index) => {
    const { manicureFeeds } = item
    const imageModel = {
      width: string2number(manicureFeeds?.picWidth),
      height: string2number(manicureFeeds?.picHeight)
    }
    let oriW = imageModel?.width || 1
    const oriH = imageModel?.height || 0
    if (oriW === 0) {
      oriW = 1
    }
    let scrollW = oriH / oriW > 4 / 3 ? 4 / 3 : oriH / oriW
    if (oriW === 0 || oriH === 0) {
      scrollW = 4 / 3
    }
    const imgH = cardWidth * scrollW
    const salePrice = manicureFeeds?.dealInfo?.salePrice
    const integer = salePrice?.split('.')?.[0]
    const decimal = salePrice?.split('.')?.[1]
    const title = manicureFeeds?.dealInfo?.dealName
      ? `${manicureFeeds?.title}|${manicureFeeds?.dealInfo?.dealName}`
      : `${manicureFeeds?.title}`
    return (
      <TouchableOpacity
        key={`FeedsItem-${index}`}
        style={itemStyles.container}
        activeOpacity={1}
        onPress={() => {
          const jumpUrl = manicureFeeds?.jumpUrl || ''
          !!jumpUrl && openUrl(jumpUrl)
        }}
      >
        <View style={{ flexDirection: 'column' }}>
          <ImageBackground
            source={{
              uri: manicureFeeds?.picUrl
            }}
            style={[itemStyles.itemImgBg, { height: imgH }]}
          >
            <LinearGradient
              start={{ x: 0, y: 0 }}
              end={{ x: 0, y: 1 }}
              colors={['#00000000', '#00000030']}
              style={itemStyles.bgImgMask}
            />
            <View style={itemStyles.maskFooter}>
              <Image
                source={{
                  uri: 'https://p0.meituan.net/travelcube/f1b7fef0f12d2b2c39310b4d064e5d071263.png'
                }}
                style={itemStyles.shopIcon}
              />
              <Text numberOfLines={1} style={itemStyles.shopInfoText}>
                {manicureFeeds?.shopInfo?.shopShowName}
              </Text>
              {Number(manicureFeeds?.shopInfo?.score) > 0 && (
                <Text style={itemStyles.shopInfoText}>
                  {Number(manicureFeeds?.shopInfo?.score) / 10}分
                </Text>
              )}
              <Text style={[itemStyles.shopInfoText, { marginLeft: 6 }]}>
                {manicureFeeds?.shopInfo?.distance}
              </Text>
            </View>
          </ImageBackground>
          <View style={itemStyles.infoContainer}>
            <Text style={itemStyles.feedTitle} numberOfLines={2}>
              {title}
            </Text>
            <View style={itemStyles.shopTagsContainer}>
              {manicureFeeds?.styleLabel &&
                manicureFeeds?.styleLabel?.map(tagText => (
                  <View style={itemStyles.shopTag}>
                    <Text style={itemStyles.shopTagText}>{tagText}</Text>
                  </View>
                ))}
            </View>
            <View style={itemStyles.salesContainer}>
              <View style={itemStyles.priceInfo}>
                <View style={itemStyles.price}>
                  <Text style={itemStyles.currencyLogo}>¥</Text>
                  <Text style={itemStyles.salePrice}>{integer}</Text>
                  {!!decimal && (
                    <>
                      <Text style={itemStyles.decimal}>.</Text>
                      <Text style={itemStyles.decimal}>{decimal}</Text>
                    </>
                  )}
                </View>

                {!!manicureFeeds?.dealInfo?.discount && (
                  <View style={itemStyles.discount}>
                    <Text style={itemStyles.discountText}>{manicureFeeds?.dealInfo?.discount}</Text>
                  </View>
                )}
              </View>
              <Text numberOfLines={1} style={itemStyles.salesInfo}>
                {manicureFeeds?.dealInfo?.sales}
              </Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    )
  }, [])

  useEffect(() => {
    if (!location.cityId || !themeId) return
    const { cityId, lat, lng } = location
    setListParams({
      ...listParams.current,
      pageStart: 1,
      cityId,
      lat,
      lng,
      themeId,
      sortRule,
      dealTagId
    })
    fetchListData()
  }, [fetchListData, setListParams, location, themeId, sortRule, dealTagId])

  return (
    <MCWaterfallListModule
      loadingStatus={listState.loadingStatus}
      loadingMoreStatus={listState.loadingMoreStatus}
      isLoadingMoreCellHideBackground={true}
      isLoadingMoreFailCellHideBackground={true}
      data={listState.listData} // 数据源
      renderItem={renderFeedsItem} // 列表项渲染方法
      keyExtractor={(item, index) => `${item?.contentId}_${index}`} // 列表项唯一标识
      reuseIdentifierExtractor={'contentFeeds'} // 列表项复用标识
      gapTop={0}
      colCount={2}
      colGap={6}
      rowGap={6}
      footerView={
        listState.loadingMoreStatus === 'done' ? (
          <View style={listFooterStyles.container}>
            <Text style={listFooterStyles.footerText}>- 没有更多了 -</Text>
          </View>
        ) : null
      }
      onNeedLoadMore={() => {
        fetchMoreListData()
      }}
      onRetryForLoadingMoreFail={() => {
        fetchMoreListData()
      }}
      backgroundColor="#f4f4f4"
      paddingHorizontal={6}
      paddingVertical={9}
      onItemExpose={(itemData: ManicureContentItem.t) => {
        lxTrackMGEViewEvent('gc', 'b_gc_ympl5bdf_mv', 'c_gc_2ry3fm1x', {
          deal_id: itemData?.manicureFeeds?.dealInfo?.dealGroupId || '',
          item_id: itemData?.contentId || '',
          poi_id: itemData?.manicureFeeds?.shopInfo?.shopId || ''
        })
      }}
      onItemClick={(itemData: ManicureContentItem.t) => {
        lxTrackMGEClickEvent('gc', 'b_gc_ympl5bdf_mc', 'c_gc_2ry3fm1x', {
          deal_id: itemData?.manicureFeeds?.dealInfo?.dealGroupId || '',
          item_id: itemData?.contentId || '',
          poi_id: itemData?.manicureFeeds?.shopInfo?.shopId || ''
        })
      }}
      loadingView={
        <View style={listFooterStyles.container}>
          <Text style={listFooterStyles.footerText}>加载中...</Text>
        </View>
      }
      loadingMoreView={
        <View style={listFooterStyles.container}>
          <Text style={listFooterStyles.footerText}>加载中...</Text>
        </View>
      }
    />
  )
}
const itemStyles = StyleSheet.create({
  container: {
    borderRadius: 12,
    flexDirection: 'column',
    backgroundColor: '#fff',
    overflow: 'hidden'
  },
  itemImgBg: {
    borderTopLeftRadius: 5,
    borderTopRightRadius: 5,
    position: 'relative',
    overflow: 'hidden'
  },
  bgImgMask: {
    height: 50,
    width: '100%',
    position: 'absolute',
    bottom: 0
  },
  maskFooter: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    height: 24,
    paddingHorizontal: 9,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center'
  },
  shopIcon: {
    width: 12,
    height: 12
  },
  shopInfoText: {
    maxWidth: IS_WEB ? 78 : 88,
    fontWeight: '500',
    fontSize: 10,
    color: '#fff',
    marginLeft: 2
  },
  infoContainer: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    height: 'auto',
    padding: 9
  },
  feedTitle: {
    color: '#222222',
    fontFamily: 'PingFang SC',
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 16,
    letterSpacing: 0,
    textAlign: 'left'
  },
  shopTagsContainer: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
    height: 16,
    marginTop: 4,
    overflow: 'hidden'
  },
  shopTag: {
    height: 16,
    paddingHorizontal: 3.5,
    backgroundColor: '#F4F4F4',
    borderRadius: 3,
    marginRight: 3
  },
  shopTagText: {
    fontSize: 10,
    color: '#666666',
    lineHeight: 16
  },
  salesContainer: {
    width: '100%',
    height: 'auto',
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8
  },
  priceInfo: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center'
  },
  price: {
    marginBottom: 3,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-end'
  },
  currencyLogo: {
    fontSize: 12,
    color: '#FF4B10',
    fontFamily: 'MTfin2.0',
    lineHeight: 18
  },
  salePrice: {
    fontSize: 18,
    color: '#FF4B10',
    fontFamily: 'MTfin2.0',
    lineHeight: 22
  },
  decimal: {
    fontSize: 14,
    color: '#FF4B10',
    fontFamily: 'MTfin2.0',
    lineHeight: 19
  },
  discount: {
    height: 14,
    paddingHorizontal: 3.5,
    borderRadius: 3,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: '#FF4B1099',
    marginLeft: 4,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center'
  },
  discountText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FF4B10'
  },
  salesInfo: {
    fontSize: 12,
    color: '#999999'
  }
})

const listFooterStyles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    height: 45,
    backgroundColor: '#f4f4f4'
  },
  footerText: {
    fontWeight: '400',
    color: '#777777',
    fontFamily: 'PingFang SC',
    fontSize: 12,
    letterSpacing: 0,
    textAlign: 'center'
  }
})
