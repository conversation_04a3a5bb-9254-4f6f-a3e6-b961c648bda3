import React, { useState, useRef, useMemo } from 'react'
import { View } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { ThemeDealTagItem } from 'Models/ThemeDealTagItem'
import MixTabPopFilterCompoment from './components/MixTabPopFilterCompoment'
import { SORT_ITEM } from 'src/utils/config'
import { IS_WEB } from '@mrn/mrn-gc-utils'
import { PAGE_GAP_TOP_HEIGHT } from '../slogans/SlogansModule'

interface Props {
  tabs: ThemeDealTagItem.t[]
  themeTitle?: string
  selectedItem?: any
  gapFirstHoverModuleHeight?: number
  scrollToTop?: () => void
  onItemClick?: (sortId?: number, tagId?: string) => void
}

export const SecondFilterModule: React.FC<Props> = props => {
  const { tabs = [], gapFirstHoverModuleHeight, onItemClick } = props
  const [appear, setAppear] = useState(false)
  const [hover, setHover] = useState(false)
  const moduleRef = useRef(null)

  const subTabs = useMemo(() => {
    if (!tabs || tabs.length < 1) return [SORT_ITEM]
    return [SORT_ITEM, ...tabs]
  }, [tabs])

  const cprops = {
    appear,
    hover,
    onItemClick,
    ...props
  }

  return (
    <MCModule
      ref={moduleRef}
      hoverType={IS_WEB ? 'none' : 'autoHover'}
      hoverOffset={gapFirstHoverModuleHeight ?? PAGE_GAP_TOP_HEIGHT}
      separatorLineStyle={{
        lineColor: '#ffffff00'
      }}
      backgroundColor={'#ffffff00'}
      onHoverStatusChanged={event => {
        if (event && event.hoverStatus) {
          //获取置顶状态
          setHover(event.hoverStatus === 'hovering')
        }
      }}
      onAppear={() => {
        setAppear(true)
      }}
      onDisappear={() => {
        setAppear(false)
      }}
    >
      <View style={{ backgroundColor: '#F4F4F4' }}>
        <MixTabPopFilterCompoment subTabs={subTabs} {...cprops} />
      </View>
    </MCModule>
  )
}
