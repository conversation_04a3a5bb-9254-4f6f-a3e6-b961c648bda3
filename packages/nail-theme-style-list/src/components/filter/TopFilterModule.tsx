import React, { useState } from 'react'
import { View, Text, StyleSheet, Image } from '@mrn/react-native'
import { CommonBaseScrollTab } from './components/CommonBaseScrollTabComponent'
import { SceneDeatilThemeItem } from 'Models/SceneDeatilThemeItem'
import { MCModule } from '@nibfe/doraemon-practice'
import { LinearGradient } from '@mrn/react-native-linear-gradient'
import { lxTrackMGEClickEvent, lxTrackMGEViewEvent } from '@mrn/mrn-utils'
import { IS_WEB } from '@mrn/mrn-gc-utils'
import { PAGE_GAP_TOP_HEIGHT } from '../slogans/SlogansModule'

interface Props {
  tabs: SceneDeatilThemeItem.t[]
  selectedItem?: any
  onItemClick: (themeId: number, index: number) => void
}

export const FIRST_HOVER_MODULE_HEIGHT = 85

export const TopFilterModule: React.FC<Props> = ({ tabs, selectedItem, onItemClick }) => {
  const [appear, setAppear] = useState(false)
  const [hover, setHover] = useState(false)
  return (
    <MCModule
      hoverType={IS_WEB ? 'none' : 'autoHover'}
      hoverOffset={PAGE_GAP_TOP_HEIGHT}
      separatorLineStyle={{
        lineColor: '#ffffff00'
      }}
      backgroundColor={'#ffffff00'}
      onAppear={() => {
        setAppear(true)
      }}
      onDisappear={() => {
        setAppear(false)
      }}
      onHoverStatusChanged={event => {
        if (event && event.hoverStatus) {
          //获取置顶状态
          setHover(event.hoverStatus === 'hovering')
        }
      }}
    >
      <LinearGradient
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        colors={['#ffffff', '#f4f4f4']}
        style={[styles.linearGradient, { borderTopLeftRadius: hover ? 0 : 18 }]}
      >
        <CommonBaseScrollTab
          currentIndex={0}
          appear={appear}
          style={styles.wrapperStyle}
          tabs={tabs || []}
          renderTab={({ item, index, isFirst }) => {
            return (
              <View style={{ marginLeft: isFirst ? 12 : 0, marginRight: 6 }}>
                <CommonContainerItemComponent
                  tabValue={item}
                  isSelected={selectedItem?.themeIndex === index}
                />
              </View>
            )
          }}
          onExposeCallBack={({ item, index }) => {
            lxTrackMGEViewEvent('gc', 'b_gc_0k3bspkg_mv', 'c_gc_2ry3fm1x', {
              title: item?.title || '',
              index,
              abtest: '-9999',
              gender: '-9999',
              father_title: '-9999',
              area_name: '-9999'
            })
          }}
          onSelected={({ item, index }) => {
            lxTrackMGEClickEvent('gc', 'b_gc_0k3bspkg_mc', 'c_gc_2ry3fm1x', {
              title: item?.title || '',
              index,
              abtest: '-9999',
              gender: '-9999',
              father_title: '-9999',
              area_name: '-9999'
            })
            if (selectedItem?.themeIndex !== index) {
              onItemClick(item?.themeId, index)
            }
          }}
        />
      </LinearGradient>
    </MCModule>
  )
}

const CommonContainerItemComponent: React.FC<{
  tabValue: SceneDeatilThemeItem.t
  isSelected: boolean
}> = ({ tabValue, isSelected }) => {
  return (
    <View style={styles.itemStyle}>
      {isSelected && (
        <Image
          source={{
            uri: 'https://p1.meituan.net/travelcube/48641f737642e6431a28b22443a0cfff2524.png'
          }}
          style={{
            width: 83,
            height: 65,
            position: 'absolute',
            zIndex: 100
          }}
        />
      )}
      <View style={isSelected ? styles.selectViewStyle : styles.unSelectViewStyle}>
        <Image
          source={{
            uri: tabValue?.picUrl || ''
          }}
          resizeMode="stretch"
          style={{
            width: '100%',
            height: '100%',
            borderRadius: 8
          }}
        />
        <LinearGradient
          start={{ x: 0, y: 0.6 }}
          end={{ x: 0, y: 1 }}
          colors={['#00000000', '#00000081']}
          style={styles.linearView}
        >
          <Text
            numberOfLines={1}
            style={
              isSelected
                ? [styles.selectTitle, { bottom: tabValue?.subTitle ? 18 : 9 }]
                : [styles.unSelectTitle, { bottom: tabValue?.subTitle ? 12 : 3 }]
            }
          >
            {tabValue?.title}
          </Text>
          {!!tabValue?.subTitle && (
            <Text
              numberOfLines={1}
              style={isSelected ? styles.selectSubTitle : styles.unSelectSubTitle}
            >
              {tabValue.subTitle}
            </Text>
          )}
        </LinearGradient>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  linearGradient: {
    borderTopLeftRadius: 18,
    backgroundColor: '#ffffff',
    overflow: 'hidden'
  },
  wrapperStyle: {
    height: FIRST_HOVER_MODULE_HEIGHT,
    paddingTop: 14,
    backgroundColor: '#ffffff00'
  },
  itemStyle: {
    height: 65,
    flexDirection: 'row',
    justifyContent: 'center'
  },
  selectViewStyle: {
    width: 83,
    height: 65,
    borderRadius: 8,
    overflow: 'hidden'
  },
  unSelectViewStyle: {
    width: 74.5,
    height: 55,
    marginTop: 3,
    borderRadius: 8,
    overflow: 'hidden'
  },
  selectTitle: {
    position: 'absolute',
    bottom: 10,
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500'
  },
  selectSubTitle: {
    position: 'absolute',
    bottom: 6.5,
    fontSize: 10,
    color: '#FFFFFF',
    fontWeight: 'normal'
  },
  unSelectTitle: {
    position: 'absolute',
    bottom: 4,
    fontSize: 11,
    color: '#FFFFFF',
    fontWeight: '400'
  },
  unSelectSubTitle: {
    position: 'absolute',
    bottom: 2,
    fontSize: 9,
    color: '#FFFFFF',
    fontWeight: 'normal'
  },
  linearView: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    alignItems: 'center'
  }
})
