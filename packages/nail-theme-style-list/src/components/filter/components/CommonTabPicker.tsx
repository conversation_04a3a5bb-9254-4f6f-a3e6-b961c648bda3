import React from 'react'

import { Text, TouchableOpacity, View, StyleSheet, StyleProp, ViewStyle } from '@mrn/react-native'
import { WithTheme, WithThemeStyles } from '@ss/mtd-react-native/lib/common/styles/theme'
import {
  Icon,
  ModalProps,
  pickerStyles,
  PickerStyles,
  SlideModal,
  SlideModalProps
} from '@ss/mtd-react-native'

export interface PickerToogleParams extends PickerState {
  active?: boolean
}

export interface PickerProps extends WithThemeStyles<PickerStyles> {
  /** 隐藏按钮 */
  isHideLabel?: boolean
  /** 按钮名称 */
  label?: string
  /** 自定义按钮 Icon */
  renderIcon?: (data: { active?: boolean; size?: number; tintColor?: string }) => JSX.Element
  /** 自定义按钮 */
  renderLabel?: (active: boolean) => JSX.Element
  /** 是否禁止 */
  disabled?: boolean
  /** 控制最外层的包裹 View 的样式 */
  style?: StyleProp<ViewStyle>
  /** 点击按钮时的回调 */
  toggle?: (data: PickerToogleParams) => void
  /** 打开的内容，是否需要在弹窗中展示 */
  needModal?: boolean
  /** 控制按钮 icon 大小 */
  iconSize?: number
  /** 按钮被点击后的颜色 */
  activeColor?: string
  /** 按钮默认的颜色 */
  defaultColor?: string
  /** 控制 Modal 弹窗的属性，仅在 needModal 为 true 时生效 */
  modalProps?: ModalProps
  /** 控制 SlideModal 弹窗的属性，仅在 needModal 为 true 时生效  */
  slideModalProps?: SlideModalProps
  /** 支持自动化测试 */
  testID?: string
  fixedOffset?: boolean
  offsetY?: number
  onPickerThis?: (refThis) => void
  onClose?: () => void
  onPressClose?: () => void
}

interface PickerState {
  visible: boolean
  active?: boolean
  offsetY?: number
}

export class CommonTabPicker extends React.Component<PickerProps, PickerState> {
  private trigger = null
  static defaultProps: PickerProps = {
    label: '请选择',
    disabled: false,
    renderLabel: null,
    activeColor: null,
    defaultColor: null,
    needModal: true,
    style: {},
    iconSize: 16,
    toggle: null,
    modalProps: {},
    slideModalProps: {}
  }
  private isUnmounted = false
  constructor(props: PickerProps) {
    super(props)

    this.state = {
      visible: false,
      active: false
    }
  }

  componentDidMount() {
    this.props.onPickerThis && this.props.onPickerThis(this)
  }

  toggle = () => {
    const { disabled, toggle, needModal } = this.props
    if (disabled) {
      return
    }

    this.setState(
      state => {
        return {
          active: !state.active
        }
      },
      () => {
        let { active } = this.state
        toggle &&
          toggle({
            active,
            ...this.state
          })

        if (needModal) {
          active ? this.open() : this.close()
        }
      }
    )
  }

  close() {
    return this.setState({
      visible: false
    })
  }

  open() {
    this.trigger.measure((fx, fy, width, height, px, py) => {
      this.setState({
        offsetY: py + height
      })

      this.setState({
        visible: true
      })
    })
  }

  renderIcon(active: boolean, size: number, tintColor: string, styles: PickerStyles) {
    if (active) {
      return <Icon type="expand-more" size={size} tintColor={tintColor} style={styles.iconImage} />
    }

    return <Icon type="expand-less" size={size} tintColor={tintColor} style={styles.iconImage} />
  }

  componentWillUnmount() {
    this.isUnmounted = true
  }

  render() {
    return (
      <WithTheme compName="Picker" themeStyles={pickerStyles} styles={this.props.styles}>
        {styles => {
          let {
            style,
            disabled,
            label,
            renderLabel,
            needModal,
            iconSize,
            modalProps,
            activeColor,
            defaultColor,
            renderIcon,
            slideModalProps,
            testID
          } = this.props

          const { active, offsetY } = this.state

          let fontColor = active
            ? activeColor || StyleSheet.flatten(styles.buttonActiveText).color
            : defaultColor || StyleSheet.flatten(styles.buttonText).color

          let activeStyle = active ? [styles.buttonActiveText, { color: fontColor }] : null

          return (
            <View
              testID={testID}
              ref={c => {
                this.trigger = c
              }}
              collapsable={false}
              style={[styles.wrapper, style]}
            >
              {this.props.isHideLabel ? null : (
                <TouchableOpacity
                  onPress={() => {
                    this.toggle()
                  }}
                  activeOpacity={1}
                  style={styles.container}
                >
                  {renderLabel ? (
                    renderLabel(active)
                  ) : (
                    <View
                      style={[styles.buttonWrapper, disabled ? styles.buttonWrapperDisable : {}]}
                    >
                      <Text style={[styles.buttonText, activeStyle]}>{label}</Text>
                      {renderIcon
                        ? renderIcon({
                            active,
                            size: iconSize,
                            tintColor: fontColor
                          })
                        : this.renderIcon(active, iconSize, fontColor, styles)}
                    </View>
                  )}
                </TouchableOpacity>
              )}
              {needModal ? (
                <SlideModal
                  visible={this.state.visible}
                  modalProps={{
                    ...modalProps,
                    onClose: () => {
                      this.props.onClose && this.props.onClose()
                      if (this.state.active) {
                        this.setState({
                          active: false
                        })
                      }
                    },
                    onPressClose: () => {
                      this.props.onPressClose && this.props.onPressClose()
                      this.close()
                    }
                  }}
                  direction="down"
                  offsetX={0}
                  offsetY={this.props.fixedOffset ? this.props.offsetY || 0 : offsetY}
                  {...slideModalProps}
                >
                  <View style={styles.modalPicker}>{this.props.children}</View>
                </SlideModal>
              ) : (
                (this.props.children as JSX.Element)
              )}
            </View>
          )
        }}
      </WithTheme>
    )
  }
}
