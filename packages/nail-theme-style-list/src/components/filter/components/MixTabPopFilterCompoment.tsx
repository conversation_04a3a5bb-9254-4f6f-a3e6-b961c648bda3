import React, { useRef, useEffect, useState } from 'react'
import { View, Text, Image, StyleSheet } from '@mrn/react-native'
import { lxTrackMGEViewEvent, lxTrackMGEClickEvent } from '@mrn/mrn-utils'
import { CommonTabPicker } from './CommonTabPicker'
import { CommonSelectView } from '@mrn/mrn-gc-mcTabFilter'
import { CommonBaseScrollTab } from './CommonBaseScrollTabComponent'
import { SortType, SubFilterItem } from 'src/types'
import { IS_WEB } from '@mrn/mrn-gc-utils'
import { PAGE_GAP_TOP_HEIGHT, SLOGAN_MODULE_HEIGHT } from '../../slogans/SlogansModule'

interface Props {
  subTabs: any[]
  appear?: boolean
  hover?: boolean
  themeTitle?: string
  gapFirstHoverModuleHeight?: number
  selectedItem?: any
  onItemClick?: (sortId?: number, tagId?: string) => void
  scrollToTop?: () => void
}

const SECOND_HOVER_MODULE_HEIGHT = 42

const MixTabPopFilterComponent: React.FC<Props> = props => {
  const {
    subTabs,
    appear,
    selectedItem,
    gapFirstHoverModuleHeight: defaultGapFirstHoverModuleHeight,
    hover,
    scrollToTop,
    onItemClick
  } = props
  const gapFirstHoverModuleHeight = defaultGapFirstHoverModuleHeight || PAGE_GAP_TOP_HEIGHT
  const mrnPickerHoverOffset = hover
    ? gapFirstHoverModuleHeight + SECOND_HOVER_MODULE_HEIGHT
    : SLOGAN_MODULE_HEIGHT + gapFirstHoverModuleHeight + SECOND_HOVER_MODULE_HEIGHT
  // 当前H5不支持悬浮，且没有透明导航栏，所以要减去导航栏高度
  const webPickerHoverOffset =
    gapFirstHoverModuleHeight + SECOND_HOVER_MODULE_HEIGHT - PAGE_GAP_TOP_HEIGHT
  const pickerHover = IS_WEB ? webPickerHoverOffset : mrnPickerHoverOffset

  const [sortSelectIndex, setSortSelectIndex] = useState<number>(-1)
  const [iconSelect, setIconSelect] = useState<boolean>(false)

  //弹窗ref
  const tabPopRef = useRef(null)
  const tabRef = useRef(null)

  /**
   * 打开/关闭 弹窗
   */
  const popToggle = () => {
    setIconSelect(preState => !preState)
    tabPopRef.current && tabPopRef.current?.close()
    tabPopRef.current && tabPopRef.current?.toggle()
  }

  /**
   * 关闭弹窗
   */
  const popClose = () => {
    setIconSelect(false)
    tabPopRef.current && tabPopRef.current?.close()
  }

  // 更新下拉筛选选中态
  const setSortSelectStateChange = (index: number) => {
    setSortSelectIndex(index)
  }

  const renderSubTabItem = (item, index) => {
    if (!item) {
      return null
    }
    if (item?.filterType === 'sort') {
      return renderPopTabItem(item, index)
    } else {
      return renderCommonTab(item, index)
    }
  }

  const renderCommonTab = (item, index) => {
    const isSelected = selectedItem?.dealTagId === item?.dealTagId
    return (
      <View
        style={[
          {
            borderRadius: 6,
            height: 30,
            marginLeft: index === 0 ? 12 : 0,
            marginRight: 6,
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
            borderWidth: 0.5
          },
          isSelected ? styles.selectedView : styles.unselectedView
        ]}
      >
        {item.dealTagName ? (
          <Text
            style={[
              {
                fontSize: 12,
                marginHorizontal: 9,
                fontWeight: 'normal'
              },
              isSelected ? styles.selectedText : styles.unselectedText
            ]}
          >
            {item.dealTagName}
          </Text>
        ) : null}
      </View>
    )
  }

  /*
   * 绘制弹出的tab样式
   * @param item
   * @param index
   * @returns
   */
  const renderPopTabItem = (item, index) => {
    if (!item) {
      return null
    }
    const isSelected = sortSelectIndex > -1
    let iconUrl = ''
    if (iconSelect) {
      iconUrl = 'https://p0.meituan.net/travelcube/470ce4195d25e1bd786c1a14a071db221330.png'
    } else if (isSelected) {
      iconUrl = 'https://p0.meituan.net/ingee/e30fc847306286ddb8d6e9026f7003d01715.png'
    } else {
      iconUrl = 'https://p0.meituan.net/ingee/6c29186bb25a9a935d3a621e3be5db1e936.png'
    }
    return (
      <View
        style={[
          {
            borderRadius: 6,
            height: 30,
            marginLeft: index === 0 ? 12 : 0,
            marginRight: 6,
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'row',
            borderWidth: 0.5,
            borderColor: '#ffffff'
          },
          isSelected || iconSelect ? styles.selectedView : styles.unselectedView
        ]}
      >
        <Text
          style={[
            {
              fontSize: 12,
              marginLeft: 9,
              fontWeight: 'normal'
            },
            isSelected || iconSelect ? styles.selectedText : styles.unselectedText
          ]}
        >
          {isSelected
            ? item?.subFilterItems?.[sortSelectIndex]?.filterName || ''
            : item?.filterName}
        </Text>
        <Image
          source={{ uri: iconUrl }}
          style={{
            marginLeft: 3,
            marginRight: 9,
            width: 12,
            height: 12
          }}
        />
        {getPopPicker(item)}
      </View>
    )
  }

  const getPopPicker = (tabFilterItem: SortType) => {
    const subFilterItems = (tabFilterItem?.subFilterItems as SubFilterItem[]) || []
    if (!subFilterItems || subFilterItems.length <= 0) {
      return null
    }
    const parmProp: any = {
      selectList: subFilterItems,
      selectedItem: sortSelectIndex > -1 ? [sortSelectIndex] : [0],
      onChange: (item, levelPath) => {
        setSortSelectStateChange(levelPath?.[0])
        onItemClick && onItemClick(item.filterId, selectedItem?.dealTagId)
        popClose()
      }
    }

    return (
      <CommonTabPicker
        style={{
          width: 0,
          height: 0,
          opacity: 0
        }}
        slideModalProps={{
          wrapperStyles: [
            {
              borderBottomLeftRadius: 12,
              borderBottomRightRadius: 12,
              overflow: 'hidden'
            },
            styles.pickerStyle
          ]
        }}
        disabled={false}
        fixedOffset={true}
        offsetY={pickerHover}
        onPickerThis={ref => (tabPopRef.current = ref)}
      >
        <CommonSelectView {...parmProp} />
      </CommonTabPicker>
    )
  }

  useEffect(() => {
    if (selectedItem?.sortRule == null && !selectedItem?.dealTagId) {
      popClose()
      setSortSelectIndex(-1)
      tabRef.current && tabRef.current.onSrollToLeft()
    }
  }, [selectedItem])

  return (
    <CommonBaseScrollTab
      ref={tabRef}
      style={{
        height: SECOND_HOVER_MODULE_HEIGHT,
        paddingTop: 6,
        paddingBottom: 6
      }}
      tabs={subTabs}
      appear={appear}
      contentContainerStyle={styles.contentContainerStyle}
      renderTab={({ item, index }) => renderSubTabItem(item, index)}
      onExposeCallBack={({ item, index }) => {
        lxTrackMGEViewEvent('gc', 'b_gc_rbhuaw9u_mv', 'c_gc_2ry3fm1x', {
          title: item?.filterName || item?.dealTagName || '',
          tab_name: item?.filterName || item?.dealTagName || '',
          tab_index: index,
          index,
          abtest: '-9999',
          gender: '-9999',
          father_title: props.themeTitle || '',
          area_name: '-9999'
        })
      }}
      onSelected={({ item, index }) => {
        scrollToTop && scrollToTop()
        lxTrackMGEClickEvent('gc', 'b_gc_rbhuaw9u_mc', 'c_gc_2ry3fm1x', {
          title: item?.filterName || item?.dealTagName || '',
          tab_name: item?.filterName || item?.dealTagName || '',
          tab_index: index,
          index,
          abtest: '-9999',
          gender: '-9999',
          father_title: props.themeTitle || '',
          area_name: '-9999'
        })
        if (item?.filterType === 'sort') {
          popToggle()
        } else {
          popClose()
          let dealTagId = item?.dealTagId
          if (dealTagId === selectedItem?.dealTagId) {
            dealTagId = ''
          }
          onItemClick && onItemClick(selectedItem?.sortRule, dealTagId)
        }
      }}
    />
  )
}

export default MixTabPopFilterComponent

const styles = StyleSheet.create({
  wrapperViewStyle: {},
  contentContainerStyle: {},
  selectedView: {
    backgroundColor: '#FFF1EC',
    borderColor: '#FF4B10'
  },
  unselectedView: {
    backgroundColor: '#FFFFFF',
    borderColor: '#FFFFFF'
  },
  selectedText: {
    fontSize: 12,
    fontWeight: 'normal',
    color: '#FF4B10'
  },
  unselectedText: {
    fontSize: 12,
    fontWeight: 'normal',
    color: '#222222'
  },
  pickerStyle: {}
})
