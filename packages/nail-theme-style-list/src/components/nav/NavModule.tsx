import React from 'react'
import Icon from '@max/leez-icon'
import { View, Text, Animated, TouchableOpacity, StyleSheet } from '@mrn/react-native'
import { getRecommendHeight, getRecommendPaddingTop } from '@nibfe/dm-navigation'
import { pageRouterClose, lxTrackMGEClickEvent } from '@mrn/mrn-utils'
import KNB from '@mrn/mrn-knb'
import { IS_WEB } from '@mrn/mrn-gc-utils'
import { isMT } from '../../utils/platform'

interface Props {
  scrollY: Animated.Value
  title: string
  shareUrl?: string
  titleColor?: string
}

export const NavModule: React.FC<Props> = React.memo(props => {
  const { scrollY, title, shareUrl, titleColor } = props
  const back = () => {
    pageRouterClose()
  }
  const share = () => {
    lxTrackMGEClickEvent('gc', 'b_gc_55l3280l_mc', 'c_gc_2ry3fm1x', {})
    KNB.share({
      title: `上${isMT ? '美团' : '大众点评'}找“美甲款式”`,
      url: shareUrl || '',
      desc: '精选热款下单即可到店还原！',
      image: '',
      channel: []
    })
  }

  // 下层按钮
  const BottomButtons = () => {
    return (
      <View style={styles.row}>
        <TouchableOpacity activeOpacity={1} onPress={back} style={styles.back}>
          <Icon name="fanhui" type="title1" color="white" size={24} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: titleColor || '#fff' }]}>{title}</Text>
        <TouchableOpacity activeOpacity={1} onPress={share} style={styles.share}>
          <Icon name="fenxiang" type="title1" color="white" size={20} />
        </TouchableOpacity>
      </View>
    )
  }
  // 上层按钮
  const TopButtons = () => {
    return (
      <Animated.View
        style={[
          styles.topButton,
          {
            opacity: scrollY.interpolate({
              inputRange: [0, 50],
              outputRange: [0, 1]
            })
          }
        ]}
      >
        <View style={styles.row}>
          <TouchableOpacity activeOpacity={1} onPress={back} style={styles.back}>
            <Icon name="fanhui" type="title1" color="#3A2817" size={24} />
          </TouchableOpacity>
          <Text style={[styles.title, { color: '#3A2817' }]}>{title}</Text>
          <TouchableOpacity activeOpacity={1} onPress={share} style={styles.share}>
            <Icon name="fenxiang" type="title1" color="#3A2817" size={20} />
          </TouchableOpacity>
        </View>
      </Animated.View>
    )
  }

  if (IS_WEB) return null
  return (
    <View style={styles.wrap}>
      <BottomButtons />
      <TopButtons />
    </View>
  )
})

const styles = StyleSheet.create({
  wrap: {
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 2
  },
  row: {
    position: 'relative',
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 15,
    marginTop: getRecommendPaddingTop(),
    height: getRecommendHeight() - getRecommendPaddingTop()
  },
  back: {
    position: 'absolute',
    left: 0
  },
  backIcon: {
    width: 29,
    height: 29
  },
  title: {
    marginLeft: 35,
    fontFamily: 'Meituan Type',
    fontWeight: '700',
    fontSize: 19
  },
  share: {
    position: 'absolute',
    right: 0
  },
  topButton: {
    height: getRecommendHeight(),
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF'
  }
})
