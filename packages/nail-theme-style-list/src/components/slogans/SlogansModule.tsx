import React from 'react'
import { View, Text, StyleSheet, Image } from '@mrn/react-native'
import { MCModule } from '@nibfe/doraemon-practice'
import { getRecommendHeight } from '@nibfe/dm-navigation'
import { IS_WEB } from '@mrn/mrn-gc-utils'

export const SLOGAN_MODULE_HEIGHT = 45
export const PAGE_GAP_TOP_HEIGHT = IS_WEB ? 15 : getRecommendHeight()
const styles = StyleSheet.create({
  container: {
    height: SLOGAN_MODULE_HEIGHT,
    paddingLeft: 40,
    zIndex: 10
  },
  mainText: {
    marginTop: 1,
    fontSize: 12,
    fontWeight: '600',
    lineHeight: 14,
    letterSpacing: 0
  },
  supplyText: {
    marginTop: 5,
    fontSize: 12,
    lineHeight: 14,
    fontWeight: '400',
    letterSpacing: 0
  }
})

type Props = {
  slogan?: string
  subSlogan?: string
  sloganColor?: string
  subSloganColor?: string
}
export const SloganModule: React.FC<Props> = ({
  slogan,
  subSlogan,
  sloganColor,
  subSloganColor
}) => {
  return (
    <>
      <MCModule
        backgroundColor="transparent"
        separatorLineStyle={{ display: 'hidden-all' }}
        gapTop={IS_WEB ? 15 : PAGE_GAP_TOP_HEIGHT}
      >
        <View style={styles.container}>
          <Text numberOfLines={1} style={[styles.mainText, { color: sloganColor || '#fff' }]}>
            {slogan}
          </Text>
          {/* 副宣传语 */}
          <Text numberOfLines={1} style={[styles.supplyText, { color: subSloganColor || '#fff' }]}>
            {subSlogan}
          </Text>
          <Image
            source={{
              uri: 'https://p0.meituan.net/travelcube/3267ab93c718eb56dc9a6b573f330535945.png'
            }}
            style={{
              width: 18,
              height: 18,
              position: 'absolute',
              right: 0,
              bottom: 0
            }}
          />
        </View>
      </MCModule>
    </>
  )
}
