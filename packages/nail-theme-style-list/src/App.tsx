/* eslint-disable react-native/no-inline-styles */
import React, { FC, useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { View, Image, ImageBackground, Dimensions, Animated } from '@mrn/react-native'
import { AnimatedMCPage, MCPageRef } from '@nibfe/doraemon-practice'
import { NavModule } from 'src/components/nav/NavModule'
import LX from '@analytics/mrn-sdk'
import {
  Provider as ThemeProvider,
  APPDP_THEME_GCUI,
  APPMT_THEME_GCUI
} from '@nibfe/theme-provider-lighter'
import { MTDProvider } from '@ss/mtd-react-native'
import TextCompatUtils from './utils/TextCompatUtils'
import { ScreenProps } from './types'
import { isMT } from './utils/platform'
import useLocation from './hooks/useLocation'
import { MRN_GET_Mapi_nibmp_mcp_gateway_content_feeds_manicure_scenedetail } from 'APIs/MRN_GET_Mapi_nibmp_mcp_gateway_content_feeds_manicure_scenedetail'
import { SceneDetailResp } from 'Models/SceneDetailResp'
import { SceneDeatilThemeItem } from 'Models/SceneDeatilThemeItem'
import { FIRST_HOVER_MODULE_HEIGHT, TopFilterModule } from './components/filter/TopFilterModule'
import { SecondFilterModule } from 'components/filter/SecondFilterModule'
import {
  PAGE_GAP_TOP_HEIGHT,
  SLOGAN_MODULE_HEIGHT,
  SloganModule
} from 'components/slogans/SlogansModule'
import { WaterListModule } from 'components/list/WaterListModule'
import { IS_WEB } from '@mrn/mrn-gc-utils'

// 模块入口处执行一次全局配置兼容方法
TextCompatUtils.fixTextCompat() // 修复部分Android机型文本截断问题、iOS默认字体

interface Props {
  screenProps: ScreenProps
}

export interface themeDataState {
  title: string
  shareUrl: string
  themeList: SceneDeatilThemeItem.t[]
  titleColor?: string
}

const SCREEN_WIDTH = Dimensions.get('window').width

const NailThemePage: FC<Props> = ({ screenProps }) => {
  const { sceneId, platform } = screenProps
  const [bgRatio, setBgRatio] = useState(0)
  const mcPageRef = useRef<MCPageRef>(null)
  const [themeData, setThemeData] = useState<themeDataState>({
    title: '',
    shareUrl: '',
    themeList: [],
    titleColor: ''
  })

  const isFirstModuleShow = useMemo(() => {
    return themeData.themeList && themeData.themeList.length >= 3
  }, [themeData.themeList])

  const [selectedTheme, setSelectedTheme] = useState({
    themeId: null,
    themeIndex: null
  })
  const [selectedTab, setSelectedTab] = useState({
    sortRule: null,
    dealTagId: ''
  })

  const scrollY = useRef(new Animated.Value(0)).current

  // 当前选中主题
  const currentTheme = useMemo(() => {
    return themeData.themeList?.[selectedTheme?.themeIndex]
  }, [themeData, selectedTheme])

  const handleThemeChange = useCallback((themeId, index) => {
    setSelectedTheme({
      themeId,
      themeIndex: index
    })
    setSelectedTab({
      sortRule: null,
      dealTagId: ''
    })
  }, [])

  const handleTagChange = useCallback(
    (sortId?: number, tagId?: string) => {
      setSelectedTab({
        ...selectedTab,
        sortRule: sortId,
        dealTagId: tagId
      })
    },
    [selectedTab]
  )

  const scrollToHoverPosition = useCallback(() => {
    mcPageRef.current &&
      mcPageRef.current.scrollToPosition({
        position: SLOGAN_MODULE_HEIGHT + PAGE_GAP_TOP_HEIGHT,
        animated: true
      })
  }, [])

  const location = useLocation()
  useEffect(() => {
    if (!location.cityId || !sceneId) return
    MRN_GET_Mapi_nibmp_mcp_gateway_content_feeds_manicure_scenedetail({
      cityId: location.cityId ? Number(location.cityId) : 0,
      lng: location.lng,
      lat: location.lat,
      sceneId,
      platform,
      h5Flag: IS_WEB ? 1 : 0
    })
      .then((res: SceneDetailResp.t) => {
        if (res?.commonResp?.code === 200 && res?.themeList?.length) {
          setThemeData({
            title: res?.title,
            shareUrl: res?.shareUrl,
            themeList: res?.themeList,
            titleColor: res?.titleColor
          })
          const selectIndex = res?.themeList?.findIndex?.(item => item?.sceneId == sceneId)
          const themeIndex = selectIndex > -1 ? selectIndex : 0
          setSelectedTheme({
            themeIndex,
            themeId: res?.themeList?.[themeIndex]?.themeId
          })
        }
      })
      .catch(_ => {})
  }, [location, sceneId, platform])

  useEffect(() => {
    if (IS_WEB) {
      document.title = themeData.title
    }
  }, [themeData.title])

  useEffect(() => {
    if (currentTheme?.background) {
      Image.getSize(
        currentTheme.background,
        (width, height) => {
          setBgRatio(height / width)
        },
        () => {
          setBgRatio(0)
        }
      )
    }
  }, [currentTheme])

  return (
    <View style={{ flex: 1 }}>
      <NavModule
        scrollY={scrollY}
        title={themeData.title}
        shareUrl={themeData.shareUrl}
        titleColor={themeData.titleColor}
      />
      <AnimatedMCPage
        ref={mcPageRef}
        contentBackgroundColor="#F4F4F4"
        pageGap={0}
        pageTopGap={0}
        pageBottomGap={0}
        paddingHorizontal={0}
        showScrollIndicator={false}
        separatorLineStyle={{
          display: 'hidden-all'
        }}
        onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
          useNativeDriver: true
        })}
        scrollEventThrottle={1}
        contentBackgroundView={
          <ImageBackground
            source={{
              uri: currentTheme?.background || ''
            }}
            style={{
              height: bgRatio * SCREEN_WIDTH,
              width: SCREEN_WIDTH
            }}
            resizeMode="contain"
          />
        }
        mptInfo={{
          category: 'gc',
          cid: 'c_gc_2ry3fm1x',
          labs: {
            cat_id: 75,
            custom: {
              title: themeData?.title || '-9999',
              type: '-9999'
            }
          }
        }}
        onDisappear={() => {
          LX.pageDisappear({
            category: 'gc',
            pageInfoKey: 'nail-theme-style-list'
          })
        }}
        modules={[
          {
            moduleKey: 'slogan_module',
            module: (
              <SloganModule
                slogan={currentTheme?.slogan}
                subSlogan={currentTheme?.subSlogan}
                sloganColor={currentTheme?.sloganColor}
                subSloganColor={currentTheme?.subSloganColor}
              />
            )
          },
          {
            moduleKey: 'filter_module1',
            module: isFirstModuleShow ? (
              <TopFilterModule
                tabs={themeData.themeList}
                selectedItem={selectedTheme}
                onItemClick={handleThemeChange}
              />
            ) : null
          },
          {
            moduleKey: 'filter_module2',
            module: (
              <SecondFilterModule
                themeTitle={currentTheme?.title}
                tabs={currentTheme?.dealTagList}
                selectedItem={selectedTab}
                onItemClick={handleTagChange}
                scrollToTop={scrollToHoverPosition}
                gapFirstHoverModuleHeight={
                  isFirstModuleShow
                    ? PAGE_GAP_TOP_HEIGHT + FIRST_HOVER_MODULE_HEIGHT
                    : PAGE_GAP_TOP_HEIGHT
                }
              />
            )
          },
          {
            moduleKey: 'list_Module',
            module: (
              <WaterListModule
                location={location}
                platform={platform}
                themeId={currentTheme?.themeId}
                sortRule={selectedTab?.sortRule}
                dealTagId={selectedTab?.dealTagId}
              />
            )
          }
        ]}
      />
    </View>
  )
}

export default (props: ScreenProps) => {
  return (
    <ThemeProvider theme={isMT ? APPMT_THEME_GCUI : APPDP_THEME_GCUI}>
      <MTDProvider>
        <NailThemePage
          screenProps={{
            ...props,
            platform: Number(props.platform) || (isMT ? 2 : 1),
            cityId: props?.cityId || 0
          }}
        />
      </MTDProvider>
    </ThemeProvider>
  )
}
