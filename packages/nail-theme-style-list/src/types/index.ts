export interface ScreenProps {
  // mrn公共参数
  mrn_component: string

  // 可选参数
  platform?: number // 平台，1-点评，2-美团
  cityId?: number
  lng?: number
  lat?: number
  sceneId?: number // 场景id
}

export interface RequestParams {
  unionId: string
  contentId: string //作品id
  cityId: number
  platform: number //平台，1是点评，2是美团
  lng: number
  lat: number
  contentType: number ////300-笔记,400评价
  shopId: string //商户id
  inApp: number
  offset: number
  limit: number
  nodeTagIds: string //笔记的筛选项,是筛选项查出来的ids。 查询全部的话，就传0
  cursor: string
}

export type SubFilterItem = {
  filterName: string
  filterId: number
}

export type SortType = {
  filterId: number
  filterType: string
  filterName: string
  subFilterItems: SubFilterItem[]
}
