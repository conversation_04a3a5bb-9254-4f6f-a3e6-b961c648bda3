/**
 * @apiName Mapi_nibmp_mcp_gateway_content_feeds_manicure_scenedetail
 * @apiType GET
 * @apiUrl https://mapi.dianping.com/mapi/nibmp/mcp/gateway/content/feeds/manicure/scenedetail
 * @bridgeType mrn
 */
import { mapi } from '@mrn/mrn-utils'
import { SceneDetailResp } from '../Models/SceneDetailResp'

interface RequestParams {
  sceneId: number // 场景ID
  lng: number // 经纬度信息
  lat: number // 经纬度信息
  platform: number // 平台，1-点评，2-美团
  cityId: number // 城市ID
  h5Flag: number // 是否是H5
}

interface MapiOptions {
  // 签名，可选，默认true
  signature?: boolean
  // 防刷单，可选，默认false
  fabricate?: boolean
  // 是否支持幂等，可选，POST默认false，GET默认true
  failOver?: boolean
  // 缓存设置，可选，只有get支持，默认不缓存（0：不缓存，其他：缓存）
  cacheType?: number
  // 请求头
  headers?: {
    // 默认请求头
    picasso?: 'no-js'
    'mrn-version'?: ''
    'mrn-project'?: ''
  }
  // 设置压缩
  compress?: boolean
}

// 查询场景展示信息
export function MRN_GET_Mapi_nibmp_mcp_gateway_content_feeds_manicure_scenedetail(
  params: RequestParams,
  mapiOptions?: MapiOptions,
): Promise<SceneDetailResp.t> {
  return mapi({
    url: 'https://mapi.dianping.com/mapi/nibmp/mcp/gateway/content/feeds/manicure/scenedetail',
    method: 'GET',
    params,
    ...mapiOptions,
  })
  .catch((e: any) => {
    e.userInfo = {
      ...e.userInfo,
      requestInfo: {
        url: 'https://mapi.dianping.com/mapi/nibmp/mcp/gateway/content/feeds/manicure/scenedetail',
      },
    }
    throw e
  })
}
