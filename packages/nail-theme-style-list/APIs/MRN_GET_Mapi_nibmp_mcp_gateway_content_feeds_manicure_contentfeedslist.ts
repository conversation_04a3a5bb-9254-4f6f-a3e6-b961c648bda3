/**
 * @apiName Mapi_nibmp_mcp_gateway_content_feeds_manicure_contentfeedslist
 * @apiType GET
 * @apiUrl https://mapi.dianping.com/mapi/nibmp/mcp/gateway/content/feeds/manicure/contentfeedslist
 * @bridgeType mrn
 */
import { mapi } from '@mrn/mrn-utils'
import { ManicureContentResp } from '../Models/ManicureContentResp'

interface RequestParams {
  pageSize: number // 页大小
  pageStart: number // 页偏移量
  dealTagId?: string // 款式标签ID
  sortRule: number // 排序规则，0-智能排序，1-离我最近，2-评价最高，3-人气优先，4-价格最低，5-价格最高
  themeId: number // 主题ID
  platform: number // 平台，1-点评，2-美团
  cityId: number // 城市ID
  lng: number // 经纬度信息
  lat: number // 经纬度信息
  h5Flag: number // 是否是H5
}

interface MapiOptions {
  // 签名，可选，默认true
  signature?: boolean
  // 防刷单，可选，默认false
  fabricate?: boolean
  // 是否支持幂等，可选，POST默认false，GET默认true
  failOver?: boolean
  // 缓存设置，可选，只有get支持，默认不缓存（0：不缓存，其他：缓存）
  cacheType?: number
  // 请求头
  headers?: {
    // 默认请求头
    picasso?: 'no-js'
    'mrn-version'?: ''
    'mrn-project'?: ''
  }
  // 设置压缩
  compress?: boolean
}

// 款式卡片feeds流
export function MRN_GET_Mapi_nibmp_mcp_gateway_content_feeds_manicure_contentfeedslist(
  params: RequestParams,
  mapiOptions?: MapiOptions,
): Promise<ManicureContentResp.t> {
  return mapi({
    url: 'https://mapi.dianping.com/mapi/nibmp/mcp/gateway/content/feeds/manicure/contentfeedslist',
    method: 'GET',
    params,
    ...mapiOptions,
  })
  .catch((e: any) => {
    e.userInfo = {
      ...e.userInfo,
      requestInfo: {
        url: 'https://mapi.dianping.com/mapi/nibmp/mcp/gateway/content/feeds/manicure/contentfeedslist',
      },
    }
    throw e
  })
}
