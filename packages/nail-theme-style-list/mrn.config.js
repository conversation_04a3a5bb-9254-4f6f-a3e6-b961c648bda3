// mrn.config.js 配置文档
let iconfont
try {
  iconfont = require('@max/leez-icon/font').fonts
} catch (e) {}
module.exports = {
  name: 'mrn-beauty-nail-theme-style-list',
  main: './index.tsx', // 页面入口地址
  biz: 'gcbu',
  bundleType: 1,
  bundleDependencies: ['@mrn/mrn-base'],
  debugger: {
    //
    moduleName: 'nail-theme-style-list',
    initialProperties: {
      hideNavigationBar: true,
      sceneId: 16
    }
  },
  // 转 H5 配置
  one: {
    appConfig: {
      pages: [
        {
          name: 'nail-theme-style-list',
          path: 'index.tsx',
          enableShareAppMessage: true
        }
      ]
    }
  },
  fonts: {
    'Meituan Type': './src/assets/MeituanType-Bold.ttf',
    ...iconfont
  }
}
