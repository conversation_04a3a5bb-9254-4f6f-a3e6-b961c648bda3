{"name": "nail-theme-style-list", "version": "1.0.0", "private": true, "description": "A-MRN-PROJECT", "author": "", "scripts": {"start:web": "mc-one start -p web", "build:web": "mc-one build -p web", "build": "mrn build", "lint": "eslint --ext .ts,.tsx,.js,.jsx ./src", "fix": "yarn lint --fix", "start": "mrn start", "api": "mc-api"}, "dependencies": {"@analytics/mrn-sdk": "^1.2.0", "@dp/midas2.0": "^1.3.10", "@max/leez-dependencies": "^2.3.35", "@max/max": "^1.0.1", "@max/mrn-transform-rpx": "^1.0.8", "@mrn/mcc-component-report": "0.0.22", "@mrn/mrn-base": "^3.0.38", "@mrn/mrn-gc-mcTabFilter": "0.0.15", "@mrn/mrn-knb": "^0.4.5", "@mrn/mrn-utils": "^1.5.0", "@mrn/react-navigation": "^2.9.23", "@nibfe/doraemon-practice": "1.3.13-web.1", "@nibfe/mc-one-env": "^1.0.3", "@nibfe/mrn-babel-preset": "^3.0.27-beta.3", "@nibfe/react-native-safe-area-view": "^1.0.2", "babel-runtime-jsx-plus": "^0.1.5", "sass": "^1.64.1"}, "devDependencies": {"@gfe/babel-plugin-react-add-name": "1.0.17", "@mrn/gravel-react-native": "^0.2.4", "@mrn/mrn-cli": "^3.0.3", "@mrn/mrn-gc-pflcomponent": "^0.0.281-test06", "@mrn/mrn-gc-utils": "^0.0.6", "@nibfe/dm-navigation": "^2.0.4", "@nibfe/eslint-config": "^1.0.17", "@nibfe/gc-ui": "^3.2.12", "@nibfe/mc-one-all-deps": "1.2.20", "@nibfe/prettier-config": "^1.0.0", "@nibfe/theme-provider-lighter": "^1.2.3", "@ss/mtd-react-native": "^0.4.8", "@typescript-eslint/eslint-plugin": "^2.22.0", "@typescript-eslint/parser": "^3.1.0", "babel-eslint": "^10.0.0", "cz-customizable": "^7.0.0", "eslint-config-airbnb": "^18.0.1", "eslint-config-prettier": "^6.10.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "^3.1.2", "eslint-plugin-react": "^7.19.0", "eslint-plugin-react-native": "^3.8.1", "lint-staged": "^10.0.8"}, "eslintConfig": {"root": true, "extends": ["plugin:@mrn/eslint-plugin/recommended"]}, "prettier": {"$schema": "http://json.schemastore.org/prettierrc", "printWidth": 100, "tabWidth": 2, "useTabs": false, "semi": false, "singleQuote": true, "jsxSingleQuote": false, "trailingComma": "none", "bracketSpacing": true, "jsxBracketSameLine": false, "arrowParens": "avoid"}, "eslintIgnore": ["dist", "node_modules"], "husky": {"hooks": {"pre-commit": "yarn lint", "post-merge": "yarn"}}, "resolutions": {"@types/react": "^17.0.0"}, "engines": {"node": "<17"}}